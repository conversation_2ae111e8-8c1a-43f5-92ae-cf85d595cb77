const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Define component transformations
const transformations = {
  // Modal → Dialog
  modal: {
    imports: {
      from: "import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';",
      to: "import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';"
    },
    properties: {
      from: /modalRef: BsModalRef;/g,
      to: "dialogRef: DynamicDialogRef;"
    },
    constructor: {
      from: /private\s+modalService:\s+BsModalService/g,
      to: "private dialogService: DialogService"
    },
    methods: {
      from: /this\.modalRef\s*=\s*this\.modalService\.show\(([^,]+),\s*(?:<ModalOptions>)?({[^}]+})\);/g,
      to: (match, component, options) => {
        // Extract class if present
        const classMatch = options.match(/class:\s*['"]([^'"]+)['"]/);
        const width = classMatch && classMatch[1].includes('xl') ? '90%' : 
                     classMatch && classMatch[1].includes('lg') ? '80%' : 
                     classMatch && classMatch[1].includes('sm') ? '50%' : '70%';
        
        return `this.dialogRef = this.dialogService.open(${component}, {
          width: '${width}',
          data: {}
        });`;
      }
    },
    contentAccess: {
      from: /this\.modalRef\.content\.([a-zA-Z0-9_]+)\s*=\s*([^;]+);/g,
      to: (match, prop, value) => `this.dialogRef.data = { ...this.dialogRef.data, ${prop}: ${value} };`
    }
  },
  
  // DatePicker → Calendar
  datepicker: {
    html: {
      from: /<input[^>]*bsDatepicker[^>]*>/g,
      to: (match) => {
        const ngModelMatch = match.match(/\[\(ngModel\)\]="([^"]+)"/);
        const ngModel = ngModelMatch ? ngModelMatch[1] : '';
        const placeholderMatch = match.match(/placeholder="([^"]+)"/);
        const placeholder = placeholderMatch ? placeholderMatch[1] : '';
        const nameMatch = match.match(/name="([^"]+)"/);
        const name = nameMatch ? nameMatch[1] : '';
        
        return `<p-calendar [(ngModel)]="${ngModel}" dateFormat="yy-mm-dd" 
                [showIcon]="true" inputStyleClass="form-control"
                placeholder="${placeholder}" name="${name}"></p-calendar>`;
      }
    }
  },
  
  // Typeahead → AutoComplete
  typeahead: {
    html: {
      from: /<input[^>]*\[typeahead\][^>]*>/g,
      to: (match) => {
        const ngModelMatch = match.match(/\[\(ngModel\)\]="([^"]+)"/);
        const ngModel = ngModelMatch ? ngModelMatch[1] : '';
        
        const typeaheadMatch = match.match(/\[typeahead\]="([^"]+)"/);
        const suggestions = typeaheadMatch ? typeaheadMatch[1] : '';
        
        const loadingMatch = match.match(/\(typeaheadLoading\)="([^"]+)"/);
        const completeMethod = loadingMatch ? loadingMatch[1] : '';
        
        const selectMatch = match.match(/\(typeaheadOnSelect\)="([^"]+)"/);
        const onSelect = selectMatch ? selectMatch[1] : '';
        
        const fieldMatch = match.match(/typeaheadOptionField="([^"]+)"/);
        const field = fieldMatch ? fieldMatch[1] : '';
        
        const placeholderMatch = match.match(/placeholder="([^"]+)"/);
        const placeholder = placeholderMatch ? placeholderMatch[1] : '';
        
        const nameMatch = match.match(/name="([^"]+)"/);
        const name = nameMatch ? nameMatch[1] : '';
        
        return `<p-autoComplete [(ngModel)]="${ngModel}"
                [suggestions]="${suggestions}"
                (completeMethod)="${completeMethod}($event)" (onSelect)="${onSelect}"
                field="${field}" placeholder="${placeholder}"
                inputStyleClass="form-control"
                name="${name}"
                [forceSelection]="true" [dropdownAppendTo]="'body'"></p-autoComplete>`;
      }
    }
  },
  
  // Pagination → Paginator
  pagination: {
    html: {
      from: /<pagination[^>]*>[^<]*<\/pagination>/g,
      to: (match) => {
        const totalItemsMatch = match.match(/\[totalItems\]="([^"]+)"/);
        const totalItems = totalItemsMatch ? totalItemsMatch[1] : '';
        
        const ngModelMatch = match.match(/\[\(ngModel\)\]="([^"]+)"/);
        const page = ngModelMatch ? ngModelMatch[1] : '';
        
        const maxSizeMatch = match.match(/\[maxSize\]="([^"]+)"/);
        const maxSize = maxSizeMatch ? maxSizeMatch[1] : '5';
        
        const pageChangedMatch = match.match(/\(pageChanged\)="([^"]+)"/);
        const pageChanged = pageChangedMatch ? pageChangedMatch[1] : '';
        
        return `<p-paginator (onPageChange)="${pageChanged}" 
                [first]="(${page}-1) * itemsPerPage" [rows]="itemsPerPage" [totalRecords]="${totalItems}" 
                [rowsPerPageOptions]="[10, 20, 30]"
                [showFirstLastIcon]="true" [pageLinkSize]="${maxSize}"
                [showCurrentPageReport]="true" currentPageReportTemplate="Showing {first}-{last} of {totalRecords}">
                </p-paginator>`;
      }
    }
  },
  
  // Timepicker → Calendar with Time
  timepicker: {
    html: {
      from: /<timepicker[^>]*>[^<]*<\/timepicker>/g,
      to: (match) => {
        const ngModelMatch = match.match(/\[\(ngModel\)\]="([^"]+)"/);
        const ngModel = ngModelMatch ? ngModelMatch[1] : '';
        
        return `<p-calendar [(ngModel)]="${ngModel}" [timeOnly]="true" [showTime]="true"></p-calendar>`;
      }
    }
  },
  
  // Dropdown → Dropdown
  dropdown: {
    html: {
      from: /<div[^>]*dropdown[^>]*>[\s\S]*?<\/div>/g,
      to: (match) => {
        // This is a simplified transformation - actual implementation would need to extract options
        return `<p-dropdown [options]="options" [(ngModel)]="selectedOption" placeholder="Select an option"></p-dropdown>`;
      }
    }
  },
  
  // Confirmation popover → ConfirmDialog
  confirmationPopover: {
    html: {
      from: /\(confirm\)="([^"]+)" mwlConfirmationPopover/g,
      to: (match, confirmMethod) => `(click)="confirmAction('${confirmMethod}')"`
    },
    methods: {
      add: `
  confirmAction(methodName: string): void {
    this.confirmationService.confirm({
      message: 'Are you sure you want to proceed?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this[methodName]();
      }
    });
  }`
    }
  }
};

// Process TypeScript files
function processTypeScriptFiles(directory) {
  const tsFiles = glob.sync(`${directory}/**/*.ts`);
  
  tsFiles.forEach(file => {
    let content = fs.readFileSync(file, 'utf8');
    let modified = false;
    
    // Process imports
    Object.keys(transformations).forEach(component => {
      if (transformations[component].imports) {
        if (content.includes(transformations[component].imports.from)) {
          content = content.replace(
            transformations[component].imports.from, 
            transformations[component].imports.to
          );
          modified = true;
        }
      }
      
      // Process properties
      if (transformations[component].properties) {
        if (transformations[component].properties.from.test(content)) {
          content = content.replace(
            transformations[component].properties.from, 
            transformations[component].properties.to
          );
          modified = true;
        }
      }
      
      // Process constructor
      if (transformations[component].constructor) {
        if (transformations[component].constructor.from.test(content)) {
          content = content.replace(
            transformations[component].constructor.from, 
            transformations[component].constructor.to
          );
          modified = true;
        }
      }
      
      // Process methods
      if (transformations[component].methods) {
        if (typeof transformations[component].methods.from === 'object') {
          if (transformations[component].methods.from.test(content)) {
            content = content.replace(
              transformations[component].methods.from, 
              transformations[component].methods.to
            );
            modified = true;
          }
        } else if (transformations[component].methods.add) {
          // Add new method before the last closing brace
          if (!content.includes(transformations[component].methods.add)) {
            content = content.replace(
              /}(\s*)$/,
              `${transformations[component].methods.add}\n}$1`
            );
            modified = true;
          }
        }
      }
      
      // Process content access
      if (transformations[component].contentAccess) {
        if (transformations[component].contentAccess.from.test(content)) {
          content = content.replace(
            transformations[component].contentAccess.from, 
            transformations[component].contentAccess.to
          );
          modified = true;
        }
      }
    });
    
    if (modified) {
      fs.writeFileSync(file, content);
      console.log(`Updated: ${file}`);
    }
  });
}

// Process HTML files
function processHTMLFiles(directory) {
  const htmlFiles = glob.sync(`${directory}/**/*.html`);
  
  htmlFiles.forEach(file => {
    let content = fs.readFileSync(file, 'utf8');
    let modified = false;
    
    // Process HTML transformations
    Object.keys(transformations).forEach(component => {
      if (transformations[component].html) {
        if (transformations[component].html.from.test(content)) {
          content = content.replace(
            transformations[component].html.from, 
            transformations[component].html.to
          );
          modified = true;
        }
      }
    });
    
    if (modified) {
      fs.writeFileSync(file, content);
      console.log(`Updated: ${file}`);
    }
  });
}

// Main execution
const sourceDir = './servicePal-ui/src';
console.log('Starting migration...');
processTypeScriptFiles(sourceDir);
processHTMLFiles(sourceDir);
console.log('Migration completed!');