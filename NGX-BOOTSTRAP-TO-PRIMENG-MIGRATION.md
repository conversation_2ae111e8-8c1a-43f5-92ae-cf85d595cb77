
# Migration Guide: ngx-bootstrap to PrimeNG

This document outlines the migration from ngx-bootstrap to PrimeNG components in the ServicePal application.

## Overview

We're migrating from ngx-bootstrap to PrimeNG to:

- Improve component consistency and theming.
- Better Angular integration.
- Enhance accessibility features.
- Access a more comprehensive component library.

## Component Migration Map

### 1. Modal → Dialog

**Before (ngx-bootstrap):**

```typescript
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ModalOptions } from 'ngx-bootstrap/modal';

modalRef: BsModalRef;
constructor(private modalService: BsModalService) {}

showInvoice() {
  this.modalRef = this.modalService.show(InvoiceComponent, <ModalOptions>{class: 'modal-xl'});
  this.modalRef.content.jobNo = this.selectedJob.jobNo;
  this.modalRef.content.findInvoiceByJobId();
}
```

**After (PrimeNG):**

```typescript
import { DialogService, DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

dialogRef: DynamicDialogRef;
constructor(private dialogService: DialogService) {}

showInvoice() {
  this.dialogRef = this.dialogService.open(InvoiceComponent, {
    header: 'Invoice Details',
    width: '90%',
    data: {
      jobNo: this.selectedJob.jobNo
    }
  });
}
```

**Inside InvoiceComponent:**

```typescript
constructor(public ref: DynamicDialogRef, public config: DynamicDialogConfig) {}
ngOnInit() {
  if (this.config.data && this.config.data.jobNo) {
    this.jobNo = this.config.data.jobNo;
    this.findInvoiceByJobId();
  }
}
```

**Important Considerations:** Data is passed via `data`, not directly on `content`. Initialization happens inside the child component.

### 2. DatePicker → Calendar

**Before (ngx-bootstrap):**

```html
<input type="text" name="dueDate" id="dueDate"
       [(ngModel)]="si.dueDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
       class="form-control" placeholder="Due Date">
```

**After (PrimeNG):**

```html
<p-calendar [(ngModel)]="si.dueDate" dateFormat="yy-mm-dd" 
            [showIcon]="true" inputStyleClass="form-control"
            placeholder="Due Date"></p-calendar>
```

### 3. Typeahead → AutoComplete

**Before (ngx-bootstrap):**

```html
<input [(ngModel)]="keyServiceSearch"
       [typeahead]="serviceSearchList"
       (typeaheadLoading)="searchService()"
       (typeaheadOnSelect)="setSelectedService($event)"
       [typeaheadOptionsLimit]="7"
       typeaheadOptionField="barcode"
       placeholder="Search By Service Name"
       autocomplete="off"
       size="16"
       class="form-control" name="searchService">
```

**After (PrimeNG):**

```html
<p-autoComplete [(ngModel)]="keyServiceSearch"
                [suggestions]="serviceSearchList"
                (completeMethod)="searchService($event)" (onSelect)="setSelectedService($event)"
                field="barcode" placeholder="Search By Service Name"
                inputStyleClass="form-control"
                [size]="16"
                [forceSelection]="true" [dropdownAppendTo]="'body'" [scrollHeight]="'200px'" ></p-autoComplete>
```

**Important Considerations:**
- Use `field` for label display.
- Use `(completeMethod)` with `$event.query`.

### 4. Pagination → Paginator

**Before (ngx-bootstrap):**

```html
<pagination class="pagination-sm justify-content-center"
            [totalItems]="collectionSize"
            [(ngModel)]="page" [maxSize]="15" [boundaryLinks]="true"
            (pageChanged)="pageChanged($event)">
</pagination>
```

**After (PrimeNG):**

```html
<p-paginator (onPageChange)="pageChanged($event)" 
             [first]="firstRecordIndex" [rows]="itemsPerPage" [totalRecords]="collectionSize" 
             [rowsPerPageOptions]="[10, 20, 30]"
             [showFirstLastIcon]="true" [pageLinkSize]="15"
             [showCurrentPageReport]="true" currentPageReportTemplate="Showing {first}-{last} of {totalRecords}">
</p-paginator>
```

### 5. Timepicker → Calendar with Time

**Before (ngx-bootstrap):**

```html
<timepicker [(ngModel)]="time"></timepicker>
```

**After (PrimeNG):**

```html
<p-calendar [(ngModel)]="time" [timeOnly]="true" [showTime]="true"></p-calendar>
```

### 6. Dropdown → Dropdown

**Before (ngx-bootstrap):**

```html
<div class="btn-group" dropdown>
  <button class="btn btn-primary dropdown-toggle">
    Actions <span class="caret"></span>
  </button>
  <ul class="dropdown-menu">
    <li><a href="#">Action 1</a></li>
  </ul>
</div>
```

**After (PrimeNG):**

```html
<p-dropdown [options]="options" [(ngModel)]="selectedOption" placeholder="Select an option"></p-dropdown>
```

## Migration Steps Completed

- ✅ Dependencies Updated: Added PrimeNG, removed ngx-bootstrap.
- ✅ Components Migrated: Calendar, AutoComplete, DialogService.
- 🔄 In Progress: Modals, Pagination, Dropdown.

## Next Steps

```bash
npm install primeng@17.18.11 primeicons@7.0.0
npm uninstall ngx-bootstrap
```

Update remaining components and test thoroughly.

## Common Issues and Solutions

### 1. Date Format

Use correct `dateFormat` like `yy-mm-dd`.

### 2. Modal Data Passing

```typescript
// Parent
this.dialogService.open(YourComponent, {
  data: { key: 'value' }
});

// Child
constructor(public ref: DynamicDialogRef, public config: DynamicDialogConfig) {
  const receivedData = this.config.data;
}
```

### 3. Styling Compatibility

```css
.p-calendar .p-inputtext,
.p-autocomplete .p-inputtext,
.p-dropdown .p-inputtext {
  border-radius: .25rem;
  border: 1px solid #ced4da;
  padding: .375rem .75rem;
  font-size: 1rem;
  line-height: 1.5;
}
.p-paginator {
  display: flex;
  justify-content: center;
  padding: .5rem 0;
}
.p-paginator .p-paginator-pages .p-paginator-page {
  min-width: 28px;
  height: 28px;
  line-height: 28px;
  font-size: .875rem;
}
```

## Benefits After Migration

- 🎨 Consistent Theming
- ♿ Better Accessibility
- ⚙️ Enhanced Features
- 🧩 Angular Integration
- 🔧 Simplified Maintenance

## Resources

- [PrimeNG Documentation](https://primefaces.org/primeng)
- [PrimeNG Showcase](https://primefaces.org/primeng/showcase)
