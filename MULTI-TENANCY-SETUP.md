# ServicePal Multi-Tenancy Setup

This document describes the multi-tenancy architecture implemented for ServicePal, allowing one Java backend service to support multiple frontend deployments (one per customer).

## Architecture Overview

### Backend (Java Service)
- **Single Service Instance**: One Java Spring Boot application serves all tenants
- **Tenant Resolution**: Automatic tenant identification via request origin/subdomain
- **Database Isolation**: Each tenant has a separate MongoDB database
- **JWT Integration**: Tenant information embedded in JWT tokens

### Frontend (Angular)
- **Single Codebase**: Same Angular application for all clients
- **Separate Deployments**: Each client gets their own deployment of the same application
- **Backend Handles Tenancy**: All multi-tenancy logic is handled by the backend service

## Backend Configuration

### Key Components

1. **TenantContext**: Thread-local storage for tenant information
2. **TenantResolver**: Resolves tenant from request headers/subdomain
3. **TenantInterceptor**: Sets tenant context for each request
4. **TenantDatabaseService**: Manages tenant-specific MongoDB connections
5. **TenantAwareService**: Base service class for tenant-aware operations

### Database Setup

Each tenant gets a separate MongoDB database:
- `servicePalAmb` - for AMB tenant
- `servicePalTissa` - for Tissa tenant
- `servicePalMaharagama` - for Maharagama tenant

### Tenant Resolution

The system resolves tenants in the following order:
1. `X-Tenant-ID` header
2. Subdomain extraction (e.g., `amb.viganana.com` → `amb`)
3. Origin header parsing
4. Default tenant fallback

### Using Tenant-Aware Database Operations

To use tenant-specific database operations in your services:

```java
@Service
public class YourService extends TenantAwareService {

    public void saveData(YourEntity entity) {
        // This will automatically use the correct tenant database
        getMongoTemplate().save(entity);
    }

    public List<YourEntity> findAll() {
        // This will query the current tenant's database
        return getMongoTemplate().findAll(YourEntity.class);
    }
}
```

Or inject the TenantDatabaseService directly:

```java
@Service
public class YourService {

    @Autowired
    private TenantDatabaseService tenantDatabaseService;

    public void saveData(YourEntity entity) {
        MongoTemplate mongoTemplate = tenantDatabaseService.getCurrentTenantMongoTemplate();
        mongoTemplate.save(entity);
    }
}
```

## Frontend Configuration

### Single Application Approach

The frontend remains a single Angular application with no tenant-specific changes:

- **Same Codebase**: All clients use the exact same Angular application
- **No Environment Changes**: Standard environment configuration
- **Backend Routing**: All tenant logic handled by the backend service

### Standard Build Process

```bash
# Standard Angular build
npm run build

# Standard development server
npm start
```

## Deployment

### Backend Deployment
1. Deploy single Java service with multi-tenant configuration
2. Ensure MongoDB databases exist for each tenant
3. Configure CORS to allow all tenant subdomains

### Frontend Deployment
Each client gets their own deployment of the same Angular application:

1. **Build the application:**
   ```bash
   npm run build
   ```

2. **Deploy to client-specific domains:**
   - `dist/servicepal/` → `amb.viganana.com`
   - `dist/servicepal/` → `tissa.viganana.com`
   - `dist/servicepal/` → `maharagama.viganana.com`

3. **Each deployment:**
   - Uses the exact same Angular build
   - Connects to the same backend service
   - Backend automatically handles tenant separation based on domain
   - No frontend changes needed for new clients

## Adding New Tenants

### Backend
1. No code changes required - tenant resolution is automatic
2. Create new MongoDB database: `servicePal{tenantName}`

### Frontend
1. Create new environment file: `environment.{tenant}.ts`
2. Add build configuration in `angular.json`
3. Add npm scripts in `package.json`
4. Update deployment script

### Example: Adding "NewTenant"

1. Create `environment.newtenant.ts`:
```typescript
export const environment = {
  production: true,
  tenantId: 'newtenant',
  apiUrl: 'https://service.viganana.com/servicepal-service/',
  tenantName: 'New Tenant Motors',
  primaryColor: '#dc2626',
  logoUrl: '/assets/logos/newtenant-logo.png'
};
```

2. Add to `angular.json` configurations
3. Add npm scripts: `build:newtenant`, `serve:newtenant`
4. Deploy to `newtenant.viganana.com`

## Security Considerations

- Each tenant's data is completely isolated in separate databases
- JWT tokens include tenant information for additional security
- CORS configured to allow only authorized domains
- Tenant resolution prevents cross-tenant data access

## Monitoring and Logging

- Tenant information included in all log entries
- MongoDB logs separated by database
- Application metrics can be filtered by tenant

## Benefits

1. **Cost Effective**: Single backend service for all tenants
2. **Scalable**: Easy to add new tenants without code changes
3. **Isolated**: Complete data separation between tenants
4. **Customizable**: Tenant-specific branding and configuration
5. **Maintainable**: Single codebase with tenant-specific deployments
