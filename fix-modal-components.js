const fs = require('fs');
const path = require('path');

// Components that are typically opened as modals and need DynamicDialog injection
const modalComponents = [
  'complete-job.component.ts',
  'job-estimate.component.ts', 
  'advance-payment.component.ts',
  'create-si.component.ts',
  'create-pi.component.ts',
  'invoice.component.ts',
  'new-job.component.ts',
  'new-machine.component.ts',
  'new-agreement.component.ts',
  'employee.component.ts',
  'create-item.component.ts',
  'expense.component.ts'
];

function fixModalComponent(content, fileName) {
  let modified = false;
  
  // Check if component already has DynamicDialog imports
  if (content.includes('DynamicDialogRef') && content.includes('DynamicDialogConfig')) {
    return { content, modified: false };
  }
  
  // Add imports if missing
  if (!content.includes('DynamicDialogRef') || !content.includes('DynamicDialogConfig')) {
    const importPattern = /import\s*{[^}]*}\s*from\s*['"]@angular\/core['"];/;
    const match = content.match(importPattern);
    
    if (match) {
      const newImport = `import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';\n`;
      content = content.replace(match[0], match[0] + '\n' + newImport);
      modified = true;
    }
  }
  
  // Find constructor and add DynamicDialog parameters if not present
  const constructorPattern = /constructor\s*\(\s*([^)]*)\s*\)\s*{/;
  const constructorMatch = content.match(constructorPattern);
  
  if (constructorMatch && !constructorMatch[1].includes('DynamicDialogRef')) {
    const existingParams = constructorMatch[1].trim();
    let newParams = existingParams;
    
    if (existingParams) {
      newParams += ',\n    ';
    }
    newParams += 'public ref: DynamicDialogRef,\n    public config: DynamicDialogConfig';
    
    const newConstructor = `constructor(\n    ${newParams}\n  ) {`;
    content = content.replace(constructorPattern, newConstructor);
    modified = true;
  }
  
  // Add ngOnInit method to handle data from config if not present
  if (!content.includes('ngOnInit') && content.includes('DynamicDialogConfig')) {
    const classPattern = /export\s+class\s+\w+.*?{/;
    const classMatch = content.match(classPattern);
    
    if (classMatch) {
      const ngOnInitMethod = `
  ngOnInit() {
    // Handle data passed from parent dialog
    if (this.config.data) {
      // Extract data from config.data as needed
      // Example: this.jobNo = this.config.data.jobNo;
    }
  }
`;
      
      const insertIndex = content.indexOf('{', classMatch.index) + 1;
      content = content.slice(0, insertIndex) + ngOnInitMethod + content.slice(insertIndex);
      modified = true;
    }
  }
  
  // Replace modalRef.hide() calls with ref.close()
  if (content.includes('modalRef.hide()')) {
    content = content.replace(/this\.modalRef\.hide\(\)/g, 'this.ref.close()');
    modified = true;
  }
  
  // Replace modalRef.content access patterns
  const modalRefContentPattern = /this\.modalRef\.content\.(\w+)/g;
  if (modalRefContentPattern.test(content)) {
    content = content.replace(modalRefContentPattern, (match, property) => {
      return `// TODO: Handle ${property} via config.data or component properties`;
    });
    modified = true;
  }
  
  return { content, modified };
}

function processFile(filePath) {
  try {
    const fileName = path.basename(filePath);
    
    // Check if this is a modal component
    if (!modalComponents.includes(fileName)) {
      return;
    }
    
    const originalContent = fs.readFileSync(filePath, 'utf8');
    const { content, modified } = fixModalComponent(originalContent, fileName);
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Updated modal component: ${filePath}`);
    }
    
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

function findTypeScriptFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && item.endsWith('.component.ts')) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Main execution
console.log('🔧 Fixing modal components for PrimeNG DynamicDialog...');

const srcDir = path.join(__dirname, 'servicePal-ui', 'src');
const tsFiles = findTypeScriptFiles(srcDir);

console.log(`Checking ${tsFiles.length} TypeScript component files for modal components...`);

tsFiles.forEach(processFile);

console.log('✅ Modal component fixes completed!');
