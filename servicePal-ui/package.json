{"name": "servicepal", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "@fortawesome/fontawesome-free": "^6.5.2", "angular-confirmation-popover": "^7.0.0", "bootstrap": "^5.3.3", "jsbarcode": "^3.11.6", "ngx-barcode6": "^1.0.25", "ngx-chips": "^3.0.0", "ngx-print": "^1.2.2", "ngx-toastr": "^17.0.0", "primeng": "^19.0.0", "primeicons": "^6.0.1", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.4", "@angular/cli": "^19.0.4", "@angular/compiler-cli": "^19.0.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.4.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.6.2"}}