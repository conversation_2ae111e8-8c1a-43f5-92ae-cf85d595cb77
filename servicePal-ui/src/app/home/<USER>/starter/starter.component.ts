import { Component, OnInit } from '@angular/core';
import {PermissionService} from '../service/permission.service';
import {Company} from '../model/company';

@Component({
  standalone: false,
  selector: 'app-starter',
  templateUrl: './starter.component.html',
  styleUrls: ['./starter.component.css']
})
export class StarterComponent implements OnInit {

  today: number = Date.now();
  user: any;
  company: Company;
  permissions: Array<any> = [];

  constructor(private permissionService: PermissionService) {
    this.user = JSON.parse(localStorage.getItem('currentUser')).user;
    this.company = new Company();
    this.company.name = 'S-out Catalyst';
    this.findUserPermissions();
  }

  ngOnInit() {
  }

  findUserPermissions() {
    this.permissionService.findDesktopPermissions(this.user.username).subscribe((result: Array<any>) => {
      this.permissions = result;
    });
  }

}
