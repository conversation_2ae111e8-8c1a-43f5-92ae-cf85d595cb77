import {Role} from './role';
import {Permission} from './permission';
import {Counter} from "./counter";

export class User {
  id: string;
  token: string;
  active: boolean;
  firstName: string;
  lastName: string;
  username: string;
  counter: string;
  userRoles: Array<Role>;
  permissions: Array<Permission>;
  desktopPermissions: Array<Permission>;
  email: string;
  password: string;
  warehouseCode: number;
}
