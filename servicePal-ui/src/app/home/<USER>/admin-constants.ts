import {environment} from '../../../environments/environment';

export class ApiConstants {

  public static API_URL = environment.apiUrl;

  public static SAVE_USER = ApiConstants.API_URL + 'user/save';
  public static GET_USERS = ApiConstants.API_URL + 'user/findAll';
  public static DISABLE_USER = ApiConstants.API_URL + 'user/delete';
  public static SEARCH_USER = ApiConstants.API_URL + 'user/search';
  public static USER_CHECK = ApiConstants.API_URL + 'user/checkForUserName';
  public static UPDATE_USER = ApiConstants.API_URL + 'user/updateDesktopPerm';
  public static SEARCH_BY_NAME = ApiConstants.API_URL + 'user/searchByName';
  public static SETUP_COUNTER = ApiConstants.API_URL + 'user/setupCounter';

  public static GET_ROLES = ApiConstants.API_URL + 'role/findAll';
  public static SAVE_ROLE = ApiConstants.API_URL + 'role/save';
  public static DELETE_ROLE = ApiConstants.API_URL + 'role/delete';
  public static SEARCH_ROLE = ApiConstants.API_URL + 'role/search';

  public static FIND_ENABLED_PERMISSIONS = ApiConstants.API_URL + 'user/findEnabledPermission';
  public static FIND_DESKTOP_PERMISSIONS = ApiConstants.API_URL + 'user/findDesktopPermissions';
  public static FIND_PERMISSION_BY_MODULE = ApiConstants.API_URL + 'user/findPermissionByModule';
  public static SAVE_DESKTOP_PERMS = ApiConstants.API_URL + 'user/saveDesktopPerms';
  public static GET_ENABLED_MODULES = ApiConstants.API_URL + 'user/getEnabledModules';
  public static GET_PERMISSION = ApiConstants.API_URL + 'user/getPermission';
  public static SEARCH_USER_BY_USERNAME = ApiConstants.API_URL + ' user/findByUsername';

  public static GET_PRINT_SETTING = ApiConstants.API_URL + 'printSetting/findAll';
  public static SAVE_PRINT_SETTING = ApiConstants.API_URL + 'printSetting/save';

  public static FIND_ALL_COUNTERS = ApiConstants.API_URL + 'counter/findAll';
  public static SEARCH_COUNTER_BY_NUMBER = ApiConstants.API_URL + 'counter/searchByNameLike';


}
