import {environment} from '../../../../environments/environment';

export class BusinessConstants {

  public static API_URL = environment.apiUrl;

  public static SAVE_JOB = BusinessConstants.API_URL + 'job/save';
  public static FIND_JOB_BY_ID = BusinessConstants.API_URL + 'job/findById';
  public static FIND_JOB_BY_NO = BusinessConstants.API_URL + 'job/searchByJobNo';
  public static FIND_JOB_BY_WO_NO = BusinessConstants.API_URL + 'job/searchByWoNo';
  public static FIND_JOB_WO_NO = BusinessConstants.API_URL + 'job/searchWoNo';
  public static FIND_JOBS_BY_SERIAL_NO = BusinessConstants.API_URL + 'job/searchBySerialNo';
  public static FIND_JOBS_BY_CUSTOMER_ID = BusinessConstants.API_URL + 'job/searchByCustomerId';
  public static FIND_JOBS_BY_CUSTOMER_NIC = BusinessConstants.API_URL + 'job/searchByCustomerNic';
  public static FIND_JOB_BY_STATUS = BusinessConstants.API_URL + 'job/findByJobStatus';
  public static FIND_ALL_JOBS = BusinessConstants.API_URL + 'job/findAll';
  public static SAVE_JOB_ESTIMATE = BusinessConstants.API_URL + 'job/saveEstimate';
  public static APPROVE_JOB_ESTIMATE = BusinessConstants.API_URL + 'job/approveEstimate';
  public static FIND_JOBS_IN_PROGRESS = BusinessConstants.API_URL + 'job/findJobsInProgress';
  public static FIND_NEW_JOBS = BusinessConstants.API_URL + 'job/findNewJobs';
  public static FIND_COMPLETED_JOBS = BusinessConstants.API_URL + 'job/findCompletedJobs';
  public static TOP_UP_ADVANCE = BusinessConstants.API_URL + 'job/topUpAdvance';
  public static COMPLETING_JOB = BusinessConstants.API_URL + 'job/completingJob';
  public static UPDATE_REMARK = BusinessConstants.API_URL + 'job/updateRemark';

  public static SAVE_MACHINE_CATEGORY = BusinessConstants.API_URL + 'machineCategory/save';
  public static FIND_MACHINE_CATEGORY_BY_ID = BusinessConstants.API_URL + 'machineCategory/findById';
  public static FIND_ALL_MACHINE_CATEGORY = BusinessConstants.API_URL + 'machineCategory/findAll';
  public static FIND_MACHINE_CATEGORY_ACTIVE = BusinessConstants.API_URL + 'machineCategory/findByActive';
  public static FIND_BY_MACHINE_CATEGORY_NAME = BusinessConstants.API_URL + 'machineCategory/findByName';

  public static SAVE_SHOW_ROOM = BusinessConstants.API_URL + 'showRoom/save';
  public static FIND_ALL_SHOW_ROOMS_PAGEABLE = BusinessConstants.API_URL + 'showRoom/findAllPageable';
  public static FIND_ALL_SHOW_ROOMS = BusinessConstants.API_URL + 'showRoom/findAll';

  public static SAVE_MACHINE = BusinessConstants.API_URL + 'machine/save';
  public static FIND_MACHINE_BY_ID = BusinessConstants.API_URL + 'machine/findById';
  public static FIND_ALL_MACHINES = BusinessConstants.API_URL + 'machine/findAll';
  public static FIND_MACHINE_ACTIVE = BusinessConstants.API_URL + 'machine/findByActive';
  public static FIND_MACHINE_BY_CUSTOMER = BusinessConstants.API_URL + 'machine/findByCustomer';
  public static FIND_MACHINE_BY_BRAND = BusinessConstants.API_URL + 'machine/findByBrand';
  public static FIND_MACHINE_BY_SERIAL_NO = BusinessConstants.API_URL + 'machine/findBySerialNo';
  public static SEARCH_MACHINE_BY_SERIAL_NO_LIKE = BusinessConstants.API_URL + 'machine/searchBySerialNoLike';
  public static SEARCH_MACHINE_BY_MODEL_NO_LIKE = BusinessConstants.API_URL + 'machine/searchByModelNoLike';

  public static FIND_ALL_MAINTENANCE_AGREEMENTS = BusinessConstants.API_URL + 'maintenance/findAll';
  public static FIND_MAINTENANCE_BY_ID = BusinessConstants.API_URL + 'maintenance/findById';
  public static FIND_MAINTENANCE_BY_CUSTOMER = BusinessConstants.API_URL + 'maintenance/findByCustomer';
  public static FIND_MAINTENANCE_BY_SERIAL_NO = BusinessConstants.API_URL + 'maintenance/findBySerialNo';
  public static SEARCH_MAINTENANCE_BY_SERIAL_NO_LIKE = BusinessConstants.API_URL + 'maintenance/searchBySerialNoLike';

}
