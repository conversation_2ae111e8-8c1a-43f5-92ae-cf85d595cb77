<div class="card">
  <div class="card-header">
    <strong>New Machine</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-12">
        <form #newMachineForm="ngForm" (ngSubmit)="save(newMachineForm);">
          <div class="row">
            <div class="mb-3 col-md-6">
              <label>Serial No</label>
              <input required #serial="ngModel" (keyup)="checkSerial()" type="text" name="serial" id="serial"
                     [(ngModel)]="machine.serialNo"
                     [class.is-invalid]="serial.invalid && serial.touched"
                     class="form-control" placeholder="Enter Serial No">
              <small class="text-danger" [class.d-none]="serial.valid || serial.untouched">* NIC No is required
              </small>

              <small *ngIf="serialAvailability" [class.is-none]="true" class="text-danger">* Serial No
                already used
              </small>
            </div>

            <div class="mb-3 col-md-6">
              <label>Machine Category</label>
              <div class="input-group">
                <input [(ngModel)]="keyCategorySearch"
                       [typeahead]="categorySearchList"
                       (typeaheadLoading)="searchCategories()"
                       (typeaheadOnSelect)="setSelectedCategory($event)"
                       [typeaheadOptionsLimit]="7"

                       typeaheadOptionField="name"
                       placeholder="Search Categories"
                       autocomplete="off"
                       size="16"
                       class="form-control" name="searchCategory">
                  <button class="btn btn-primary fa fa-plus" (click)="newCategory()"
                          type="button"></button>
              </div>
            </div>

            <div class="mb-3 col-md-6">
              <label>Customer</label>
              <div class="input-group">
                <input [(ngModel)]="keyCustomerSearch"
                       [typeahead]="customerSearchList"
                       (typeaheadLoading)="searchCustomers()"
                       (typeaheadOnSelect)="setSelectedCustomer($event)"
                       [typeaheadOptionsLimit]="7"

                       typeaheadOptionField="name"
                       placeholder="Search Customers"
                       autocomplete="off"
                       size="16"
                       class="form-control" name="searchCustomer">
                  <button class="btn btn-primary fa fa-plus" (click)="newCustomer()"
                          type="button"></button>
              </div>
            </div>

            <div class="mb-3 col-md-6">
              <label>Brand</label>
              <div class="input-group">
                <input [(ngModel)]="keyBrand"
                       [typeahead]="brandSearchList"
                       (typeaheadLoading)="searchBrands()"
                       (typeaheadOnSelect)="setSelectedBrand($event)"
                       [typeaheadOptionsLimit]="10"

                       typeaheadOptionField="name"
                       placeholder="Search Brand"
                       autocomplete="off"
                       id="appendedInputButtons" size="16"
                       class="form-control m-" name="brand">
                  <button class="btn btn-primary fa fa-plus" (click)="newBrand()"
                          type="button"></button>
              </div>
            </div>

            <div class="mb-3 col-md-6">
              <label>Model No</label>
              <input type="text" required #modelNo="ngModel" class="form-control" id="modelNo"
                     name="modelNo" placeholder="Enter Model No"
                     [class.is-invalid]="modelNo.invalid && modelNo.touched"
                     [(ngModel)]="machine.modelNo">
              <small class="text-danger" [class.d-none]="modelNo.valid || modelNo.untouched">*Model No is required
              </small>
            </div>

            <div class="mb-3 col-md-6">
              <label>Date of Sale</label>
              <input type="text" #dateOfSale="ngModel" class="form-control" id="dateOfSale"
                     name="remark" placeholder="Enter Remark"
                     [class.is-invalid]="dateOfSale.invalid && dateOfSale.touched"
                     [(ngModel)]="machine.dateOfSale">
              <small class="text-danger" [class.d-none]="dateOfSale.valid || dateOfSale.untouched">*Date of Sale is required
              </small>
            </div>

            <div class="col-md-6">
              <div class="form-check checkbox col-md-6 mt-4">
                <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                       [checked]="machine.active">
                <label class="form-check-label me-2" for="check3">Active</label>
              </div>
            </div>

          </div>

          <div class="row float-end">
            <div class="col-md-4">
              <button type="submit" class="btn btn-success me-4"
                      [disabled]="!newMachineForm.form.valid || serialAvailability"> save
              </button>
            </div>
            <div class="col-md-4">
              <button type="button" class="btn btn-warning" (click)="clear();">clear</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

