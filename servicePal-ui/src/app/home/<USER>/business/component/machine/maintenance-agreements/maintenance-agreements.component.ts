import {Component, OnInit} from '@angular/core';
import {Brand} from '../../../../inventory/model/brand';
import {Machine} from '../../../model/machine';
import {Customer} from "../../../../trade/model/customer";
import {CustomerService} from "../../../../trade/service/customer.service";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {NewMachineComponent} from "../new-machine/new-machine.component";
import {MaintenanceRecord} from "../../../model/maintenanceRecord";
import {MaintenanceService} from "../../../service/maintenance.service";

@Component({
  standalone: false,
  selector: 'app-manage-machine',
  templateUrl: './maintenance-agreements.component.html',
  styleUrls: ['./maintenance-agreements.component.css']
})
export class MaintenanceAgreementsComponent implements OnInit {
  firstRecordIndex: number = 0;
  itemsPerPage: number = 10;


  page;
  pageSize: number;
  maxsize: number;
  collectionSize: number;

  keySerialSearch: string;
  machineSearchList: Array<Machine> = [];

  keyCustomerSearch: string;
  selectedCustomerId: string;
  customerSearchList: Array<Customer> = [];

  keyBrandSearch: string;
  selectedBrandId: string;
  brandSearchList: Array<Brand> = [];

  keyModelNo: string;

  agreements: Array<MaintenanceRecord> = [];
  selectedAgreement = new MaintenanceRecord();

  selectedRow: number;

  modalRef: BsModalRef;

  pageChangeEventType: string;

  constructor(private maintenanceService: MaintenanceService, private modalService: BsModalService,
              private customerService: CustomerService) {
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 10;
    this.maxsize = 10;
    this.findAllMachines();
  }

  pageChanged(event: any) {
    this.page = event.page + 1;
    switch (this.pageChangeEventType) {
      case 'customer': {
        this.filterByCustomer();
        break;
      }
  onPageChange(event: any) {
    const page = event.page + 1; // PrimeNG paginator is 0-indexed
    this.page = page;
    // Call your existing pagination method
    this.pageChanged({ page: page });
  }

      default : {
        this.findAllMachines();
        break;
      }
    }
  }

  findAllMachines() {
    this.maintenanceService.findAllPagination(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.agreements = result.content;
      this.collectionSize = result.totalPages * 10;
    });
  }

  searchMachinesBySerial() {
    return this.maintenanceService.findBySerialNo(this.keySerialSearch).subscribe((data: Array<Machine>) => {
      return this.machineSearchList = data;
    });
  }

  setSelectedAgreement(event) {
    this.selectedAgreement = event.item;
  }

  addFilteredRecord(event) {
    this.agreements.length = 0;
    this.agreements.push(event.item);
  }

  searchCustomers(event?: any) {
    
    const query = event ? event.query : this.keyCustomerSearch;
    this.customerService.findByNameLike(query).subscribe((result: Array<Customer>) => {
      return this.customerSearchList = result;
    })
  }

  setSelectedCustomer(event) {
    this.selectedCustomerId = event.item.id;
  }

  filterByCustomer() {
    this.maintenanceService.findByCustomer(this.selectedCustomerId, this.page - 1, this.pageSize).subscribe((result: any) => {
      this.collectionSize = result.totalPages * 10;
      this.pageChangeEventType = 'customer';
      return this.agreements = result.content;
    });
  }

  itemRecord(item, index) {
    this.selectedAgreement = item;
    this.selectedRow = index;
  }

  edit() {
    this.modalRef = this.modalService.show(NewMachineComponent, <ModalOptions>{class: 'modal-md'});
    this.modalRef.content.loadById(this.selectedAgreement.id);
  }

}
