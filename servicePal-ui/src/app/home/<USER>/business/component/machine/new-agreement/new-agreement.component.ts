import {Component, OnInit} from '@angular/core';
import {Maintenance} from '../../../model/maintenance';
import {MachineCategoryService} from '../../../service/machineCategory.service';
import {Brand} from '../../../../inventory/model/brand';
import {BrandService} from '../../../../inventory/service/brand.service';
import {NgForm} from '@angular/forms';
import {NotificationService} from '../../../../../core/service/notification.service';
import {Customer} from '../../../../trade/model/customer';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';
import {ModalOptions} from 'ngx-bootstrap/modal';
import {NewCustomerComponent} from '../../../../trade/component/customer/new-customer/new-customer.component';
import {CustomerService} from "../../../../trade/service/customer.service";
import {Machine} from "../../../model/machine";
import {MachineService} from "../../../service/machine.service";
import {NewMachineComponent} from "../new-machine/new-machine.component";

@Component({
  standalone: false,
  selector: 'app-new-maintenance',
  templateUrl: './new-agreement.component.html',
  styleUrls: ['./new-agreement.component.css']
})
export class NewAgreementComponent implements OnInit {

  maintenance: Maintenance;
  serialAvailability: boolean;

  keySerialSearch: string;
  machineSearchList: Array<Machine> = [];

  keyCustomerSearch: string;
  customerSearchList: Array<Customer> = [];

  keyBrand: string;
  brandSearchList: Array<Brand> = [];

  isModal: false;
  modalRef: BsModalRef;
  modalRefCategory: BsModalRef;
  modalRefCustomer: BsModalRef;
  modalRefBrand: BsModalRef;

  constructor(private categoryService: MachineCategoryService, private customerService: CustomerService,
              private brandService: BrandService, private machineService: MachineService,
              private notificationService: NotificationService, private modalService: BsModalService) {
  }

  ngOnInit() {
    this.maintenance = new Maintenance();
    this.maintenance.customer = new Customer();
    this.maintenance.active = true;
  }

  checkSerial() {
    /*this.machineService.findBySerialNo(this.maintenance.serialNo).subscribe(result => {
      if (result) {
        this.notificationService.showError('Maintenance Already available');
        this.maintenance.serialNo = '';
      }
    });*/
  }

  loadById(machineId) {
    this.machineService.findById(machineId).subscribe((result: Maintenance) => {
      this.maintenance = result;
    });
  }

  save(form: NgForm) {
    this.machineService.save(this.maintenance).subscribe(result => {
      if (result === true) {
        this.notificationService.showSuccess('Maintenance Saved Successfully');
        if (this.isModal) {
          this.modalRef.hide();
        }
        form.resetForm();
      } else {
        this.notificationService.showError('Maintenance failed to save');
      }
    });
  }

  searchCategories() {
    this.machineService.findBySerialNo(this.keySerialSearch).subscribe((result: Array<Machine>) => {
      return this.machineSearchList = result;
    })
  }

  searchCustomers() {
    this.customerService.findByNameLike(this.keyCustomerSearch).subscribe((result: Array<Customer>) => {
      return this.customerSearchList = result;
    })
  }

  searchBrands() {
    this.brandService.findByName(this.keyBrand).subscribe((result: Array<Brand>) => {
      return this.brandSearchList = result;
    })
  }

  setSelectedMachine(event) {
    this.maintenance.machines.push(event.item.id);
  }

  setSelectedCustomer(event) {
    this.maintenance.customer.id = event.item.id;
  }

  newMachine() {
    this.modalRefCategory = this.modalService.show(NewMachineComponent, <ModalOptions>{class: 'modal-md'});
    this.modalRefCategory.content.isModal = true;
    this.modalRefCategory.content.modalRef = this.modalRefCategory;
  }

  newCustomer() {
    this.modalRefCustomer = this.modalService.show(NewCustomerComponent, <ModalOptions>{class: 'modal-md'});
    this.modalRefCustomer.content.isModal = true;
    this.modalRefCustomer.content.modalRef = this.modalRefCustomer;
  }

}
