<div class="card">
  <div class="card-header">
    <strong>View All Machines</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-3">
        <div class="input-group">
          <input [(ngModel)]="keySerialSearch"
                 [typeahead]="machineSearchList"
                 (typeaheadLoading)="searchMachinesBySerial()"
                 (typeaheadOnSelect)="addFilteredRecord($event)"
                 [typeaheadOptionsLimit]="7"

                 typeaheadOptionField="serialNo"
                 autocomplete="off"
                 placeholder="Search By Serial No"
                 class="form-control" name="serialSearch">
        </div>
      </div>
      <div class="col-md-3">
        <div class="input-group">
          <input [(ngModel)]="keyCustomerSearch"
                 [typeahead]="customerSearchList"
                 (typeaheadLoading)="searchCustomers()"
                 (typeaheadOnSelect)="setSelectedCustomer($event)"
                 [typeaheadOptionsLimit]="7"

                 typeaheadOptionField="name"
                 placeholder="Search By Customer"
                 autocomplete="off"
                 size="16"
                 class="form-control" name="customerSearch">
        </div>
      </div>
      <div class="col-md-3">
        <div class="input-group">
          <input [(ngModel)]="keyBrandSearch"
                 [typeahead]="brandSearchList"
                 (typeaheadLoading)="loadBrands()"
                 (typeaheadOnSelect)="setSelectedBrand($event)"
                 [typeaheadOptionsLimit]="10"

                 typeaheadOptionField="name"
                 placeholder="search By Brand"
                 autocomplete="off"
                 id="appendedInputButtons" size="16"
                 required #brand="ngModel"
                 class="form-control m-" name="brand">
        </div>
      </div>
      <div class="col-md-3">
        <div class="input-group">
          <input [(ngModel)]="keyModelNo"
                 [typeahead]="machineSearchList"
                 (typeaheadLoading)="loadMachineByModel()"
                 (typeaheadOnSelect)="addFilteredRecord($event)"
                 [typeaheadOptionsLimit]="7"

                 typeaheadOptionField="categoryName"
                 placeholder="Search By Model"
                 autocomplete="off"
                 size="16"
                 required #category="ngModel"
                 class="form-control " name="categorySearch">
        </div>
      </div>
    </div>

    <div class="modal-body" (load)="findAllMachines()">
      <table class="table table-striped">
        <thead align="center">
        <tr>
          <th scope="col">Serial No</th>
          <th scope="col">Brand Name</th>
          <th scope="col">Model No</th>
          <th scope="col">Category</th>
          <th scope="col">Expire on</th>
          <th scope="col">Customer</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let mcn of machines,let i = index"
            (click)="itemRecord(mcn,i)"
            [class.active]="i === selectedRow" align="center">
          <td>{{mcn.serialNo}}</td>
          <td>{{mcn.brand.name}}</td>
          <td>{{mcn.modelNo}}</td>
          <td>{{mcn.machineCategory.name}}</td>
          <td>{{mcn.warrantyExpire}}</td>
          <td>{{mcn.customer != null ? mcn.customer.name : 'N/A'}}</td>
        </tr>
        </tbody>
      </table>
      <div class="row">
        <div class="col-md-12">
          <pagination class="pagination-sm justify-content-center"
                      [totalItems]="collectionSize"
                      [(ngModel)]="page"
                      [boundaryLinks]="true"
                      [maxSize]="maxsize"
                      (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>
      {{collectionSize + ' ' + maxsize}}
      <div class="row d-flex">
        <button type="button" class="btn btn-danger float-end" (click)="edit()">Edit</button>
      </div>
    </div>
  </div>
</div>

