<div class="card">
  <div class="card-header">
    <strong>New Agreement</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-12">
        <form #newAgreementForm="ngForm" (ngSubmit)="save(newAgreementForm);">
          <div class="row">
            <!--<div class="mb-3 col-md-6">
              <label>Serial No</label>
              <input required #serial="ngModel" (keyup)="checkSerial()" type="text" name="serial" id="serial"
                     [(ngModel)]="maintenance.serialNo"
                     [class.is-invalid]="serial.invalid && serial.touched"
                     class="form-control" placeholder="Enter Serial No">
              <small class="text-danger" [class.d-none]="serial.valid || serial.untouched">* NIC No is required
              </small>

              <small *ngIf="serialAvailability" [class.is-none]="true" class="text-danger">* Serial No
                already used
              </small>
            </div>-->

            <div class="mb-3 col-md-6">
              <label>Machine</label>
              <div class="input-group">
                <input [(ngModel)]="keySerialSearch"
                       [typeahead]="machineSearchList"
                       (typeaheadLoading)="searchCategories()"
                       (typeaheadOnSelect)="setSelectedMachine($event)"
                       [typeaheadOptionsLimit]="7"

                       typeaheadOptionField="name"
                       placeholder="Search Machines"
                       autocomplete="off"
                       size="16"
                       class="form-control" name="searchMachine">
                  <button class="btn btn-primary fa fa-plus" (click)="newMachine()"
                          type="button"></button>
              </div>
            </div>

            <div class="mb-3 col-md-6">
              <label>Customer</label>
              <div class="input-group">
                <input [(ngModel)]="keyCustomerSearch"
                       [typeahead]="customerSearchList"
                       (typeaheadLoading)="searchCustomers()"
                       (typeaheadOnSelect)="setSelectedCustomer($event)"
                       [typeaheadOptionsLimit]="7"

                       typeaheadOptionField="name"
                       placeholder="Search Customers"
                       autocomplete="off"
                       size="16"
                       class="form-control" name="searchCustomer">
                  <button class="btn btn-primary fa fa-plus" (click)="newCustomer()"
                          type="button"></button>
              </div>
            </div>

            <div class="mb-3 col-md-6">
              <label>Remark</label>
              <input type="text" #remark="ngModel" class="form-control" id="remark"
                     name="remark" placeholder="Enter Remark"
                     [class.is-invalid]="remark.invalid && remark.touched"
                     [(ngModel)]="maintenance.remark">
              <small class="text-danger" [class.d-none]="remark.valid || remark.untouched">*Remark is required
              </small>
            </div>

            <div class="mb-3 col-md-6">
              <label>Agreement Start Date </label>
              <input type="date" #startDate="ngModel" class="form-control" id="warrantyStart"
                     placeholder="Enter Expiry date"
                     name="expiryDate" [(ngModel)]="maintenance.warrantyStart">
            </div>

            <div class="mb-3 col-md-6">
              <label>Agreement Expire Date </label>
              <input type="date" #expiryDate="ngModel" class="form-control" id="warrantyEnd"
                     placeholder="Enter Expiry date"
                     name="expiryDate" [(ngModel)]="maintenance.warrantyExpire">
            </div>

            <div class="mb-3 col-md-6">
              <label>Visits Per Year</label>
              <input type="text" #serviceCount="ngModel" class="form-control" id="serviceCount"
                     placeholder="Visits Per Year"
                     name="serviceCount" [(ngModel)]="maintenance.visitsPerYear">
            </div>

            <div class="mb-3 col-md-6">
              <label>Total Maintenance Charges</label>
              <input type="text" #charges="ngModel" class="form-control" id="charges"
                     placeholder="Maintenance Charges"
                     name="serviceCount" [(ngModel)]="maintenance.totalMaintenanceCharge">
            </div>

            <div class="mb-3 col-md-6">
              <label>Amount Paying</label>
              <input type="text" #payment="ngModel" class="form-control" id="payment"
                     placeholder="Amount Paying"
                     name="serviceCount" [(ngModel)]="maintenance.paidAmount">
            </div>

            <div class="col-md-6">
              <div class="form-check checkbox col-md-6 mt-4">
                <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                       [checked]="maintenance.active">
                <label class="form-check-label me-2" for="check3">Active</label>
              </div>
            </div>

          </div>

          <div class="row float-end">
            <div class="col-md-4">
              <button type="submit" class="btn btn-success me-4"
                      [disabled]="!newAgreementForm.form.valid || serialAvailability"> save
              </button>
            </div>
            <div class="col-md-4">
              <button type="button" class="btn btn-warning" (click)="newAgreementForm.resetForm()">clear</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

