import {Component, OnInit} from '@angular/core';
import {Machine} from '../../../model/machine';
import {MachineCategory} from '../../../model/machine-category';
import {MachineCategoryService} from '../../../service/machineCategory.service';
import {Brand} from '../../../../inventory/model/brand';
import {BrandService} from '../../../../inventory/service/brand.service';
import {NgForm} from '@angular/forms';
import {MachineService} from '../../../service/machine.service';
import {NotificationService} from '../../../../../core/service/notification.service';
import {Customer} from '../../../../trade/model/customer';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';
import {ModalOptions} from 'ngx-bootstrap/modal';
import {MachineCategoryComponent} from '../../machine-category/machine-category.component';
import {NewCustomerComponent} from '../../../../trade/component/customer/new-customer/new-customer.component';
import {BrandComponent} from '../../../../inventory/components/brand/brand.component';
import {CustomerService} from "../../../../trade/service/customer.service";

@Component({
  standalone: false,
  selector: 'app-new-machine',
  templateUrl: './new-machine.component.html',
  styleUrls: ['./new-machine.component.css']
})
export class NewMachineComponent implements OnInit {

  machine: Machine;
  serialAvailability: boolean;

  keyCategorySearch: string;
  categorySearchList: Array<MachineCategory> = [];

  keyCustomerSearch: string;
  customerSearchList: Array<Customer> = [];

  keyBrand: string;
  brandSearchList: Array<Brand> = [];

  isModal: false;
  modalRef: BsModalRef;
  modalRefCategory: BsModalRef;
  modalRefCustomer: BsModalRef;
  modalRefBrand: BsModalRef;

  constructor(private categoryService: MachineCategoryService, private customerService: CustomerService,
              private brandService: BrandService, private machineService: MachineService,
              private notificationService: NotificationService, private modalService: BsModalService) {
  }

  ngOnInit() {
    this.machine = new Machine();
    this.machine.customer = new Customer();
    this.machine.machineCategory = new MachineCategory();
    this.machine.brand = new Brand();
    this.machine.active = true;
  }

  checkSerial() {
    this.machineService.findBySerialNo(this.machine.serialNo).subscribe(result => {
      if (result) {
        this.notificationService.showError('Machine Already available');
        this.machine.serialNo = '';
      }
    });
  }

  loadById(machineId) {
    this.machineService.findById(machineId).subscribe((result: Machine) => {
      this.machine = result;
    });
  }

  save(form: NgForm) {
    this.machineService.save(this.machine).subscribe(result => {
      if (result === true) {
        this.notificationService.showSuccess('Machine Saved Successfully');
        if (this.isModal) {
          this.modalRef.hide();
        }
        form.resetForm();
      } else {
        this.notificationService.showError('Machine failed to save');
      }
    });
  }

  searchCategories() {
    this.categoryService.search(this.keyCategorySearch).subscribe((result: Array<MachineCategory>) => {
      return this.categorySearchList = result;
    })
  }

  searchCustomers() {
    this.customerService.findByNameLike(this.keyCustomerSearch).subscribe((result: Array<Customer>) => {
      return this.customerSearchList = result;
    })
  }

  searchBrands() {
    this.brandService.findByName(this.keyBrand).subscribe((result: Array<Brand>) => {
      return this.brandSearchList = result;
    })
  }

  setSelectedCategory(event) {
    this.machine.machineCategory.id = event.item.id;
  }

  setSelectedCustomer(event) {
    this.machine.customer.id = event.item.id;
  }

  setSelectedBrand(event) {
    this.machine.brand.id = event.item.id;
  }

  clear() {

  }

  newCategory() {
    this.modalRefCategory = this.modalService.show(MachineCategoryComponent, <ModalOptions>{class: 'modal-md'});
    this.modalRefCategory.content.isModal = true;
    this.modalRefCategory.content.modalRef = this.modalRefCategory;
  }

  newCustomer() {
    this.modalRefCustomer = this.modalService.show(NewCustomerComponent, <ModalOptions>{class: 'modal-md'});
    this.modalRefCustomer.content.isModal = true;
    this.modalRefCustomer.content.modalRef = this.modalRefCustomer;
  }

  newBrand() {
    this.modalRefBrand = this.modalService.show(BrandComponent, <ModalOptions>{class: 'modal-md'});
    this.modalRefBrand.content.isModal = true;
    this.modalRefBrand.content.modalRef = this.modalRefBrand;
  }
}
