import {Component, OnInit} from '@angular/core';
import {Brand} from '../../../../inventory/model/brand';
import {BrandService} from '../../../../inventory/service/brand.service';
import {Machine} from '../../../model/machine';
import {MachineService} from '../../../service/machine.service';
import {MachineCategoryService} from '../../../service/machineCategory.service';
import {Customer} from "../../../../trade/model/customer";
import {CustomerService} from "../../../../trade/service/customer.service";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {NewMachineComponent} from "../new-machine/new-machine.component";

@Component({
  standalone: false,
  selector: 'app-manage-machine',
  templateUrl: './manage-machine.component.html',
  styleUrls: ['./manage-machine.component.css']
})
export class ManageMachineComponent implements OnInit {
  firstRecordIndex: number = 0;
  itemsPerPage: number = 10;


  page;
  pageSize: number;
  maxsize: number;
  collectionSize: number;

  keySerialSearch: string;
  machineSearchList: Array<Machine> = [];

  keyCustomerSearch: string;
  selectedCustomerId: string;
  customerSearchList: Array<Customer> = [];

  keyBrandSearch: string;
  selectedBrandId: string;
  brandSearchList: Array<Brand> = [];

  keyModelNo: string;

  machines: Array<Machine> = [];
  selectedMachine = new Machine();

  selectedRow: number;

  modalRef: BsModalRef;

  pageChangeEventType: string;

  constructor(private machineService: MachineService, private customerService: CustomerService,
              private machineCategoryService: MachineCategoryService, private brandService: BrandService,
              private modalService: BsModalService) {
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 10;
    this.maxsize = 10;
    this.findAllMachines();
  }

  pageChanged(event: any) {
    this.page = event.page + 1;
    switch (this.pageChangeEventType) {
      case 'model' : {
        this.loadMachineByModel();
        break;
      }
  onPageChange(event: any) {
    const page = event.page + 1; // PrimeNG paginator is 0-indexed
    this.page = page;
    // Call your existing pagination method
    this.pageChanged({ page: page });
  }

      case 'brand': {
        this.filterByBrand();
        break;
      }
      default : {
        this.findAllMachines();
        break;
      }
    }
  }

  findAllMachines() {
    this.machineService.findAllPagination(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.machines = result.content;
      this.collectionSize = result.totalPages * 10;
    });
  }

  searchMachinesBySerial() {
    return this.machineService.findBySerialNo(this.keySerialSearch).subscribe((data: Array<Machine>) => {
      return this.machineSearchList = data;
    });
  }

  setSelectedMachine(event) {
    this.selectedMachine = event.item;
  }

  addFilteredRecord(event) {
    this.machines.length = 0;
    this.machines.push(event.item);
  }

  loadMachineByModel() {
    return this.machineService.searchByModelNoLike(this.keyModelNo, this.page - 1, this.pageSize).subscribe((result: any) => {
      this.collectionSize = result.totalPages * 10;
      this.pageChangeEventType = 'model';
      return this.machines = result.content;
    });
  }

  searchCustomers(event?: any) {
    
    const query = event ? event.query : this.keyCustomerSearch;
    this.customerService.findByNameLike(query).subscribe((result: Array<Customer>) => {
      return this.customerSearchList = result;
    })
  }

  loadBrands(event?: any) {
    
    const query = event ? event.query : this.keyBrandSearch;
    this.brandService.findByName(query).subscribe((data: Array<Brand>) => {
      return this.brandSearchList = data;
    });
  }

  setSelectedBrand(event) {
    this.selectedBrandId = event.item.id;
    this.filterByBrand();
  }

  filterByBrand() {
    this.machineService.findByBrand(this.selectedBrandId, this.page - 1, this.pageSize).subscribe((result: any) => {
      this.collectionSize = result.totalPages * 10;
      this.pageChangeEventType = 'brand';
      return this.machines = result.content;
    });
  }

  setSelectedCustomer(event) {
    this.selectedCustomerId = event.item.id;
  }

  filterByCustomer() {
    this.machineService.findByCustomer(this.selectedCustomerId, this.page - 1, this.pageSize).subscribe((result: any) => {
      this.collectionSize = result.totalPages * 10;
      this.pageChangeEventType = 'customer';
      return this.machines = result.content;
    });
  }

  itemRecord(item, index) {
    this.selectedMachine = item;
    this.selectedRow = index;
  }

  edit() {
    this.modalRef = this.modalService.show(NewMachineComponent, <ModalOptions>{class: 'modal-md'});
    this.modalRef.content.loadById(this.selectedMachine.id);
  }

}
