<div class="card">
  <div class="card-header">
    <strong>Manage Jobs</strong>
  </div>
  <div class="card-body">
    <div class="row ">
      <div class="col-md-2">
          <input [(ngModel)]="keyJobNo" class="form-control" name="jobNo" placeholder="Job Number">
      </div>
      <div class="col-md-3">
        <div class="input-group">
          <input [(ngModel)]="keyWoNo"
                 [typeahead]="jobListForWo"
                 (typeaheadLoading)="searchWo()"
                 [typeaheadOptionsLimit]="7"
                 typeaheadOptionField="woNumber"
                 placeholder="Search W.O"
                 autocomplete="off"
                 size="16"
                 class="form-control" name="searchWo">
          <button (click)="searchJob()" class="btn btn-success">Search</button>
        </div>
      </div>
      <div class="col-md-3">
        <div class="input-group">
          <input [(ngModel)]="keyCustomerSearch"
                 [typeahead]="customerSearchList"
                 (typeaheadLoading)="searchCustomers()"
                 (typeaheadOnSelect)="setSelectedCustomer($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadOptionField="name"
                 placeholder="Search Customers"
                 autocomplete="off"
                 size="16"
                 class="form-control" name="searchCustomer">
          <button class="btn btn-success fa fa-search" (click)="searchCustomer()"
                  type="button"></button>
        </div>
      </div>
      <div class="col-md-3">
        <input [(ngModel)]="keyMachineSearch"
               [typeahead]="machineSearchList"
               (typeaheadLoading)="searchMachines()"
               (typeaheadOnSelect)="setSelectedMachine($event)"
               [typeaheadOptionsLimit]="7"
               typeaheadOptionField="serialNo"
               placeholder="Search By Serial"
               autocomplete="off"
               size="16"
               class="form-control" name="searchCategory">
      </div>
      <div class="col-md-1">
        <button (click)="findAllPendingJobs()" class="btn btn-primary float-end">Reset</button>
      </div>
    </div>

    <div class="row mt-2">
      <table class="table table-striped">
        <thead align="center">
        <tr>
          <th scope="col">Job No</th>
          <th scope="col">W.O No</th>
          <th scope="col">Customer Name</th>
          <th scope="col">Serial No</th>
          <th scope="col">Category</th>
          <th scope="col">Status</th>
          <th scope="col">Created Date</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let job of jobs,let i = index"
            (click)="selectJob(job,i)"
            [class.active]="i === selectedRow" align="center">
          <td>{{ job.jobNo }}</td>
          <td>{{ null != job.woNumber ? job.woNumber : 'N/A' }}</td>
          <td>{{ job.customer.name }}</td>
          <td>{{ job.machine.serialNo }}</td>
          <td>{{ job.machine.machineCategory.name }}</td>
          <td>{{ job.jobStatus.value }}</td>
          <td>{{ job.jobDate }}</td>
        </tr>
        </tbody>
      </table>
      <div class="row">
        <div class="col-md-12">
          <pagination class="pagination-sm justify-content-center"
                      [totalItems]="collectionSize"
                      [(ngModel)]="page" [maxSize]="15" [boundaryLinks]="true"
                      (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-danger float-end ms-2" (click)="showInvoice()">Show Invoice</button>
        <button type="button" class="btn btn-danger float-end ms-2" (click)="showEstimate()">Show Estimate</button>
        <button type="button" class="btn btn-danger float-end ms-2" (click)="viewDetails()">Basic Details</button>
      </div>
    </div>
  </div>
</div>

