<div class="card">
  <div class="card-header">
    <strong>Completed Jobs</strong>
  </div>
  <div class="card-body">
    <div class="row ">
      <div class="col-md-4">
          <input [(ngModel)]="keyJobNo" class="form-control" name="jobNo">
      </div>
      <div class="col-md-4">
        <button (click)="searchJob()" class="btn btn-success">Search</button>
      </div>
      <div class="col-md-4">
        <button (click)="findAllCompletedJobs()" class="btn btn-primary float-end">Reset</button>
      </div>
    </div>

    <div class="row mt-2">
      <table class="table table-striped">
        <thead align="center">
        <tr>
          <th scope="col">Job No</th>
          <th scope="col">Customer Name</th>
          <th scope="col">Serial No</th>
          <th scope="col">Category</th>
          <th scope="col">Status</th>
          <th scope="col">Created Date</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let job of jobs,let i = index"
            (click)="selectJob(job,i)"
            [class.active]="i === selectedRow" align="center">
          <td>{{job.jobNo}}</td>
          <td>{{job.customer.name}}</td>
          <td>{{job.machine.serialNo}}</td>
          <td>{{job.machine.machineCategory.name}}</td>
          <td>{{job.jobStatus.value}}</td>
          <td>{{job.jobDate}}</td>
        </tr>
        </tbody>
      </table>
    </div>
    <div class="d-flex justify-content-center">
      <p-paginator
          [rows]="pageSize"
          [totalRecords]="collectionSize"
          [first]="(page-1) * pageSize"
          (onPageChange)="pageChanged($event)">
      </p-paginator>
    </div>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-danger float-end ms-2" (click)="invoice()">Invoice</button>
      </div>
    </div>
  </div>
</div>
