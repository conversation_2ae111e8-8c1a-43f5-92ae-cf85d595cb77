<div class="card">
  <div class="card-header">
    <strong>Create Job Estimate</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="mb-3 col-md-3">
        <label>Barcode</label>
        <input [(ngModel)]="keyItemSearch"
               [typeahead]="stockSearchList"
               (typeaheadLoading)="searchItems()"
               (typeaheadOnSelect)="setSelectedItem($event)"
               [typeaheadOptionsLimit]="7"
               typeaheadOptionField="barcode"
               autocomplete="off"
               #code
               class="form-control" name="searchItem">
      </div>
      <div class="mb-3 col-md-4">
        <label>Item Name</label>
        <input [(ngModel)]="itemName" name="itmName" placeholder="Item Name" autocomplete="off"
               class="form-control">
      </div>
      <div class="mb-3 col-md-2">
        <label>Price</label>
        <input type="number" required #price="ngModel" class="form-control" id="price" name="price"
               placeholder="Enter Price" [class.is-invalid]="price.invalid && price.touched"
               [(ngModel)]="itemPrice">
      </div>
      <div class="mb-3 col-md-2">
        <label>Qty</label>
        <input type="text" required #qty="ngModel" #qty1 class="form-control" id="qty" name="qty"
               placeholder="Enter Qty" [class.is-invalid]="qty.invalid && qty.touched"
               (keydown.enter)="addToEstimate()" [(ngModel)]="itemQty">
      </div>
      <div class="mb-3 col-md-1 d-flex">
        <button class="btn btn-group-sm btn-success align-self-end" (click)="addToEstimate()">
          Add
        </button>
      </div>

      <div class="col-md-12">
        <div class="table-height">
          <table class="table table-bordered">
            <thead>
            <td>Used Part</td>
            <td>Qty</td>
            <td>Stock Qty</td>
            <td>Price</td>
            </thead>
            <tbody>
            <tr *ngFor="let er of estimate.jobEstimateRecordList,let i = index"
                (click)="selectRow(i)" [class.active]="i === selectedEsRecordIndex">
              <td>{{ er.itemName }}</td>
              <td>{{ er.quantity }}</td>
              <td>{{ er.stockQty }}</td>
              <td>{{ er.price }}</td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="row mt-2 mb-4">
      <div class="col-md-3 form-group">
        <label class="text-end">Services</label>
        <input [(ngModel)]="keyServiceSearch"
               [typeahead]="serviceSearchList"
               (typeaheadLoading)="searchService()"
               (typeaheadOnSelect)="setSelectedService($event)"
               [typeaheadOptionsLimit]="7"
               typeaheadOptionField="barcode"
               placeholder="Search By Service Name"
               autocomplete="off"
               size="16"
               class="form-control" name="searchService">
      </div>
      <div class="col-md-3 form-group">
        <label class="text-end">Transport</label>
        <input [(ngModel)]="keyTransportSearch"
               [typeahead]="transportSearchList"
               (typeaheadLoading)="searchTransport()"
               (typeaheadOnSelect)="setSelectedTransport($event)"
               [typeaheadOptionsLimit]="7"
               typeaheadOptionField="barcode"
               placeholder="Search By Transport Name"
               autocomplete="off"
               size="16"
               class="form-control" name="searchTransport">
      </div>
      <div class="col-md-3">
        <label class="text-end">Discount</label>
        <input class="form-control" [(ngModel)]="estimate.discount" (change)="calculateTotal()" placeholder="Discount">
      </div>
      <div class="col-md-3">
        <label class="text-end">Total</label>
        <input class="form-control" [ngModel]="estimate.totalAmount" readonly>
      </div>
      <div class="col-md-12">
        <tag-input [(ngModel)]="estimate.services" [identifyBy]="'id'"
                   [hideForm]="true" [removable]="true" (onRemove)="calculateTotal()" #tagService>
          <ng-template let-item="item" let-index="index">
            <span> {{ item.itemName + '-' + item.sellingPrice }} </span>
            <delete-icon (click)="tagService.removeItem(item, index)"></delete-icon>
          </ng-template>
        </tag-input>
      </div>
      <div class="col-md-12">
        <tag-input [(ngModel)]="transportList" [identifyBy]="'id'" [displayBy]="'itemName'"
                   [hideForm]="true" [removable]="true" (onRemove)="removeTransport()" #tagTransport>
          <ng-template let-item="item" let-index="index">
            <span> {{ item.itemName + '-' + item.sellingPrice }} </span>
            <delete-icon (click)="tagTransport.removeItem(item, index)"></delete-icon>
          </ng-template>
        </tag-input>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-danger float-start ms-2" [disabled]="estimate.approved"
                (click)="removeSelected()">Remove row
        </button>
        <button type="button" class="btn btn-danger float-end ms-2" [disabled]="estimate.approved" (click)="save()">
          Save Estimate
        </button>
        <button type="button" class="btn btn-success float-end" (click)="clear()">Clear</button>
      </div>
    </div>
  </div>
</div>
