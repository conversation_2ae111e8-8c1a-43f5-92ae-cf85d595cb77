<div class="card">
  <div class="card-header">
    <strong>Jobs in Progress</strong>
  </div>
  <div class="card-body">

    <div class="row ">
      <div class="col-md-4">
        <div class="input-group">
          <input [(ngModel)]="keyJobNo" class="form-control" name="jobNo">
        </div>
      </div>
      <div class="col-md-4">
        <button (click)="searchJob()" class="btn btn-success">Search</button>
      </div>
      <div class="col-md-4">
        <button (click)="findJobsInProgress()" class="btn btn-primary float-end">Reset</button>
      </div>
    </div>

    <div class="row mt-2">
      <table class="table table-striped">
        <thead align="center">
        <tr>
          <th scope="col">Job No</th>
          <th scope="col">Customer Name</th>
          <th scope="col">Serial No</th>
          <th scope="col">Category</th>
          <th scope="col">Status</th>
          <th scope="col">Created Date</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let job of jobs,let i = index"
            (click)="selectJob(job,i)"
            [class.active]="i === selectedRow" align="center">
          <td>{{job.jobNo}}</td>
          <td>{{job.customer.name}}</td>
          <td>{{job.machine.serialNo}}</td>
          <td>{{job.machine.machineCategory.name}}</td>
          <td>{{job.jobStatus.value}}</td>
          <td>{{job.jobDate}}</td>
        </tr>
        </tbody>
      </table>
      <div class="row">
        <div class="col-md-12">
          <pagination class="pagination-sm justify-content-center"
            [totalItems]="collectionSize"
            [(ngModel)]="page"
            (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-success float-end ms-2"
                (click)="openModalRemarkUpdater(remarkUpdater)">Update Remark
        </button>
        <button type="button" class="btn btn-success float-end ms-2"
                (click)="openModalCompleteJob()">Complete Job
        </button>
        <button type="button" class="btn btn-primary float-end ms-2"
                (click)="openModalJobEstimate()">Show Estimate
        </button>
        <button type="button" class="btn btn-danger float-end ms-2" (click)="makePayment()">Make Payment</button>
      </div>
    </div>
  </div>
</div>

<ng-template #remarkUpdater>
  <div class="modal-header">
    <h4 class="modal-title float-start">Update Remark</h4>
  </div>
  <div class="modal-body">
    <div class="row">
      <div class="col-md-12">
        <label>Old Remark</label>
        <input class="form-control" [ngModel]="selectedJob.remark" readonly>
      </div>
      <div class="col-md-12 mt-1">
        <label>New Remark</label>
        <textarea class="form-control" [(ngModel)]="remark"></textarea>
      </div>
      <div class="col-md-12 mt-2">
        <button class="btn btn-primary float-end" (click)="updateRemark()">Save</button>
      </div>
    </div>
  </div>
</ng-template>
