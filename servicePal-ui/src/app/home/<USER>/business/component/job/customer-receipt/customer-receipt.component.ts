import {Component, OnInit} from '@angular/core';
import {CompanyService} from "../../../../../core/service/company.service";
import {Company} from "../../../../../core/model/company";
import {DomSanitizer} from "@angular/platform-browser";
import {Job} from "../../../model/job";
import {JobService} from "../../../service/job.service";
import {Customer} from "../../../../trade/model/customer";
import {Machine} from "../../../model/machine";
import {Brand} from "../../../../inventory/model/brand";

@Component({
  standalone: false,
  selector: 'app-customer-receipt',
  templateUrl: './customer-receipt.component.html',
  styleUrls: ['./customer-receipt.component.css']
})
export class CustomerReceiptComponent implements OnInit {

  company: Company;
  imageFile: any;
  jobId: string;
  job: Job;
  toDate: number;
  template: number;

  constructor(private companyService: CompanyService, private sanitizer: DomSanitizer,
              private jobService: JobService) {

  }

  ngOnInit(): void {
    this.template = 2;
    this.job = new Job();
    this.job.customer = new Customer();
    this.job.machine = new Machine();
    this.job.machine.brand = new Brand();
    this.toDate = Date.now();
    this.company = new Company();
  }

  findCompany() {
    this.companyService.findCompany().subscribe((data: Company) => {
      this.company = data;
      this.imageFile = this.sanitizer.bypassSecurityTrustResourceUrl('data:image/jpg;base64,'
        + this.company.logo);
    });
  }

  findSelectedJob() {
    this.jobService.findById(this.jobId).subscribe((result: Job) => {
      this.job = result;
    })
  }

  initiate() {
    this.findCompany();
    this.findSelectedJob();
  }

}
