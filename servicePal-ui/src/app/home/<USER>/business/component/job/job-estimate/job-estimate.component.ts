import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {Item} from '../../../../inventory/model/item';
import {JobEstimate} from '../../../model/job-estimate';
import {JobEstimateRecord} from '../../../model/job-estimate-record';
import {ItemService} from '../../../../inventory/service/item.service';
import {JobService} from '../../../service/job.service';
import {NotificationService} from '../../../../../core/service/notification.service';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {Job} from '../../../model/job';
import {StockService} from '../../../../inventory/service/stock.service';
import {Employee} from "../../../../hr/model/employee";
import {EmployeeService} from "../../../../hr/service/employee.service";
import {Stock} from "../../../../inventory/model/stock";

@Component({
  standalone: false,
  selector: 'app-job-estimate',
  templateUrl: './job-estimate.component.html',
  styleUrls: ['./job-estimate.component.css']
})
export class JobEstimateComponent implements OnInit {

  itemQty: number;
  itemPrice: number;
  minPrice: number;
  jobNo: string;
  EstimateStatus: string;
  selectedJob: Job;

  keyItemSearch: string;
  itemName: string;
  stockSearchList: Array<Stock> = [];

  keyServiceSearch: string;
  serviceSearchList: Array<Item> = [];

  keyTransportSearch: string;
  transportSearchList: Array<Item> = [];

  estimate: JobEstimate;
  estimateRecord: JobEstimateRecord;

  selectedItem: Item = new Item();

  selectedEsRecordIndex: number;
  modalRef: BsModalRef;

  transportList: Array<Item> = [];

  keyEmpSearch: string;
  empSearchList: Array<Employee> = [];

  @ViewChild('qty1') inputEl: ElementRef;
  @ViewChild('code') inputE2: ElementRef;

  constructor(private itemService: ItemService, private jobService: JobService, private employeeService: EmployeeService,
              private notificationService: NotificationService, private stockService: StockService) {
  }

  ngOnInit() {
    this.estimate = new JobEstimate();
    this.selectedJob = new Job();
    this.estimate.services = [];
    this.estimate.jobEstimateRecordList = [];
    this.transportList = [];
    this.estimate.discount = 0;
    this.estimate.partsCharge = 0;
    this.estimate.serviceCharge = 0;
    this.estimate.transportCharge = 0;
  }

  findSelectedJob() {
    this.jobService.findByJobNo(this.jobNo).subscribe((result: Job) => {
      if (null === result.jobEstimate) {
        this.ngOnInit();
      } else {
        this.selectedJob = result;
        this.estimate = result.jobEstimate;
        this.transportList = [this.estimate.transport];
        this.keyTransportSearch = (null != this.estimate.transport ? this.estimate.transport.itemName : 'N/A');
        this.findStockQty('list');
      }
    })
  }

  searchEmployee() {
    this.employeeService.findByEmployeeNameLike(this.keyEmpSearch).subscribe((result: Array<Employee>) => {
      return this.empSearchList = result;
    })
  }

  addToEstimate() {
    this.minPrice = this.selectedJob.woNumber !== null ? this.selectedItem.itemCost : this.selectedItem.sellingPrice;
    if (this.itemPrice < this.minPrice) {
      this.notificationService.showWarning('Invalid Price');
      return;
    }
    this.estimateRecord = new JobEstimateRecord();
    this.estimateRecord.barcode = this.selectedItem.barcode;
    this.estimateRecord.itemCode = this.selectedItem.itemCode;
    if (this.selectedItem.itemCode == '0') {
      this.estimateRecord.itemName = this.itemName;
    } else {
      this.estimateRecord.itemName = this.selectedItem.itemName;
    }
    this.estimateRecord.itemId = this.selectedItem.id;
    this.estimateRecord.quantity = this.itemQty;
    this.estimateRecord.unitPrice = this.itemPrice;
    this.findStockQty(this.estimateRecord.barcode);
    this.estimateRecord.price = this.itemQty * this.itemPrice;
    this.estimate.jobEstimateRecordList.push(this.estimateRecord);
    this.calculateTotal();
    this.itemQty = 0;
    this.itemPrice = 0;
    this.itemName = "";
    this.selectedItem = new Item();
    this.keyItemSearch = '';
    this.inputE2.nativeElement.focus();
  }

  findStockQty(barcode) {
    //  finding available stock qty when loading saved estimate
    if (barcode === 'list') {
      for (let index in this.estimate.jobEstimateRecordList) {
        this.stockService.findByBarcodeAndUserWarehouse(this.estimate.jobEstimateRecordList[index].barcode).subscribe((stock: Stock) => {
          if (null != stock) {
            this.estimate.jobEstimateRecordList[index].stockQty = stock.quantity;
          } else {
            this.estimate.jobEstimateRecordList[index].stockQty = 0;
          }
        })
      }
    } else {
      //  finding stock qty when creating estimate
      this.stockService.findByBarcodeAndUserWarehouse(barcode).subscribe((stock: Stock) => {
        if (null != stock) {
          this.estimateRecord.stockQty = stock.quantity;
        } else {
          this.estimateRecord.stockQty = 0;
        }
      })
    }
  }

  searchItems() {
    if (this.keyItemSearch.length >= 2) {
      this.stockService.searchByWhAndBarcodeLike(this.keyItemSearch).subscribe((result: Array<Stock>) => {
        return this.stockSearchList = result;
      })
    }
  }

  searchService() {
    if (this.keyServiceSearch.length >= 2) {
      this.itemService.findAllServiceActiveByBarcodeLike(this.keyServiceSearch).subscribe((result: Array<Item>) => {
        return this.serviceSearchList = result;
      })
    }
  }

  searchTransport() {
    this.itemService.findAllTransportActiveByBarcodeLike(this.keyTransportSearch).subscribe((result: Array<Item>) => {
      return this.transportSearchList = result;
    })
  }

  calculateTotal() {
    this.estimate.totalAmount = 0;
    this.estimate.serviceCharge = 0;
    this.estimate.partsCharge = 0;
    this.estimate.transportCharge = 0;

    for (let index = 0; this.estimate.jobEstimateRecordList.length > index; index++) {
      this.estimate.partsCharge = this.estimate.partsCharge + this.estimate.jobEstimateRecordList[index].price;
    }
    for (let index in this.estimate.services) {
      this.estimate.serviceCharge += this.estimate.services[index].sellingPrice;
    }
    this.estimate.transportCharge = (null != this.estimate.transport ? this.estimate.transport.sellingPrice : 0);
    this.estimate.totalAmount = this.estimate.partsCharge + this.estimate.serviceCharge -
      parseFloat(this.estimate.discount.toString());
    if (null != this.estimate.transport) {
      this.estimate.totalAmount = this.estimate.totalAmount + parseFloat(this.estimate.transport.sellingPrice.toString());
    }
    this.estimate.totalAmount.toFixed(2);
  }

  setSelectedItem(event) {
    this.selectedItem = event.item;
    this.itemName = this.selectedItem.itemName;
    this.itemPrice = this.selectedItem.sellingPrice;
    this.inputEl.nativeElement.focus();
  }

  setSelectedService(event) {
    this.keyServiceSearch = "";
    this.estimate.services.push(event.item);
    this.calculateTotal();
  }

  setSelectedTransport(event) {
    this.transportList[0] = event.item;
    this.estimate.transport = new Item();
    this.estimate.transport = this.transportList[0];
    this.calculateTotal();
    this.keyTransportSearch = "";
  }

  removeTransport() {
    this.keyTransportSearch = "";
    this.estimate.transport = null;
    this.calculateTotal();
  }

  save() {
    if (this.jobNo === '') {
      this.notificationService.showError('Job Not Selected');
      return;
    }
    this.estimate.createdDate = new Date();
    this.estimate.jobNo = this.jobNo;
    this.jobService.saveEstimate(this.estimate).subscribe(result => {
      if (result) {
        this.notificationService.showSuccess('Job Estimate Saved');
        this.ngOnInit();
        this.modalRef.hide();
      } else {
        this.notificationService.showError('Job Estimate creation failed');
      }
    })
  }

  clear() {
    this.ngOnInit();
    this.keyServiceSearch = '';
    this.keyTransportSearch = '';
  }

  removeSelected() {
    this.estimate.jobEstimateRecordList.splice(this.selectedEsRecordIndex, 1);
    this.calculateTotal();
  }

  selectRow(index) {
    this.selectedEsRecordIndex = index;
  }

}
