<div id="print-section" style="padding: 20px; font-family: Verdana">

  <address style="text-align: center">
    <p style="font-weight: bold; font-size: x-large">{{company.name}}</p>
    <label>{{company.fullAddress}}</label><br>
    <label>{{company.telephone1 + ',' + company.telephone2 + ',' + company.telephone3}}</label><br>
    <label>{{company.email}}</label>
  </address>

  <div style="text-align: center">
    <label style="text-decoration: underline; font-weight: bolder"> Product Collection Receipt</label>
  </div>

  <div style="margin-top: 10px; width: 100%; clear: both">
    <div *ngIf="template==1" style="float: left">
      <p>Customer Telephone - {{job.customer.telephone1 +
      (null != job.customer.telephone2 ? '/' + job.customer.telephone2 : '')}}</p>
    </div>
    <div style="float: right; margin-left: 10%">
      <p>Job No - {{job.jobNo}}</p>
    </div>
    <div style="float: right">
      <p>{{toDate | date:'medium': '+530'}}</p>
    </div>
  </div>

  <div style="clear: both; width: 100%">
    <hr>
  </div>

  <div style="margin-top: 10px; width: 100%">
    <p *ngIf="template==1">Received from {{job.customer.salutation + " " + job.customer.name + " "}} of
      {{job.customer.address}}. <b>Brand</b> is {{job.machine.brand.name}}. <b> Model </b> is {{job.machine.modelNo}}.
      <b>Serial No</b> is {{job.machine.serialNo}}. Item to be bought to workshop for repairs.</p>
    <table *ngIf="template==2" style="width:100%;">
      <td valign="top" style="width:10%">
        <b>Customer</b>
      </td>
      <td style="width:50%">
        <p>{{job.customer.salutation + " " + job.customer.name + " "}}</p>
        <p>{{job.customer.address}}</p>
        <p>{{job.customer.telephone1 +
        (null != job.customer.telephone2 ? '/' + job.customer.telephone2 : '')}}</p>
      </td>
      <td valign="top" style="width:10%"><b>Device</b></td>
      <td style="width:30%">
        <p>Brand - {{job.machine.brand.name}}
        <p>
        <p>Model - {{job.machine.modelNo}}
        <p>
        <p>Serial No - {{job.machine.serialNo}} </p>
      </td>
    </table>
    <table style="width:100%; margin-top: 10px">
      <tr style="margin-top: 8px">
        <td style="width:15%;"><b>Defects</b></td>
        <td>{{job.defect}}</td>
      </tr>
      <tr style="margin-top: 8px">
        <td style="width:15%;"><b>Less parts</b></td>
        <td>{{job.lessParts}}</td>
      </tr>
      <tr style="margin-top: 8px">
        <td style="width:15%;"><b>Damages</b></td>
        <td>{{job.damages}}</td>
      </tr>
      <tr style="margin-top: 8px">
        <td style="width:15%;"><b>Remark</b></td>
        <td>{{job.remark}}</td>
      </tr>
    </table>
  </div>

  <div style="clear: both; width: 100%; margin-top: 25px">
    <hr>
  </div>

  <div style="margin-top: 35px; width: 100%;">
    <table style="width: 100%">
      <tr>
        <td>
          <p class="fw-bold">------------------------</p>
          <p>Customer Signature</p>
        </td>
        <td style="text-align: center">
          <p class="fw-bold">-----------------</p>
          <p>Vehicle No.</p>
        </td>
        <td style="float: right">
          <p class="fw-bold">-------------------------</p>
          <p>Accepted by Signature</p>
        </td>
      </tr>
    </table>
  </div>

  <div style="width: 100%; margin-top: 20px; text-align: right">
    <span>Name</span>
    <span class="fw-bold"> ----------------------</span>
  </div>

</div>

<div style="float: right">
  <button class="btn btn-primary float-end m-4" printSectionId="print-section" ngxPrint>
    Print
  </button>
</div>
