<div class="card">
  <div class="card-header">
    <strong>Job Details</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-12">

        <div class="row">
          <div class="mb-3 col-md-3">
            <label>Job No</label>
            <input type="text" class="form-control" name="jobNo" readonly [ngModel]="job.jobNo">
          </div>

          <div class="mb-3 col-md-3">
            <label>W.O No</label>
            <input type="text" class="form-control" name="woNo" readonly [ngModel]="job.woNumber">
          </div>

          <div class="mb-3 col-md-6">
            <label>Created Date</label>
            <input type="text" [ngModel]="job.jobDate" readonly class="form-control">
          </div>

          <div class="mb-3 col-md-4">
            <label>Customer Name</label>
              <input type="text" class="form-control" readonly [ngModel]="job.customer.name">
          </div>

          <div class="mb-3 col-md-8">
            <label>Customer Address</label>
            <div class="input-group">
              <input type="text" class="form-control" readonly [ngModel]="job.customer.address">
            </div>
          </div>
          <div class="mb-3 col-md-4">
            <label>Customer Telephone</label>
            <div class="input-group">
              <input class="form-control" readonly [ngModel]="job.customer.telephone1 +
                (null != job.customer.telephone1 ? '/' + job.customer.telephone1 : '')">
            </div>
          </div>
          <div class="mb-3 col-md-4">
            <label>Machine Brand</label>
            <div class="input-group">
              <input class="form-control" readonly [ngModel]="job.machine.brand.name">
            </div>
          </div>
          <div class="mb-3 col-md-4">
            <label>Machine Model</label>
            <div class="input-group">
              <input class="form-control" readonly [ngModel]="job.machine.modelNo">
            </div>
          </div>
          <div class="mb-3 col-md-4">
            <label>Machine Serial</label>
            <div class="input-group">
              <input class="form-control" readonly [ngModel]="job.machine.serialNo">
            </div>
          </div>
          <div class="mb-3 col-md-6">
            <label>Technicians</label>
            <div class="input-group">
              <tag-input readonly [(ngModel)]='job.assignments' [identifyBy]="'id'" [displayBy]="'name'" theme='dark'
                         [editable]="false" [hideForm]="true" [allowDupes]="false"></tag-input>
            </div>
          </div>
          <div class="mb-3 col-md-6">
            <label>Inspected By</label>
            <div class="input-group">
              <input class="form-control" readonly [ngModel]="(job.inspectedBy != null ? job.inspectedBy.name: 'N/A')">
            </div>
          </div>
          <div class="mb-3 col-md-6">
            <label>Completed Date</label>
            <div class="input-group">
              <input class="form-control" readonly [ngModel]="job.completedDate ? job.completedDate : 'N/A'">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
