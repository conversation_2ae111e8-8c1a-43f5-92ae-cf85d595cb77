import { Component, OnInit, TemplateRef } from '@angular/core';
import { Job } from '../../../model/job';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { JobService } from '../../../service/job.service';
import { JobEstimateComponent } from '../job-estimate/job-estimate.component';
import { MetaDataService } from '../../../../../core/service/metaData.service';
import { NotificationService } from '../../../../../core/service/notification.service';
import { CompleteJobComponent } from '../complete-job/complete-job.component';
import { AdvancePaymentComponent } from "../advance-payment/advance-payment.component";

@Component({
  standalone: false,
  selector: 'app-jobs-in-progress',
  templateUrl: './jobs-in-progress.component.html',
  styleUrls: ['./jobs-in-progress.component.css']
})
export class JobsInProgressComponent implements OnInit {

  jobs: Array<Job> = [];
  selectedJob: Job;

  keyJobNo: string;
  pageSize: number;
  collectionSize: number;
  page: number = 1;

  selectedRow: number;
  dialogRef: DynamicDialogRef;
  remarkDialogRef: DynamicDialogRef;

  remark: string;

  constructor(private jobService: JobService, private dialogService: DialogService,
              private metaDataService: MetaDataService, private notificationService: NotificationService) {
  }

  ngOnInit() {
    this.pageSize = 10;
    this.findJobsInProgress();
  }

  searchJob() {
    this.jobService.findByJobNo(this.keyJobNo).subscribe((data: Job) => {
      this.jobs = new Array<Job>();
      if (data.jobStatus.value === 'Job In Progress') {
        this.jobs.push(data);
      }
    });
  }

  setSelectedJob(event) {
    this.selectedJob = event.item;
  }

  selectJob(job, index) {
    this.selectedRow = index;
    this.remark = '';
    this.selectedJob = job;
  }

  onPageChange(event: any) {
    const page = event.page + 1; // PrimeNG paginator is 0-indexed
    this.jobService.findJobs(page, this.pageSize).subscribe((data: any) => {
      this.jobs = data.content;
      this.collectionSize = data.totalElements;
    });
  }

  findJobsInProgress() {
    this.jobService.findJobsInProgress(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.jobs = result.content;
      this.collectionSize = result.totalElements;
    });
  }

  openModalCompleteJob() {
    this.dialogRef = this.dialogService.open(CompleteJobComponent, {
      header: 'Complete Job',
      width: '70%',
      data: {
        selectedJob: {
          id: this.selectedJob.id,
          jobNo: this.selectedJob.jobNo
        }
      }
    });
    this.dialogRef.onClose.subscribe(result => {
      this.findJobsInProgress();
    });
  }

  openModalRemarkUpdater() {
    // For template-based dialogs, we'll need to create a component or use p-dialog directly in template
    // This method can be updated to show a simple dialog
  }

  openModalJobEstimate() {
    this.remarkDialogRef = this.dialogService.open(JobEstimateComponent, {
      header: 'Job Estimate',
      width: '90%',
      data: {
        jobNo: this.selectedJob.jobNo
      }
    });
  }

  updateRemark() {
    this.jobService.updateRemark(this.selectedJob.jobNo, this.remark).subscribe((result: any) => {
      if (result.code === 200) {
        this.notificationService.showSuccess(result.message);
        this.remark = '';
        this.remarkDialogRef?.close();
      } else {
        this.notificationService.showWarning(result.message);
      }
    })
  }

  makePayment() {
    this.dialogRef = this.dialogService.open(AdvancePaymentComponent, {
      header: 'Advance Payment',
      width: '70%',
      data: {
        job: this.selectedJob
      }
    });
    this.dialogRef.onClose.subscribe(result => {
      this.findJobsInProgress();
    });
  }
}
