import {Component, OnInit, TemplateRef} from '@angular/core';
import {Job} from '../../../model/job';
import {BsModalRef, BsModalService, ModalOptions} from 'ngx-bootstrap/modal';
import {JobService} from '../../../service/job.service';
import {JobEstimateComponent} from '../job-estimate/job-estimate.component';
import {MetaDataService} from '../../../../../core/service/metaData.service';
import {NotificationService} from '../../../../../core/service/notification.service';
import {CompleteJobComponent} from '../complete-job/complete-job.component';
import {AdvancePaymentComponent} from "../advance-payment/advance-payment.component";

@Component({
  standalone: false,
  selector: 'app-jobs-in-progress',
  templateUrl: './jobs-in-progress.component.html',
  styleUrls: ['./jobs-in-progress.component.css']
})

export class JobsInProgressComponent implements OnInit {
  firstRecordIndex: number = 0;
  itemsPerPage: number = 10;


  jobs: Array<Job> = [];
  selectedJob: Job;

  keyJobNo: string;
  jobsSearch: Array<Job> = [];

  page;
  pageSize;
  collectionSize;

  selectedRow: number;
  modalRef: BsModalRef;
  remarkModal: BsModalRef;

  remark: string;

  constructor(private jobService: JobService, private modalService: BsModalService,
              private metaDataService: MetaDataService, private notificationService: NotificationService) {
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 10;
    this.findJobsInProgress();
  }

  searchJob() {
    this.jobService.findByJobNo(this.keyJobNo).subscribe((data: Job) => {
      this.jobs = new Array<Job>();
      if (data.jobStatus.value == 'Job In Progress') {
        this.jobs.push(data);
      }
    });
  }

  setSelectedJob(event) {
    this.selectedJob = event.item;
  }

  selectJob(job, index) {
    this.selectedRow = index;
    this.remark = '';
    this.selectedJob = job;
  }

  pageChanged(event: any) {
    this.page = event.page + 1;
    this.findJobsInProgress();
  }
  onPageChange(event: any) {
    const page = event.page + 1; // PrimeNG paginator is 0-indexed
    this.page = page;
    // Call your existing pagination method
    this.pageChanged({ page: page });
  }


  findJobsInProgress() {
    this.jobService.findJobsInProgress(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.jobs = result.content;
      this.collectionSize = result.totalPages * 10;
    });
  }

  openModalCompleteJob() {
    this.modalRef = this.modalService.show(CompleteJobComponent, <ModalOptions>{class: 'modal-md'});
    this.modalRef.content.selectedJob.id = this.selectedJob.id;
    this.modalRef.content.selectedJob.jobNo = this.selectedJob.jobNo;
    this.modalRef.content.modalRef = this.modalRef;
    this.modalService.onHide.subscribe(result => {
      this.findJobsInProgress();
    })
  }

  openModalRemarkUpdater(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, {class: 'modal-md'});
  }

  openModalJobEstimate() {
    this.remarkModal = this.modalService.show(JobEstimateComponent, <ModalOptions>{class: 'modal-lg'});
    this.remarkModal.content.jobNo = this.selectedJob.jobNo;
    this.remarkModal.content.findSelectedJob();
  }

  updateRemark() {
    this.jobService.updateRemark(this.selectedJob.jobNo, this.remark).subscribe((result: any) => {
      if (result.code === 200) {
        this.notificationService.showSuccess(result.message);
        this.remark = '';
        this.remarkModal.hide();
      } else {
        this.notificationService.showWarning(result.message);
      }
    })
  }

  makePayment() {
    this.modalRef = this.modalService.show(AdvancePaymentComponent, <ModalOptions>{class: 'modal-md'});
    this.modalRef.content.job = this.selectedJob;
    this.modalRef.content.modalRef = this.modalRef;
    this.modalService.onHide.subscribe(result => {
      this.findJobsInProgress();
    });
  }

}
