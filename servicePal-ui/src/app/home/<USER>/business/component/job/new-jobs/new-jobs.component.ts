import {Component, OnInit, TemplateRef} from '@angular/core';
import {Job} from '../../../model/job';
import {BsModalRef, BsModalService, ModalOptions} from 'ngx-bootstrap/modal';
import {JobService} from '../../../service/job.service';
import {JobEstimateComponent} from '../job-estimate/job-estimate.component';
import {NotificationService} from '../../../../../core/service/notification.service';
import {MetaData} from '../../../../../core/model/metaData';
import {JobEstimate} from '../../../model/job-estimate';
import {CustomerReceiptComponent} from "../customer-receipt/customer-receipt.component";
import {JobCardComponent} from "../job-card/job-card.component";
import {AdvancePaymentComponent} from "../advance-payment/advance-payment.component";

@Component({
  standalone: false,
  selector: 'app-new-jobs',
  templateUrl: './new-jobs.component.html',
  styleUrls: ['./new-jobs.component.css']
})
export class NewJobsComponent implements OnInit {

  jobs: Array<Job> = [];
  selectedJob: Job;

  keyJobNo: string;
  jobsSearch: Array<Job> = [];

  page;
  pageSize;
  collectionSize;

  selectedRow: number;
  modalRef: BsModalRef;

  approvedDate: Date;
  toDay: Date;

  constructor(private jobService: JobService,
              private modalService: BsModalService, private notificationService: NotificationService) {
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 10;
    this.findNewJobs();
    this.selectedJob = new Job();
    this.selectedJob.jobStatus = new MetaData();
    this.selectedJob.jobEstimate = new JobEstimate();
    this.toDay = new Date();
  }

  searchJob() {
    this.jobService.findByJobNo(this.keyJobNo).subscribe((data: Job) => {
      this.jobs = new Array<Job>();
      if (data.jobStatus.value == 'Job Created' || data.jobStatus.value == 'Job Assigned' ||
        data.jobStatus.value == 'Job Estimated') {
        this.jobs.push(data);
      }
    });
  }

  setSelectedJob(event) {
    this.selectedJob = event.item;
  }

  selectJob(job, index) {
    this.selectedRow = index;
    this.selectedJob = job;
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findNewJobs();
  }

  findNewJobs() {
    this.jobService.findNewJobs(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.jobs = result.content;
      this.collectionSize = result.totalPages * 10;
    });
  }

  makePayment() {
    this.modalRef = this.modalService.show(AdvancePaymentComponent, <ModalOptions>{class: 'modal-md'});
    this.modalRef.content.job = this.selectedJob;
    this.modalRef.content.modalRef = this.modalRef;
    this.modalService.onHide.subscribe(result => {
      this.findNewJobs();
    });
  }

  createEstimate() {
    this.modalRef = this.modalService.show(JobEstimateComponent, <ModalOptions>{class: 'modal-xl'});
    this.modalRef.content.jobNo = this.selectedJob.jobNo;
    this.modalRef.content.modalRef = this.modalRef;
    this.modalRef.content.findSelectedJob();
    this.modalService.onHide.subscribe(result => {
      this.findNewJobs();
    });
  }

  approveEstimate() {
    this.jobService.approveEstimate(this.selectedJob.jobNo, this.approvedDate.toLocaleDateString()).subscribe(result => {
      if (result) {
        this.notificationService.showSuccess('Estimate Approved');
        this.modalRef.hide();
        this.selectedJob = new Job();
      }
    })
  }

  openModalApproveEstimate(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, <ModalOptions>{class: 'modal-md'});
    this.modalService.onHide.subscribe(result => {
      this.findNewJobs();
    })
  }

  showCustomerReceipt() {
    this.modalRef = this.modalService.show(CustomerReceiptComponent, <ModalOptions>{class: 'modal-xl'});
    this.modalRef.content.jobId = this.selectedJob.id;
    this.modalRef.content.initiate();
  }

  showJobCard() {
    this.modalRef = this.modalService.show(JobCardComponent, <ModalOptions>{class: 'modal-xl'});
    this.modalRef.content.jobId = this.selectedJob.id;
    this.modalRef.content.initiate();
  }

}
