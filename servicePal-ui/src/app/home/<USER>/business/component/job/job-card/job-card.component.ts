import {Component, OnInit} from '@angular/core';
import {Company} from "../../../../../core/model/company";
import {Job} from "../../../model/job";
import {CompanyService} from "../../../../../core/service/company.service";
import {DomSanitizer} from "@angular/platform-browser";
import {JobService} from "../../../service/job.service";
import {Customer} from "../../../../trade/model/customer";
import {Machine} from "../../../model/machine";
import {Brand} from "../../../../inventory/model/brand";

@Component({
  standalone: false,
  selector: 'app-job-card',
  templateUrl: './job-card.component.html',
  styleUrls: ['./job-card.component.css']
})
export class JobCardComponent implements OnInit {

  company: Company;
  imageFile: any;
  jobId: string;
  job: Job;
  toDate: Date;
  rowCount: Array<any> = [];

  constructor(private companyService: CompanyService, private sanitizer: DomSanitizer, private jobService: JobService) {

  }

  ngOnInit(): void {
    this.job = new Job();
    this.job.customer = new Customer();
    this.job.machine = new Machine();
    this.job.machine.brand = new Brand();
    this.toDate = new Date();
    this.company = new Company();
    this.rowCount = Array.from({length: 10}, (v, k) => k + 1);
  }

  findCompany() {
    this.companyService.findCompany().subscribe((data: Company) => {
      this.company = data;
      this.imageFile = this.sanitizer.bypassSecurityTrustResourceUrl('data:image/jpg;base64,'
        + this.company.logo);
    });
  }

  findSelectedJob() {
    this.jobService.findById(this.jobId).subscribe((result: Job) => {
      this.job = result;
    })
  }

  initiate() {
    this.findCompany();
    this.findSelectedJob();
  }

}
