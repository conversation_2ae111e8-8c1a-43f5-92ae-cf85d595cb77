<div class="card">
  <div class="card-header">
    <strong>Complete Job</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-6 mb-3">
        <label>Completed Date</label>
        <input #approvedDt="ngModel" type="text" name="approvedDt" id="approvedDt" [minDate]="toDay"
               [(ngModel)]="completedDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
               class="form-control" placeholder="Enter Job Completed Date">
      </div>
      <div class="col-md-6 mb-3">
        <label>Inspected By</label>
        <input [(ngModel)]="keyEmpSearch"
               [typeahead]="empSearchList"
               (typeaheadLoading)="searchEmployee()"
               (typeaheadOnSelect)="setSelectedEmp($event)"
               [typeaheadOptionsLimit]="7"

               typeaheadOptionField="name"
               placeholder="Enter Technician Name"
               autocomplete="off"
               size="16"
               class="form-control" name="searchEmp">
      </div>
      <div class="col-md-6 mb-3">
        <label>Completed By</label>
        <input [(ngModel)]="keyTechSearch"
               [typeahead]="empAssignSearchList"
               (typeaheadLoading)="searchEmployeeForAssign()"
               (typeaheadOnSelect)="setSelectedEmpForAssignment($event)"
               [typeaheadOptionsLimit]="7"

               typeaheadOptionField="name"
               placeholder="Completed by Employees"
               autocomplete="off"
               size="16"
               class="form-control" name="searchTechs">
      </div>
      <div class="col-md-6 mb-3">
        <label>Commission</label>
        <input #commission="ngModel" type="number" name="commission" id="commission" class="form-control"
               placeholder="Commission" [(ngModel)]="empCommission">
      </div>
      <div class="offset-md-10 col-md-2">
        <button class="btn btn-sm btn-primary" (click)="addAssignment(empCommission)">Add</button>
      </div>
      <div class="col-md-12 mb-3">
        <label>Assigned Technicians</label>
          <tag-input [(ngModel)]="commissions" [identifyBy]="'id'"
                     [hideForm]="true" [removable]="true" #tagCommission>
            <ng-template let-item="item" let-index="index">
              <span> {{item.employee.name}} </span>
              <delete-icon (click)="tagCommission.removeItem(item, index)"></delete-icon>
            </ng-template>
          </tag-input>
      </div>
    </div>
    <div class="row mt-3">
      <div class="col-md-12">
        <button class="btn btn-success float-end ms-2" mwlConfirmationPopover (confirm)="completeJob()">Complete Job
        </button>
        <button class="btn btn-success float-end" mwlConfirmationPopover (confirm)="invoice()">Create Invoice</button>
      </div>
    </div>
  </div>
</div>


