import {Component, OnInit} from '@angular/core';
import {Employee} from '../../../../hr/model/employee';
import {CreateSiComponent} from '../../../../trade/component/create-si/create-si.component';
import {BsModalRef, BsModalService, ModalOptions} from 'ngx-bootstrap/modal';
import {JobService} from '../../../service/job.service';
import {NotificationService} from '../../../../../core/service/notification.service';
import {EmployeeService} from '../../../../hr/service/employee.service';
import {Job} from '../../../model/job';
import {JobCommission} from "../../../model/job-commission";

@Component({
  standalone: false,
  selector: 'app-complete-job',
  templateUrl: './complete-job.component.html',
  styleUrls: ['./complete-job.component.css']
})
export class CompleteJobComponent implements OnInit {

  inspectedBy: Employee;
  selectedEmp: Employee;
  completedDate: Date;

  keyEmpSearch: string;
  keyTechSearch: string;
  empSearchList: Array<Employee>;
  empAssignSearchList: Array<Employee>;
  commissions: Array<JobCommission>;

  empCommission: number;

  selectedJob: Job;
  toDay: Date;

  modalRef: BsModalRef;
  modalRefInvoice: BsModalRef;

  constructor(private jobService: JobService, private modalService: BsModalService,
              private notificationService: NotificationService, private employeeService: EmployeeService) {
  }

  ngOnInit(): void {
    this.selectedJob = new Job();
    this.toDay = new Date();
    this.commissions = [];
  }

  setSelectedEmp(event) {
    this.inspectedBy = new Employee();
    this.inspectedBy.id = event.item.id;
  }

  setSelectedEmpForAssignment(event) {
    this.selectedEmp = new Employee();
    this.selectedEmp.id = event.item.id;
    this.selectedEmp.name = event.item.name;
  }

  addAssignment(commission) {
    if (null == this.selectedJob.assignments) {
      this.selectedJob.assignments = [];
    }
    let jobCommission = new JobCommission();
    jobCommission.employee = new Employee();
    jobCommission.employee.id = this.selectedEmp.id;
    jobCommission.employee.name = this.selectedEmp.name;
    jobCommission.commission = commission;
    this.commissions.push(jobCommission);
    this.keyTechSearch = "";
    this.empCommission = 0;
  }

  completeJob() {
    this.selectedJob.inspectedBy = new Employee();
    this.selectedJob.jobCommissions = [];
    this.selectedJob.completedDate = this.completedDate;
    this.selectedJob.inspectedBy.id = this.inspectedBy.id;
    this.selectedJob.jobCommissions = this.commissions;
    this.jobService.completingJob(this.selectedJob).subscribe((result: any) => {
      if (result.code === 200) {
        this.notificationService.showSuccess('Job Completed');
        this.modalRef.hide();
      } else if (result.code === 150) {
        this.notificationService.showWarning(result.message);
        console.log(result.data);
      } else {
        this.notificationService.showError(result.message);
        console.log(result.data);
      }
    })
  }

  invoice() {
    this.completeJob();
    this.modalRefInvoice = this.modalService.show(CreateSiComponent, <ModalOptions>{class: 'modal-lg'});
    this.modalRefInvoice.content.jobNo = this.selectedJob.jobNo;
    this.modalRefInvoice.content.directMode = false;
    this.modalRefInvoice.content.jobId = this.selectedJob.id;
    this.modalRefInvoice.content.modalRef = this.modalRefInvoice;
    this.modalRefInvoice.content.initProcess();
  }

  searchEmployee(event?: any) {
    const query = event ? event.query : this.keyEmpSearch;
    this.employeeService.findByEmployeeNameLike(query).subscribe((result: Array<Employee>) => {
      return this.empSearchList = result;
    })
  }

  searchEmployeeForAssign() {
    this.employeeService.findByEmployeeNameLike(this.keyTechSearch).subscribe((result: Array<Employee>) => {
      return this.empAssignSearchList = result;
    })
  }

}
