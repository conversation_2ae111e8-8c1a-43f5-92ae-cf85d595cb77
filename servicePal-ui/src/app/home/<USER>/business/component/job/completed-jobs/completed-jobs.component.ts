import {Component, OnInit} from '@angular/core';
import {Job} from '../../../model/job';
import {BsModalRef, BsModalService, ModalOptions} from 'ngx-bootstrap/modal';
import {JobService} from '../../../service/job.service';
import {MetaData} from '../../../../../core/model/metaData';
import {CreateSiComponent} from '../../../../trade/component/create-si/create-si.component';
import {JobCommission} from "../../../model/job-commission";

@Component({
  standalone: false,
  selector: 'app-completed-jobs',
  templateUrl: './completed-jobs.component.html',
  styleUrls: ['./completed-jobs.component.css']
})
export class CompletedJobsComponent implements OnInit {
  firstRecordIndex: number = 0;
  itemsPerPage: number = 10;


  jobs: Array<Job> = [];
  selectedJob: Job;

  keyJobNo: string;
  jobsSearch: Array<Job> = [];

  page;
  pageSize;
  collectionSize;

  selectedRow: number;
  modalRef: BsModalRef;

  jobStatus: MetaData;

  constructor(private jobService: JobService,
              private modalService: BsModalService) {
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 10;
    this.jobStatus = new MetaData();
    this.findAllCompletedJobs();
  }

  searchJob() {
    this.jobService.findByJobNo(this.keyJobNo).subscribe((data: Job) => {
      this.jobs = new Array<Job>();
      if (data.jobStatus.value == 'Job Completed') {
        this.jobs.push(data);
      }
    });
  }

  setSelectedJob(event) {
    this.selectedJob = event.item;
  }

  selectJob(job, index) {
    this.selectedRow = index;
    this.selectedJob = job;
  }

  pageChanged(event: any) {
    this.page = event.page + 1;
    this.findAllCompletedJobs();
  }
  onPageChange(event: any) {
    const page = event.page + 1; // PrimeNG paginator is 0-indexed
    this.page = page;
    // Call your existing pagination method
    this.pageChanged({ page: page });
  }


  findAllCompletedJobs() {
    this.jobService.findCompletedJobs(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.jobs = result.content;
      this.collectionSize = result.totalPages * 10;
    });
  }

  invoice() {
    this.modalRef = this.modalService.show(CreateSiComponent, <ModalOptions>{class: 'modal-lg'});
    this.modalRef.content.directMode = false;
    this.modalRef.content.modalRef = this.modalRef;
    this.modalRef.content.jobId = this.selectedJob.id;
    this.modalRef.content.initProcess();
    this.modalService.onHide.subscribe(result => {
      this.findAllCompletedJobs();
    })
  }


}
