<div class="card">
  <div class="card-header">
    <strong>New Jobs</strong>
  </div>
  <div class="card-body">

    <div class="row ">
      <div class="col-md-4">
        <div class="input-group">
          <input [(ngModel)]="keyJobNo" class="form-control" name="jobNo">
        </div>
      </div>
      <div class="col-md-4">
        <button (click)="searchJob()" class="btn btn-success">Search</button>
      </div>
      <div class="col-md-4">
        <button (click)="findNewJobs()" class="btn btn-primary float-end">Reset</button>
      </div>
    </div>

    <div class="row mt-2">
      <table class="table table-striped">
        <thead align="center">
        <tr>
          <th scope="col">Job No</th>
          <th scope="col">Customer Name</th>
          <th scope="col">Serial No</th>
          <th scope="col">Category</th>
          <th scope="col">Status</th>
          <th scope="col">Created Date</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let job of jobs,let i = index"
            (click)="selectJob(job,i)"
            [class.active]="i === selectedRow" align="center">
          <td>{{job.jobNo}}</td>
          <td>{{job.customer.name}}</td>
          <td>{{job.machine.serialNo}}</td>
          <td>{{job.machine.machineCategory.name}}</td>
          <td>{{job.jobStatus.value}}</td>
          <td>{{job.jobDate}}</td>
        </tr>
        </tbody>
      </table>
      <div class="row">
        <div class="col-md-12">
          <pagination class="pagination-sm justify-content-center"
            [totalItems]="collectionSize"
            [(ngModel)]="page"
            (pageChanged)="pageChanged($event)"
            >
          </pagination>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-danger float-end ms-2" (click)="makePayment()">Make Payment</button>
        <button type="button" class="btn btn-danger float-end ms-2"
                [disabled]="selectedJob.jobStatus != null && selectedJob.jobStatus.value !== 'Job Estimated'"
                (click)="openModalApproveEstimate(approveEstimateModal)">Approve Estimate
        </button>
        <button type="button" class="btn btn-success float-end"
                (click)="createEstimate()">Create/Edit Estimate
        </button>
        <button type="button" class="btn btn-danger float-end me-2" (click)="showJobCard()">Job Card
        </button>
        <button type="button" class="btn btn-danger float-end me-2" (click)="showCustomerReceipt()">Customer Receipt
        </button>
      </div>
    </div>
  </div>
</div>

<ng-template #approveEstimateModal>
  <div class="modal-body row">
    <div class="col-md-6">
      <p><b>Job No : </b>{{selectedJob.jobNo}}</p>
      <p><b>Customer : </b>{{selectedJob.customer.name}}</p>
      <p><b>Serial : </b>{{selectedJob.machine.serialNo}}</p>
    </div>
    <div class="col-md-6">
      <label>Approved Date</label>
      <input #approvedDt="ngModel" type="text" name="approvedDt" id="approvedDt"
             [(ngModel)]="approvedDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
             [minDate]="toDay" class="form-control" placeholder="Enter Job Approved Date">
      <button class="btn btn-success float-end mt-4" mwlConfirmationPopover (confirm)="approveEstimate()">Approve
        Estimate
      </button>
    </div>
  </div>
</ng-template>


