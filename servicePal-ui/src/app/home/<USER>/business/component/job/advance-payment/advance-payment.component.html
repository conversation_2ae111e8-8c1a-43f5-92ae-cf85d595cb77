<div class="card">
  <div class="card-header">
    <strong>Pay Advance Payment</strong>
  </div>
  <div class="card-body">
    <form #payAdvanceForm="ngForm">
      <div class="row">
        <div class="col-md-12 form-group">
          <label>Job No</label>
          <input type="text" class="form-control" id="jobNo" name="jobNo" #jobNo="ngModel" disabled
                 [ngModel]="job.jobNo"
                 [class.is-invalid]="jobNo.invalid && jobNo.touched">
        </div>

        <div class="col-md-12 form-group">
          <label>Customer</label>
          <input type="text" class="form-control" id="customerName" name="customerName" #customerName="ngModel" disabled
                 [ngModel]="job.customer.name" [class.is-invalid]="customerName.invalid && customerName.touched">
        </div>

        <div class="col-md-12 form-group">
          <label>Paid Amount</label>
          <input type="text" class="form-control" id="advance" name="total" #advance="ngModel" disabled
                 [ngModel]="job.advancePayment" [class.is-invalid]="advance.invalid && advance.touched">
        </div>

        <div class="col-md-12 form-group">
          <label>Amount paying</label>
          <input type="text" class="form-control" id="payingAmount" name="payingAmount" #payingAmount="ngModel"
                 [(ngModel)]="amountPaying" [class.is-invalid]="payingAmount.invalid && payingAmount.touched" required>
        </div>

        <div class="col-md-12">
          <button class="btn btn-success float-end" (click)="topUpAdvance()" [disabled]="!payAdvanceForm.valid">Pay
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
