<div class="card">
  <div class="card-header">
    <strong>New Job</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-12">

        <form #newJobForm="ngForm" (ngSubmit)="save(newJobForm);">
          <div class="row">
            <div class="mb-3 col-md-4">
              <label>Date</label>
              <input required #jobDate="ngModel" type="text" name="jobDate" id="jobDate"
                     [(ngModel)]="job.jobDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
                     [class.is-invalid]="jobDate.invalid && jobDate.touched"
                     class="form-control" placeholder="Enter Job Date">
              <small class="text-danger" [class.d-none]="jobDate.valid || jobDate.untouched">* Job Date is required
              </small>
            </div>

            <div class="mb-3 col-md-4">
              <label>Work Order No</label>
              <input type="text" #woNo="ngModel" class="form-control" id="woNo"
                     name="woNo" placeholder="Enter Work Order Number"
                     [(ngModel)]="job.woNumber">
            </div>

            <div class="mb-3 col-md-4">
              <label>Spare parts location</label>
              <select class="form-control" required #selectedWh="ngModel"
                      [class.is-invalid]="selectedWh.invalid && selectedWh.touched" name="selectedWh"
                      [(ngModel)]="job.warehouse.id">
                <option *ngFor="let warehouse of warehouses" [value]="warehouse.id">{{warehouse.name}}</option>
              </select>
            </div>

            <div class="mb-3 col-md-6">
              <label>Customer</label>
              <div class="input-group">
                <input [(ngModel)]="keyCustomerSearch"
                       [typeahead]="customerSearchList"
                       (typeaheadLoading)="loadCustomerByNic()"
                       (typeaheadOnSelect)="setSelectedCustomer($event)"
                       [typeaheadOptionsLimit]="7"

                       typeaheadOptionField="nicBr"
                       placeholder="Search Customers"
                       autocomplete="off"
                       size="16"
                       class="form-control" name="searchCustomer">
                  <button class="btn btn-primary fa fa-plus" (click)="newCustomer()"
                          type="button"></button>
                  <button class="btn btn-success fa fa-search" (click)="searchCustomer()"
                          type="button"></button>
              </div>
            </div>

            <div class="mb-3 col-md-6">
              <label>Machine Serial</label>
              <div class="input-group">
                <input [(ngModel)]="keyMachineSearch"
                       [typeahead]="machineSearchList"
                       (typeaheadLoading)="searchMachines()"
                       (typeaheadOnSelect)="setSelectedMachine($event)"
                       [typeaheadOptionsLimit]="7"

                       typeaheadOptionField="serialNo"
                       placeholder="Search By Serial"
                       autocomplete="off"
                       size="16"
                       class="form-control" name="searchCategory">
                  <button class="btn btn-primary fa fa-plus" (click)="newMachine()"
                          type="button"></button>
              </div>
            </div>

            <div class="mb-3 col-md-6">
              <label>Technician Name</label>
              <input [(ngModel)]="keyEmpSearch"
                     [typeahead]="empSearchList"
                     (typeaheadLoading)="searchEmployee()"
                     (typeaheadOnSelect)="setSelectedEmp($event)"
                     [typeaheadOptionsLimit]="7"

                     typeaheadOptionField="name"
                     placeholder="Enter Technician Name"
                     autocomplete="off"
                     size="16"
                     class="form-control" name="searchEmp">
              <tag-input [ngModel]="job.assignments" [identifyBy]="'id'" [displayBy]="'name'"
                         [hideForm]="true" name="techList"></tag-input>
            </div>

            <div class="mb-3 col-md-6">
              <label>Damages</label>
              <input type="text" #damages="ngModel" class="form-control" id="damages"
                     name="remark" placeholder="Enter Damages"
                     [class.is-invalid]="damages.invalid && damages.touched"
                     [(ngModel)]="job.damages">
            </div>

            <div class="mb-3 col-md-6">
              <label>LessParts</label>
              <input type="text" #lessParts="ngModel" class="form-control" id="lessParts"
                     name="lessParts" placeholder="Enter Less Parts"
                     [class.is-invalid]="lessParts.invalid && lessParts.touched"
                     [(ngModel)]="job.lessParts">
            </div>

            <div class="mb-3 col-md-6">
              <label>Defect</label>
              <input type="text" #defects="ngModel" class="form-control" id="defect"
                     name="defect" placeholder="Enter Defect"
                     [class.is-invalid]="defects.invalid && defects.touched"
                     [(ngModel)]="job.defect">
            </div>

            <div class="mb-3 col-md-6">
              <label>Showroom</label>
              <select type="text" required #showroom="ngModel"
                      [class.is-invalid]="showroom.invalid && showroom.touched"
                      class="form-control" id="showroom" [(ngModel)]="job.showRoom.id" name="showroom">
                <option [value]="undefined">-Select-</option>
                <option *ngFor="let sr of showRooms" [value]="sr.id">{{sr.name}}</option>
              </select>
            </div>

            <div class="mb-3 col-md-6">
              <label>Advance Payment</label>
              <input type="text" #advance="ngModel" class="form-control" id="advance"
                     name="advance" placeholder="Enter Advance Payment"
                     [class.is-invalid]="advance.invalid && advance.touched"
                     [(ngModel)]="job.advancePayment">
            </div>

            <div class="mb-3 col-md-12">
              <label>Remark</label>
              <input type="text" #remark="ngModel" class="form-control" id="remark"
                     name="remark" placeholder="Enter Remark"
                     [class.is-invalid]="remark.invalid && remark.touched"
                     [(ngModel)]="job.remark">
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <button type="submit" class="btn btn-success float-end ms-2"
                      [disabled]="!newJobForm.form.valid">save
              </button>
              <button type="button" class="btn btn-warning float-end" (click)="clear()">clear</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
