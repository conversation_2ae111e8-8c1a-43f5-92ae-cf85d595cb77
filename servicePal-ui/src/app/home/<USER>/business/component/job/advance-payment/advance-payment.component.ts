import {Component, OnInit} from '@angular/core';
import {Job} from "../../../model/job";
import {JobService} from "../../../service/job.service";
import {Response} from "../../../../../core/model/response";
import {NotificationService} from "../../../../../core/service/notification.service";
import {BsModalRef} from "ngx-bootstrap/modal";
import {Customer} from "../../../../trade/model/customer";

@Component({
  standalone: false,
  selector: 'app-advance-payment',
  templateUrl: './advance-payment.component.html',
  styleUrls: ['./advance-payment.component.css']
})
export class AdvancePaymentComponent implements OnInit {

  job: Job;
  amountPaying: number;
  modalRef: BsModalRef;

  constructor(private jobService: JobService, private notificationService: NotificationService) {
  }

  ngOnInit(): void {
    this.job = new Job();
    this.job.customer = new Customer();
    this.amountPaying = 0;
  }

  topUpAdvance() {
    this.jobService.topUpAdvance(this.job.jobNo, this.amountPaying).subscribe((result: Response) => {
      if (result.code === 200) {
        this.notificationService.showSuccess(result.message);
        this.modalRef.hide();
      } else {
        this.notificationService.showError(result.message);
      }
    })
  }

}
