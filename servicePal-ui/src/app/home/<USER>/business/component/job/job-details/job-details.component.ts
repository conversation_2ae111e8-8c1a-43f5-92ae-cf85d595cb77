import {Component, OnInit} from '@angular/core';
import {Job} from "../../../model/job";
import {JobService} from "../../../service/job.service";
import {Customer} from "../../../../trade/model/customer";
import {Machine} from "../../../model/machine";
import {Brand} from "../../../../inventory/model/brand";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {JobEstimateComponent} from "../job-estimate/job-estimate.component";
import {InvoiceComponent} from "../../../../trade/component/invoice/invoice.component";

@Component({
  standalone: false,
  selector: 'app-job-details',
  templateUrl: './job-details.component.html',
  styleUrls: ['./job-details.component.css']
})
export class JobDetailsComponent implements OnInit {

  jobNo: string;
  job: Job;

  constructor(private jobService: JobService) {
  }

  ngOnInit(): void {
    this.job = new Job();
    this.job.customer = new Customer();
    this.job.machine = new Machine();
    this.job.machine.brand = new Brand();
  }

  findJobByNo() {
    this.jobService.findByJobNo(this.jobNo).subscribe((result: Job) => {
      this.job = result;
    })
  }

  initiate() {
    this.findJobByNo();
  }

}
