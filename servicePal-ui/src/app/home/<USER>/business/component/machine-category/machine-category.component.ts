import {Component, OnInit} from '@angular/core';
import {MachineCategory} from '../../model/machine-category';
import {MachineCategoryService} from '../../service/machineCategory.service';
import {NotificationService} from '../../../../core/service/notification.service';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';

@Component({
  standalone: false,
  selector: 'app-machine-category',
  templateUrl: './machine-category.component.html',
  styleUrls: ['./machine-category.component.css']
})
export class MachineCategoryComponent implements OnInit {

  machineCategory: MachineCategory;
  machineCategories: Array<MachineCategory> = [];
  checkListRecName: string;
  selectedRow: number;
  page = 1;
  pageSize = 10;
  collectionSize;

  isModal: false;
  modalRef: BsModalRef;

  constructor(private machineCatService: MachineCategoryService,
              private notificationService: NotificationService, private modalService: BsModalService) {
  }

  ngOnInit() {
    this.findAll();
    this.machineCategory = new MachineCategory();
    this.machineCategory.active = true;
    this.isModal = false;
  }

  findAll() {
    this.machineCatService.findAllPagination(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.machineCategories = result.content;
      this.collectionSize = result.totalPages * 10;
    });
  }

  pageChanged(event) {
    this.page = event.page;
    this.findAll();
  }

  save() {
    this.machineCatService.save(this.machineCategory).subscribe(result => {
      this.notificationService.showSuccess(result);
      if(this.isModal){
        this.modalRef.hide();
      }
      this.ngOnInit();
    }, error => {
      console.log(error);
    });
  }

  addToCheckList() {
    this.checkListRecName = '';
  }

  setSelectedCat(machineCat, index) {
    this.selectedRow = index;
    this.machineCategory = machineCat;
  }

  clearAll() {
    this.ngOnInit();
  }

}
