import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {BusinessConstants} from '../business-constants';

@Injectable({
  providedIn: 'root'
})
export class MachineService {

  constructor(private http: HttpClient) {
  }

  save(machine) {
    return this.http.post<any>(BusinessConstants.SAVE_MACHINE, machine);
  }

  findAllPagination(page, pageSize) {
    return this.http.get(BusinessConstants.FIND_ALL_MACHINES, {params: {page: page, pageSize: pageSize}});
  }

  findById(id: string) {
    return this.http.get(BusinessConstants.FIND_MACHINE_BY_ID, {params: {id: id}});
  }

  findByActive(active: string, page, pageSize) {
    return this.http.get(BusinessConstants.FIND_MACHINE_ACTIVE, {
      params: {
        active: active,
        page: page,
        pageSize: pageSize
      }
    });
  }

  findBySerialNo(serialNo: string) {
    return this.http.get(BusinessConstants.FIND_MACHINE_BY_SERIAL_NO, {params: {serialNo: serialNo}});
  }

  findByCustomer(customerId: string, page, pageSize) {
    return this.http.get(BusinessConstants.FIND_MACHINE_BY_CUSTOMER, {
      params: {
        customerId: customerId, page: page,
        pageSize: pageSize
      }
    });
  }

  findByBrand(brandId: string, page, pageSize) {
    return this.http.get(BusinessConstants.FIND_MACHINE_BY_BRAND, {
      params: {
        brandId: brandId, page: page,
        pageSize: pageSize
      }
    });
  }

  searchBySerialNoLike(serialNo: string) {
    return this.http.get(BusinessConstants.SEARCH_MACHINE_BY_SERIAL_NO_LIKE, {
      params: {serialNo: serialNo}
    });
  }

  searchByModelNoLike(modelNo: string, page, pageSize) {
    return this.http.get(BusinessConstants.SEARCH_MACHINE_BY_MODEL_NO_LIKE, {
      params: {
        modelNo: modelNo,
        page: page, pageSize: pageSize
      }
    });
  }

}
