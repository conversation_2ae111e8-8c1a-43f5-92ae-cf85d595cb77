import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {BusinessConstants} from '../business-constants';

@Injectable({
  providedIn: 'root'
})
export class MaintenanceService {

  constructor(private http: HttpClient) {
  }

  findAllPagination(page, pageSize) {
    return this.http.get(BusinessConstants.FIND_ALL_MAINTENANCE_AGREEMENTS, {params: {page: page, pageSize: pageSize}});
  }

  findById(id: string) {
    return this.http.get(BusinessConstants.FIND_MAINTENANCE_BY_ID, {params: {id: id}});
  }

  findBySerialNo(serialNo: string) {
    return this.http.get(BusinessConstants.FIND_MAINTENANCE_BY_SERIAL_NO, {params: {serialNo: serialNo}});
  }

  findByCustomer(customerId: string, page, pageSize) {
    return this.http.get(BusinessConstants.FIND_MAINTENANCE_BY_CUSTOMER, {
      params: {
        customerId: customerId, page: page,
        pageSize: pageSize
      }
    });
  }

  searchBySerialNoLike(serialNo: string) {
    return this.http.get(BusinessConstants.SEARCH_MAINTENANCE_BY_SERIAL_NO_LIKE, {
      params: {serialNo: serialNo}
    });
  }

}
