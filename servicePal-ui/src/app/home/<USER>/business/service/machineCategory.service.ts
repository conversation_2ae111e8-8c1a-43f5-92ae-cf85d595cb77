import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {BusinessConstants} from '../business-constants';

@Injectable({
  providedIn: 'root'
})
export class MachineCategoryService {

  constructor(private http: HttpClient) {
  }

  save(machineCategory) {
    return this.http.post<any>(BusinessConstants.SAVE_MACHINE_CATEGORY, machineCategory);
  }

  findAllPagination(page, pageSize) {
    return this.http.get(BusinessConstants.FIND_ALL_MACHINE_CATEGORY, {params: {page: page, pageSize: pageSize}});
  }

  findById(id: string) {
    return this.http.get(BusinessConstants.FIND_MACHINE_CATEGORY_BY_ID, {params: {id: id}});
  }

  findByActive(active: string) {
    return this.http.get(BusinessConstants.FIND_MACHINE_CATEGORY_ACTIVE, {params: {active: active}});
  }

  search(key: string) {
    return this.http.get(BusinessConstants.FIND_BY_MACHINE_CATEGORY_NAME, {params: {key: key}});
  }

}
