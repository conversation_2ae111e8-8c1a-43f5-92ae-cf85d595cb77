import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {BusinessConstants} from '../business-constants';
import {JobCommission} from "../model/job-commission";
import {Job} from "../model/job";

@Injectable({
  providedIn: 'root'
})

export class JobService {

  constructor(private http: HttpClient) {
  }

  save(job) {
    return this.http.post<any>(BusinessConstants.SAVE_JOB, job);
  }

  public findAllPagination(page, pageSize) {
    return this.http.get(BusinessConstants.FIND_ALL_JOBS, {params: {page: page, pageSize: pageSize}});
  }

  findById(id: string) {
    return this.http.get(BusinessConstants.FIND_JOB_BY_ID, {params: {id: id}});
  }

  findByJobNo(jobNo: string) {
    return this.http.get(BusinessConstants.FIND_JOB_BY_NO, {params: {jobNo: jobNo}});
  }

  findByWoNo(woNo: string) {
    return this.http.get(BusinessConstants.FIND_JOB_BY_WO_NO, {params: {woNo: woNo}});
  }

  findWoNo(woNo: string) {
    return this.http.get(BusinessConstants.FIND_JOB_WO_NO, {params: {woNo: woNo}});
  }

  findByCustomer(customerId: string) {
    return this.http.get(BusinessConstants.FIND_JOBS_BY_CUSTOMER_ID, {params: {customerId: customerId}});
  }

  findByCustomerNic(customerNic: string) {
    return this.http.get(BusinessConstants.FIND_JOBS_BY_CUSTOMER_NIC, {params: {customerNic: customerNic}});
  }

  findBySerial(serialNo: string) {
    return this.http.get(BusinessConstants.FIND_JOBS_BY_SERIAL_NO, {params: {serialNo: serialNo}});
  }

  approveEstimate(jobNo: string, approvedDate: string) {
    return this.http.get(BusinessConstants.APPROVE_JOB_ESTIMATE, {params: {jobNo: jobNo, approvedDate: approvedDate}});
  }

  changeAssignment(jobNo: string, empNo: string) {
    return this.http.get(BusinessConstants.APPROVE_JOB_ESTIMATE, {params: {jobNo: jobNo, empNo: empNo}});
  }

  completingJob(job: Job) {
    return this.http.post(BusinessConstants.COMPLETING_JOB, job);
  }

  findByJobStatus(jobStatusId, page, pageSize) {
    return this.http.get(BusinessConstants.FIND_JOB_BY_STATUS, {
      params: {
        jobStatusId: jobStatusId,
        page: page,
        pageSize: pageSize
      }
    });
  }

  findJobsInProgress(page, pageSize) {
    return this.http.get(BusinessConstants.FIND_JOBS_IN_PROGRESS, {
      params: {
        page: page,
        pageSize: pageSize
      }
    });
  }

  findNewJobs(page, pageSize) {
    return this.http.get(BusinessConstants.FIND_NEW_JOBS, {
      params: {
        page: page,
        pageSize: pageSize
      }
    });
  }

  findCompletedJobs(page, pageSize) {
    return this.http.get(BusinessConstants.FIND_COMPLETED_JOBS, {
      params: {
        page: page,
        pageSize: pageSize
      }
    });
  }

  topUpAdvance(jobNo, amount) {
    return this.http.get(BusinessConstants.TOP_UP_ADVANCE, {params: {jobNo: jobNo, amount: amount}});
  }

  saveEstimate(jobEstimate) {
    return this.http.post(BusinessConstants.SAVE_JOB_ESTIMATE, jobEstimate);
  }

  updateRemark(jobNo, remark) {
    return this.http.get(BusinessConstants.UPDATE_REMARK, {
      params: {
        jobNo: jobNo,
        remark: remark
      }
    });
  }
}
