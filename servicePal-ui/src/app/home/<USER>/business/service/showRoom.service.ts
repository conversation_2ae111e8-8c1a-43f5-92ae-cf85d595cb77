import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {BusinessConstants} from '../business-constants';

@Injectable({
  providedIn: 'root'
})
export class ShowRoomService {

  constructor(private http: HttpClient) {
  }

  save(showRoom) {
    return this.http.post<any>(BusinessConstants.SAVE_SHOW_ROOM, showRoom);
  }

  findAllPagination(page, pageSize) {
    return this.http.get(BusinessConstants.FIND_ALL_SHOW_ROOMS_PAGEABLE,
      {params: {page: page, pageSize: pageSize}});
  }

  findAll() {
    return this.http.get(BusinessConstants.FIND_ALL_SHOW_ROOMS);
  }
}
