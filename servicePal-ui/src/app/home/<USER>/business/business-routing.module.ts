import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {NewJobComponent} from './component/job/new-job/new-job.component';
import {ManageJobComponent} from './component/job/manage-job/manage-job.component';
import {NewMachineComponent} from './component/machine/new-machine/new-machine.component';
import {ManageMachineComponent} from './component/machine/manage-machine/manage-machine.component';
import {MachineCategoryComponent} from './component/machine-category/machine-category.component';
import {JobEstimateComponent} from './component/job/job-estimate/job-estimate.component';
import {NewJobsComponent} from './component/job/new-jobs/new-jobs.component';
import {JobsInProgressComponent} from './component/job/jobs-in-progress/jobs-in-progress.component';
import {CompletedJobsComponent} from './component/job/completed-jobs/completed-jobs.component';
import {NewCustomerComponent} from '../trade/component/customer/new-customer/new-customer.component';
import {ManageCustomerComponent} from '../trade/component/customer/manage-customer/manage-customer.component';
import {CompleteJobComponent} from './component/job/complete-job/complete-job.component';
import {JobCardComponent} from './component/job/job-card/job-card.component';
import {JobDetailsComponent} from './component/job/job-details/job-details.component';
import {CustomerReceiptComponent} from "./component/job/customer-receipt/customer-receipt.component";
import {MaintenanceAgreementsComponent} from "./component/machine/maintenance-agreements/maintenance-agreements.component";
import {NewAgreementComponent} from "./component/machine/new-agreement/new-agreement.component";
import {AdvancePaymentComponent} from "./component/job/advance-payment/advance-payment.component";

const routes: Routes = [
  {
    path: 'new_job',
    component: NewJobComponent
  }, {
    path: 'new_jobs',
    component: NewJobsComponent
  },
  {
    path: 'jobs_in_progress',
    component: JobsInProgressComponent
  },
  {
    path: 'completed_jobs',
    component: CompletedJobsComponent
  },
  {
    path: 'manage_jobs',
    component: ManageJobComponent
  },
  {
    path: 'estimate',
    component: JobEstimateComponent
  },
  {
    path: 'new_machine',
    component: NewMachineComponent
  },
  {
    path: 'manage_machine',
    component: ManageMachineComponent
  },
  {
    path: 'new_agreement',
    component: NewAgreementComponent
  },
  {
    path: 'maintenance_agreements',
    component: MaintenanceAgreementsComponent
  },
  {
    path: 'machine_category',
    component: MachineCategoryComponent
  },
  {
    path: 'complete_job',
    component: CompleteJobComponent
  },
  {
    path: 'job_card',
    component: JobCardComponent
  },
  {
    path: 'job_details',
    component: JobDetailsComponent
  },
  {
    path: 'customer_receipt',
    component: CustomerReceiptComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class BusinessRoutingModule {
}

export const businessRouteParams = [NewJobComponent, ManageJobComponent, JobCardComponent, JobDetailsComponent,
  NewMachineComponent, ManageMachineComponent, MachineCategoryComponent, JobEstimateComponent, NewJobsComponent,
  JobsInProgressComponent, CompletedJobsComponent, NewCustomerComponent, ManageCustomerComponent, CompleteJobComponent,
  CustomerReceiptComponent, MaintenanceAgreementsComponent, NewAgreementComponent, AdvancePaymentComponent];
