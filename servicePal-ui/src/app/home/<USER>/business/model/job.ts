import {Machine} from './machine';
import {JobEstimate} from './job-estimate';
import {Employee} from '../../hr/model/employee';
import {MetaData} from '../../../core/model/metaData';
import {Customer} from '../../trade/model/customer';
import {ShowRoom} from "./show-room";
import {JobCommission} from "./job-commission";
import {Commission} from "./commission";
import {Warehouse} from "../../inventory/model/warehouse";

export class Job {

  id: string;

  jobNo: string;

  woNumber: string;

  jobDate: Date;

  customer: Customer;

  machine: Machine;

  assignments: Array<Employee>;

  jobCommissions: Array<JobCommission>;

  jobEstimate: JobEstimate;

  jobStatus: MetaData;

  remark: string;

  refNo: string;

  inspectedBy: Employee;

  completedDate: Date;

  defect: string;

  lessParts: string;

  damages: string;

  showRoom: ShowRoom;

  advancePayment: number;

  warehouse: Warehouse;
}
