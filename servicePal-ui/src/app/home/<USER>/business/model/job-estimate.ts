import {JobEstimateRecord} from './job-estimate-record';
import {MetaData} from '../../../core/model/metaData';
import {Item} from '../../inventory/model/item';

export class JobEstimate {

  preparedBy: string;

  jobEstimateRecordList: Array<JobEstimateRecord>;

  services: Array<Item>;

  transport: Item;

  subContractorCharge: number;

  partsCharge: number;

  serviceCharge: number;

  transportCharge: number;

  discount: number;

  totalAmount: number;

  approvedBy: string;

  createdDate: Date;

  approveMethod: MetaData;

  jobNo: string;

  approved: boolean;
}
