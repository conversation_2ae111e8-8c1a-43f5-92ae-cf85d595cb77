import {Employee} from "../../hr/model/employee";
import {Job} from "./job";
import {MetaData} from "../../../core/model/metaData";
import {Commission} from "./commission";
import {JobCommission} from "./job-commission";
import {Customer} from "../../trade/model/customer";
import {Brand} from "../../inventory/model/brand";

export class MaintenanceRecord {

  id: string;

  machineSerial: string;

  date: Date;

  status: MetaData;

  customer: Customer;

  brand: Brand;

  model: String;

  employees: Array<Employee>;

  charge: number;

  jobCommissions: Array<JobCommission>;

  remark: string;

}
