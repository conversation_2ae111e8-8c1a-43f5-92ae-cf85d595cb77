import {MachineCategory} from './machine-category';
import {Brand} from '../../inventory/model/brand';
import {Customer} from '../../trade/model/customer';
import {MaintenanceRecord} from "./maintenanceRecord";
import {Machine} from "./machine";
import {MetaData} from "../../../core/model/metaData";

export class Maintenance {

  id: string;

  agreementNo: string;

  customer: Customer;

  machines: Array<Machine>;

  maintenanceRecords: Array<MaintenanceRecord>;

  totalMaintenanceCharge: number;

  paidAmount: number;

  currentVisitNo: number;

  visitsPerYear: number;

  warrantyStart: Date;

  warrantyExpire: Date;

  paymentStatus: MetaData;

  remark: string;

  active: boolean;
}
