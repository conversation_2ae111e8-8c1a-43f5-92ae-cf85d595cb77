<div id="side-bar" class="d-flex flex-column">

  <div *ngIf="!isStarter">
    <div class="m-2 cursor-pointer text-center" *ngFor="let perm of perms"
         (click)="loadView(perm.route)">
      <i [class]="perm.iconCss"></i>
      <p>{{perm.name}}</p>
    </div>
  </div>

  <div *ngIf="isStarter" class="text-center d-flex flex-column">
    <div>
      <h4>Hi {{user.firstName}}</h4>
    </div>
    <div>
      <div class="mt-3">
        <i class="fa fa-2x fa-bell cursor-pointer" routerLink="home/notification"></i>
        <span class="badge badge-danger">1</span>
      </div>
      <div class="mt-4">
        <i class="fa fa-2x fa-tasks cursor-pointer" routerLink="home/tasks"></i>
        <span class="badge badge-danger">5</span>
      </div>
    </div>
  </div>

  <div *ngIf="isStarter" class="mt-auto p-1">
    <div class="d-flex flex-row justify-content-around">
      <div>
        <i class="fa fa-2x fa-desktop cursor-pointer" routerLink="./manage_desktop" title="Manage Desktop Icons"></i>
      </div>
      <div>
        <i class="fa fa-2x fa-power-off cursor-pointer" routerLink="../login" title="Logout"></i>
      </div>
    </div>
  </div>

</div>
