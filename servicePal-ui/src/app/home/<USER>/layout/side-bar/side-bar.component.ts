import {Component, OnInit} from '@angular/core';
import {Event, NavigationEnd, Router} from '@angular/router';
import {CommonService} from '../../service/common.service';

@Component({
  standalone: false,
  selector: 'app-side-bar',
  templateUrl: './side-bar.component.html',
  styleUrls: ['./side-bar.component.css']
})
export class SideBarComponent implements OnInit {

  perms: Array<any> = [];
  isStarter = false;
  user: any;

  constructor(private router: Router, private commonService: CommonService) {
    router.events.subscribe((event: Event) => {
      if (event instanceof NavigationEnd) {
        this.loadRoutes();
      }
    });
    if (null === localStorage.getItem('currentUser')) {
      this.router.navigateByUrl('/login');
    } else {
      this.user = JSON.parse(localStorage.getItem('currentUser')).user;
    }
  }

  ngOnInit() {
  }

  loadRoutes() {
    this.perms = [];
    let currentRoute = this.router.url;
    currentRoute = currentRoute.replace('/home/', '');
    if (currentRoute !== 'starter' && currentRoute !== 'all_availablePerms' && currentRoute !== 'manage_desktop'
      && currentRoute !== '/login' && currentRoute !== 'dashboard') {
      this.commonService.findRelatedRoutes(currentRoute).subscribe((result: Array<any>) => {
        this.perms = result;
        this.isStarter = false;
      })
    } else {
      this.isStarter = true;
    }
  }

  loadView(url) {
    this.router.navigateByUrl('/home/' + url);
  }

}
