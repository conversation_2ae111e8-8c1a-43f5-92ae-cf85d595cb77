import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {ReportApiConstants} from "../report-constants";

@Injectable({
  providedIn: 'root'
})

export class ExpenseReportService {

  constructor(private http: HttpClient) {
  }

  public findAll(page, pageSize) {
    return this.http.get(ReportApiConstants.GET_EXPENSE, {params: {page: page, pageSize: pageSize}});
  }

  public findByType(type, page, pageSize) {
    return this.http.get(ReportApiConstants.SEARCH_EXPENSE_BY_TYPE, {
      params: {
        typeId: type,
        page: page,
        pageSize: pageSize
      }
    });
  }

  public findByTypeCategory(typeCategory, page, pageSize) {
    return this.http.get(ReportApiConstants.SEARCH_EXPENSE_BY_TYPE_CATEGORY, {
      params: {
        catId: typeCategory, page: page,
        pageSize: pageSize
      }
    });
  }

  public findByTypeAndEmployee(typeId, empId, page, pageSize) {
    return this.http.get(ReportApiConstants.SEARCH_EXPENSE_BY_TYPE_AND_EMPLOYEE, {
      params: {
        typeId: typeId, empId: empId, page: page,
        pageSize: pageSize
      }
    });
  }

  public findByTypeAndEmployeeAndBetweenDates(typeId, empId, fromDate, toDate) {
    return this.http.get(ReportApiConstants.SEARCH_EXPENSE_BY_TYPE_AND_EMPLOYEE_AND_BETWEEN_DAYS, {
      params: {
        typeId: typeId, empId: empId, fromDate: fromDate, toDate: toDate
      }
    });
  }

  public findByTypeAndBetweenDates(typeId, fromDate, toDate) {
    return this.http.get(ReportApiConstants.SEARCH_EXPENSE_BY_TYPE_AND_BETWEEN_DAYS, {
      params: {
        typeId: typeId, fromDate: fromDate, toDate: toDate
      }
    });
  }

  public findByEmployeeAndBetweenDates(empId, fromDate, toDate) {
    return this.http.get(ReportApiConstants.SEARCH_EXPENSE_BY_EMPLOYEE_AND_BETWEEN_DAYS, {
      params: {
        empId: empId, fromDate: fromDate, toDate: toDate
      }
    });
  }

  public findByDateBetween(fromDate, toDate, page, pageSize) {
    return this.http.get(ReportApiConstants.SEARCH_EXPENSE_BETWEEN_DATES, {
      params: {
        fromDate: fromDate,
        toDate: toDate, page: page,
        pageSize: pageSize
      }
    });
  }

  public findByEmployee(empId, page, pageSize) {
    return this.http.get(ReportApiConstants.SEARCH_EXPENSE_BY_EMPLOYEE, {
      params: {
        empId: empId,
        page: page,
        pageSize: pageSize
      }
    });
  }

  public findByRangeFilter(rangeId, page, pageSize) {
    return this.http.get(ReportApiConstants.SEARCH_EXPENSE_BY_DATE_RANGE, {
      params: {
        rangeId: rangeId, page: page,
        pageSize: pageSize
      }
    });
  }


}
