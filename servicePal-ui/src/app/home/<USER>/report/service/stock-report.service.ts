import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {ReportApiConstants} from '../report-constants';

@Injectable({
  providedIn: 'root'
})
export class StockReportService {

  constructor(private http: HttpClient) {
  }

  public getReorderList() {
    return this.http.get(ReportApiConstants.GET_REORDER_LIST);
  }

  public findStockMovement(barcode, whCode, sDate, eDate) {
    return this.http.get(ReportApiConstants.FIND_STOCK_MOVEMENT, {
      params: {
        barcode: barcode,
        whCode: whCode,
        sDate: sDate,
        eDate: eDate
      }
    })
  }

}
