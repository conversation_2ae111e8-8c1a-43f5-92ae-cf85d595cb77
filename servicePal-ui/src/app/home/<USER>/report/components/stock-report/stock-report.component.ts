import {Component, OnInit} from '@angular/core';
import {StockService} from "../../../inventory/service/stock.service";
import {ItemService} from "../../../inventory/service/item.service";
import {StockSummary} from "../../../inventory/model/stockSummary";

@Component({
  standalone: false,
  selector: 'app-stock-report',
  templateUrl: './stock-report.component.html',
  styleUrls: ['./stock-report.component.css']
})
export class StockReportComponent implements OnInit {

  stockSummaryObs: Array<StockSummary>;

  noOfItems: number = 0;
  totalQuantity: number = 0;
  totalCostValue: number = 0;
  totalPriceValue: number = 0;

  constructor(private stockService: StockService, private itemService: ItemService) {
  }

  ngOnInit() {
    this.findStockSummary();
  }

  findStockSummary() {
    this.stockSummaryObs = [];
    this.stockService.findStockSummary().subscribe((result: Array<StockSummary>) => {
      this.stockSummaryObs = result;
      this.calculateCumulativeVals(result);
    });
  }

  calculateCumulativeVals(summaries: Array<StockSummary>) {
    for (let ele of summaries) {
      this.noOfItems = this.noOfItems + ele.noOfItems;
      this.totalQuantity = this.totalQuantity + ele.totalQuantity;
      this.totalCostValue = this.totalCostValue + ele.totalCostValue;
      this.totalPriceValue = this.totalPriceValue + ele.totalPriceValue;
    }
  }

  /* loadItems() {
     return this.itemService.findAllActiveByNameLike(this.keyItemSearch).subscribe((data: Array<Item>) => {
       return this.itemSearched = data;
     });
   }*/

}
