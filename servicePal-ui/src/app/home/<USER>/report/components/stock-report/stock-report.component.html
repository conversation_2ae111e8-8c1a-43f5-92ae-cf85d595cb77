<div class="card">
  <div class="card-header">
    <strong>STOCK REPORT</strong>
  </div>
  <div class="card-body">
    <div class="row" *ngFor="let summary of stockSummaryObs">
      <div class="col-md-4">
        Warehouse Name
      </div>
      <div class="col-md-8">
        <b>{{summary.warehouseName}}</b>
      </div>
      <div class="col-md-4">
        Total No Of Items
      </div>
      <div class="col-md-8">
        <b>{{summary.noOfItems | number:'1.2-2'}}</b>
      </div>
      <div class="col-md-4">
        Total Quantity
      </div>
      <div class="col-md-8">
        <b>{{summary.totalQuantity | number:'1.2-2'}}</b>
      </div>
      <div class="col-md-4">
        Total Stock Price Value
      </div>
      <div class="col-md-8">
        <b>{{summary.totalPriceValue | number:'1.2-2'}} LKR</b>
      </div>
      <div class="col-md-4">
        Total Stock Cost Value
      </div>
      <div class="col-md-8">
        <b>{{summary.totalCostValue | number:'1.2-2'}} LKR</b>
      </div>
      <div class="col-md-12">
        <hr class="m-2">
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <h4>Cumulative Values</h4>
      </div>
      <div class="col-md-4">
        Total No Of Items
      </div>
      <div class="col-md-8">
        <b>{{noOfItems | number:'1.2-2'}}</b>
      </div>
      <div class="col-md-4">
        Total Quantity
      </div>
      <div class="col-md-8">
        <b>{{totalQuantity | number:'1.2-2'}}</b>
      </div>
      <div class="col-md-4">
        Total Stock Price Value
      </div>
      <div class="col-md-8">
        <b>{{totalPriceValue | number:'1.2-2'}} LKR</b>
      </div>
      <div class="col-md-4">
        Total Stock Cost Value
      </div>
      <div class="col-md-8">
        <b>{{totalCostValue | number:'1.2-2'}} LKR</b>
      </div>
    </div>

  </div>
</div>
