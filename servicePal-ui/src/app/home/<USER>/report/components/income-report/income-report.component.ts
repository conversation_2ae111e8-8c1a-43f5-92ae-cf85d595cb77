import {Component, OnInit} from '@angular/core';
import {MetaData} from "../../../../core/model/metaData";
import {Transaction} from "../../../../core/model/transaction";
import {TransactionService} from "../../../../core/service/transaction.service";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {InvoiceComponent} from "../../../trade/component/invoice/invoice.component";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {JobDetailsComponent} from "../../../business/component/job/job-details/job-details.component";

@Component({
  standalone: false,
  selector: 'app-income-report',
  templateUrl: './income-report.component.html',
  styleUrls: ['./income-report.component.css']
})
export class IncomeReportComponent implements OnInit {

  sDate: Date;
  eDate: Date;
  durationFilter: Array<MetaData>;
  selectedDuration: MetaData;
  transactions: Array<Transaction>;
  selectedTr: Transaction;
  selectedRow: number;
  totalAmount: number;
  modalRef: BsModalRef;

  constructor(private transactionService: TransactionService, private metaDataService: MetaDataService,
              private modalService: BsModalService) {
  }

  ngOnInit(): void {
    this.selectedDuration = new MetaData();
    this.sDate = new Date();
    this.eDate = new Date();
    this.findDurationFilterData();
    this.findTodayTransaction()
  }

  findTransaction(durationId) {
    this.transactionService.findByRangeFilterAndOperator(durationId, "plus").subscribe(
      (result: Array<Transaction>) => {
        this.transactions = result;
        this.calculateTotalAmount();
      })
  }

  findTodayTransaction() {
    this.metaDataService.findByValueAndCategory("Today", "ReportFilterDuration").subscribe((result: MetaData) => {
      this.findTransaction(result.id);
    })
  }

  findDurationFilterData() {
    this.metaDataService.findByCategory('ReportFilterDuration').subscribe((data: Array<MetaData>) => {
      this.durationFilter = data;
    });
  }

  searchBetweenDates() {
    this.transactionService.findByDateRangeAndOperator(this.sDate.toLocaleDateString(),
      this.eDate.toLocaleDateString(),
      "plus").subscribe(
      (result: Array<Transaction>) => {
        this.transactions = result;
        this.calculateTotalAmount();
      })
  }

  filterByDuration() {
    this.findTransaction(this.selectedDuration.id);
  }

  selectTr(tr: Transaction, index: number) {
    this.selectedTr = tr;
    this.selectedRow = index;
  }

  viewDetail() {
    if (this.selectedTr.refType === 'Job Advance') {
      this.modalRef = this.modalService.show(JobDetailsComponent, <ModalOptions>{class: 'modal-lg'});
      this.modalRef.content.findJobByNo(this.selectedTr.refNo);
    }
    if (this.selectedTr.type.value === 'SalesInvoice') {
      this.modalRef = this.modalService.show(InvoiceComponent, <ModalOptions>{class: 'modal-xl'});
      this.modalRef.content.invoiceNo = this.selectedTr.refNo;
      this.modalRef.content.findInvoice();
    }
  }

  calculateTotalAmount() {
    this.totalAmount = 0;
    for (let tr of this.transactions) {
      if (null != tr.amount)
        this.totalAmount = this.totalAmount + tr.amount
    }
  }

}
