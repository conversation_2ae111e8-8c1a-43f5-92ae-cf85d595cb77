<div class="card">
  <div class="card-header">
    <strong>Income Report</strong>
  </div>
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-3">
        <select 
          type="text" 
          required 
          #duration="ngModel" 
          [class.is-invalid]="duration.invalid && duration.touched"
          class="form-select" 
          id="duration" 
          [(ngModel)]="selectedDuration.id" 
          name="duration"
          (ngModelChange)="filterByDuration()">
          <option *ngFor="let filter of durationFilter" [value]="filter.id">{{filter.value}}</option>
        </select>
      </div>
      <div class="offset-1 col-md-3">
        <!-- Updated to PrimeNG calendar -->
        <p-calendar
          required
          #startDate="ngModel"
          [(ngModel)]="sDate"
          [showIcon]="true"
          [dateFormat]="'yy-mm-dd'"
          placeholder="Enter Job Date"
          class="form-control">
        </p-calendar>
      </div>
      <div class="col-md-3">
        <!-- Updated to PrimeNG calendar -->
        <p-calendar
          required
          #endDate="ngModel"
          [(ngModel)]="eDate"
          [showIcon]="true"
          [dateFormat]="'yy-mm-dd'"
          placeholder="Enter Job Date"
          class="form-control">
        </p-calendar>
      </div>
      <div class="col-md-2">
        <button class="btn btn-primary" (click)="searchBetweenDates()">Search</button>
      </div>
    </div>

    <div class="row g-3 mt-2" id="print-income-div">
      <table class="table table-striped">
        <thead align="center">
        <tr>
          <th scope="col">Date</th>
          <th scope="col">Type</th>
          <th scope="col">Reference</th>
          <th scope="col">Amount</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let tr of transactions,let i = index"
            (click)="selectTr(tr,i)"
            [class.active]="i === selectedRow" align="center">
          <td>{{tr.date | date:'short'}}</td>
          <td>{{tr.refType}}</td>
          <td>{{tr.refNo}}</td>
          <td>{{tr.amount | number : '1.2-2'}}</td>
        </tr>
        </tbody>
      </table>
      <label class="ms-1 mt-3 fw-bold">Total Amount</label>
      <label class="mt-3 ms-3 fw-bold">{{totalAmount | number : '1.2-2'}}</label>
    </div>
    <div class="row g-3">
      <div class="col-md-12">
        <button type="button" class="btn btn-danger float-end ms-2" printSectionId="print-income-div" ngxPrint
                [useExistingCss]="true" printTitle="Income Report">Print
        </button>
        <button type="button" class="btn btn-danger float-end ms-2" (click)="viewDetail()">View Detail</button>
      </div>
    </div>
  </div>
</div>