import {Component, OnInit} from '@angular/core';
import {Item} from "../../../inventory/model/item";
import {StockReportService} from "../../service/stock-report.service";
import {ItemService} from "../../../inventory/service/item.service";
import {StockMovement} from "../../model/stock-movement";
import {NotificationService} from "../../../../core/service/notification.service";
import {formatDate} from "@angular/common";
import {Warehouse} from "../../../inventory/model/warehouse";
import {WarehouseService} from "../../../inventory/service/warehouse.service";

@Component({
  standalone: false,
  selector: 'app-stock-moving-report',
  templateUrl: './stock-moving-report.component.html',
  styleUrls: ['./stock-moving-report.component.css']
})
export class StockMovingReportComponent implements OnInit {

  selectedItem: Item;
  itemBarcodeSearch: string;
  itemListByBarcode: Array<Item>;
  itemNameSearch: string;
  itemList: Array<Item>;

  stockMovementList: Array<StockMovement>;

  sDate: Date;
  eDate: Date;

  warehouses: Array<Warehouse> = [];
  selectedWarehouse: Warehouse;

  constructor(private stockReportService: StockReportService, private itemService: ItemService,
              private notificationService: NotificationService, private warehouseService: WarehouseService,) {
  }

  ngOnInit(): void {
    this.selectedItem = new Item();
    this.loadWarehouses();
  }

  findStockMovement() {
    if (this.itemBarcodeSearch || this.itemNameSearch) {
      let sDate = formatDate(this.sDate, 'M/d/yyyy', 'en_US');
      let eDate = formatDate(this.eDate, 'M/d/yyyy', 'en_US');
      this.stockReportService.findStockMovement(this.selectedItem.barcode, this.selectedWarehouse.code,
        sDate, eDate)
        .subscribe((data: Array<StockMovement>) => {
          this.stockMovementList = data;
        })
    } else {
      this.notificationService.showError('Please Enter Item')
    }
  }

  loadWarehouses(event?: any) {
    this.warehouseService.findAllActive().subscribe((result: Array<Warehouse>) => {
      if (result.length === 1) {
        this.selectedWarehouse = result[0];
      }
      return this.warehouses = result;
    })
  }

  loadItemName() {
    this.itemService.findAllByNameLike(this.itemNameSearch).subscribe((data: Array<Item>) => {
      this.itemList = data;
    })
  }

  setSelectedItem(event) {
    this.selectedItem.barcode = event.item.barcode;
    this.itemNameSearch = event.item.itemName;
    this.itemBarcodeSearch = event.item.barcode;
  }

  loadItemBarcode() {
    this.itemService.findAllByBarcodeLike(this.itemBarcodeSearch).subscribe((data: Array<Item>) => {
      this.itemListByBarcode = data;
    })
  }

}
