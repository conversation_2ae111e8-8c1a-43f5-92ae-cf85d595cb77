<div class="card">
  <div class="card-header">
    <strong>Stock Moving Report</strong>
  </div>
  <div class="card-body">
    <div class="row g-3">
      <div class="col-sm-2 col-xl-2 col-lg-2 col-md-2 col-xs-12">
        <select class="form-control" required #selectedWh="ngModel"
                [class.is-invalid]="selectedWh.invalid && selectedWh.touched" name="selectedWh"
                [(ngModel)]="selectedWarehouse">
          <option [ngValue]="">Select Warehouse</option>
          <option *ngFor="let wh of warehouses" [ngValue]="wh">{{wh.name}}</option>
        </select>
      </div>
      <div class="col-sm-3 col-xl-3 col-lg-3 col-md-3 col-xs-12">
        <!-- Updated to PrimeNG autocomplete -->
        <p-autocomplete
          [(ngModel)]="itemNameSearch"
          [suggestions]="itemList"
          (onSelect)="setSelectedItem($event)"
          [dropdown]="true"
          field="itemName"
          (completeMethod)="loadItemName()"
          placeholder="Enter Item Name"
          class="form-control" id="item" name="item">
        </p-autocomplete>
      </div>
      <div class="col-sm-2 col-xl-2 col-lg-2 col-md-2 col-xs-12">
        <!-- Updated to PrimeNG autocomplete -->
        <p-autocomplete
          [(ngModel)]="itemBarcodeSearch"
          [suggestions]="itemListByBarcode"
          (onSelect)="setSelectedItem($event)"
          [dropdown]="true"
          field="barcode"
          (completeMethod)="loadItemBarcode()"
          placeholder="Enter Barcode"
          class="form-control" id="barcode" name="barcode">
        </p-autocomplete>
      </div>
      <div class="col-sm-2 col-md-2 col-lg-2 col-xl-2 col-xs-12">
        <!-- Updated to PrimeNG calendar -->
        <p-calendar
          [(ngModel)]="sDate"
          [showIcon]="true"
          [dateFormat]="'yy-mm-dd'"
          placeholder="Enter Start Date"
          class="form-control"
          id="startDate" name="startDate">
        </p-calendar>
      </div>
      <div class="col-sm-2 col-md-2 col-lg-2 col-xl-2 col-xs-12">
        <!-- Updated to PrimeNG calendar -->
        <p-calendar
          [(ngModel)]="eDate"
          [showIcon]="true"
          [dateFormat]="'yy-mm-dd'"
          placeholder="Enter End Date"
          class="form-control"
          id="endDate" name="endDate">
        </p-calendar>
      </div>
      <div class="col-sm-1 col-md-1 col-lg-1 col-xl-1 col-xs-12">
        <button (click)="findStockMovement()" class="btn btn-primary">Search</button>
      </div>
    </div>
    <div class="row g-3 mt-3">
      <table class="table table-stripped table-bordered table-responsive-sm">
        <thead align="center">
        <th>Type</th>
        <th>Item Name</th>
        <th>Date</th>
        <th>Quantity</th>
        <th>Stock Qty Before</th>
        <th>Stock Qty After</th>
        </thead>
        <tbody>
        <tr *ngFor="let record of stockMovementList, let i=index">
          <td>{{record.type}}</td>
          <td>{{record.itemName}}</td>
          <td>{{record.dateTime | date }}</td>
          <td>{{record.quantity}}</td>
          <td>{{record.stockCountBefore}}</td>
          <td>{{record.stockCountAfter}}</td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
