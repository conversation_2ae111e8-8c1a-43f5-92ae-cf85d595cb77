<div class="card">
  <div class="card-header">
    <strong>Stock Moving Report</strong>
  </div>
  <div class="card-body">
    <div class="row g-3">
      <div class="col-sm-2 col-xl-2 col-lg-2 col-md-2 col-xs-12">
        <select class="form-control" required #selectedWh="ngModel"
                [class.is-invalid]="selectedWh.invalid && selectedWh.touched" name="selectedWh"
                [(ngModel)]="selectedWarehouse">
          <option [ngValue]="">Select Warehouse</option>
          <option *ngFor="let wh of warehouses" [ngValue]="wh">{{wh.name}}</option>
        </select>
      </div>
      <div class="col-sm-3 col-xl-3 col-lg-3 col-md-3 col-xs-12">
        <input #item="ngModel"
               [(ngModel)]="itemNameSearch"
               [typeahead]="itemList"
               (typeaheadLoading)="loadItemName()"
               (typeaheadOnSelect)="setSelectedItem($event)"
               [typeaheadOptionsLimit]="7"

               typeaheadOptionField="itemName"
               class="form-control" id="item" name="item"
               placeholder="Enter Item Name"
               type="text" autocomplete="off">
      </div>
      <div class="col-sm-2 col-xl-2 col-lg-2 col-md-2 col-xs-12">
        <input #item="ngModel"
               [(ngModel)]="itemBarcodeSearch"
               [typeahead]="itemListByBarcode"
               (typeaheadLoading)="loadItemBarcode()"
               (typeaheadOnSelect)="setSelectedItem($event)"
               [typeaheadOptionsLimit]="7"

               typeaheadOptionField="barcode"
               class="form-control" id="barcode" name="barcode"
               placeholder="Enter Barcode"
               type="text" autocomplete="off">
      </div>
      <div class="col-sm-2 col-md-2 col-lg-2 col-xl-2 col-xs-12">
        <input #startDate="ngModel" [(ngModel)]="sDate" [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }" bsDatepicker
               class="form-control"
               id="startDate" name="startDate" placeholder="Enter Start Date"
               required type="text" autocomplete="off">
      </div>
      <div class="col-sm-2 col-md-2 col-lg-2 col-xl-2 col-xs-12">
        <input #endDate="ngModel" [(ngModel)]="eDate" [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }" bsDatepicker
               class="form-control"
               id="endDate" name="endDate" placeholder="Enter End Date"
               required type="text" autocomplete="off">
      </div>
      <div class="col-sm-1 col-md-1 col-lg-1 col-xl-1 col-xs-12">
        <button (click)="findStockMovement()" class="btn btn-primary">Search</button>
      </div>
    </div>
    <div class="row g-3 mt-3">
      <table class="table table-stripped table-bordered table-responsive-sm">
        <thead align="center">
        <th>Type</th>
        <th>Item Name</th>
        <th>Date</th>
        <th>Quantity</th>
        <th>Stock Qty Before</th>
        <th>Stock Qty After</th>
        </thead>
        <tbody>
        <tr *ngFor="let record of stockMovementList, let i=index">
          <td>{{record.type}}</td>
          <td>{{record.itemName}}</td>
          <td>{{record.dateTime | date }}</td>
          <td>{{record.quantity}}</td>
          <td>{{record.stockCountBefore}}</td>
          <td>{{record.stockCountAfter}}</td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
