<div class="card">
  <div class="card-header">
    <strong>REORDER REPORT</strong>
  </div>
  <div class="card-body">
    <div class="row g-3 mb-2">
      <div class="col-md-3 input-group">
        <input [(ngModel)]="threshold"
               (ngModelChange)="findReorderList()"
               autocomplete="off" type="number"
               placeholder="Threshold Val"
               class="form-control" name="category">
      </div>
      <div class="col-md-3">
        <select class="form-control" required #selectedWh="ngModel"
                [class.is-invalid]="selectedWh.invalid && selectedWh.touched" name="selectedWh"
                [(ngModel)]="selectedWarehouse" (change)="findReorderListByWhCode()">
          <option [ngValue]="">Select Warehouse</option>
          <option *ngFor="let wh of warehouses" [ngValue]="wh">{{wh.name}}</option>
        </select>
      </div>
      <!--<div class="col-md-3 input-group">
        <input [(ngModel)]="keyItemSearch"
               [typeahead]="itemSearched"
               (typeaheadLoading)="loadItems()"
               (typeaheadOnSelect)="setSelectedItem($event)"
               [typeaheadOptionsLimit]="7"

               typeaheadOptionField="itemName"
               placeholder="Search By Items"
               autocomplete="off"
               size="16"
               class="form-control" name="searchItem">
      </div>-->
      <div class="col-md-3 input-group">
        <input [(ngModel)]="keyItemCategory"
               [typeahead]="itemCategories"
               (typeaheadLoading)="loadItemCategories()"
               (typeaheadOnSelect)="setSelectedItemCategory($event)"
               [typeaheadOptionsLimit]="15"

               typeaheadOptionField="categoryName"
               placeholder="Category"
               autocomplete="off"
               size="16"
               #category="ngModel"
               class="form-control " name="category">
      </div>
      <div class="col-md-3 input-group">
        <input [(ngModel)]="keyBrand"
               [typeahead]="brands"
               (typeaheadLoading)="loadBrands()"
               (typeaheadOnSelect)="setSelectedBrand($event)"
               [typeaheadOptionsLimit]="10"

               typeaheadOptionField="name"
               placeholder="Brand"
               autocomplete="off"
               id="appendedInputButtons" size="16"
               class="form-control m-" name="brand">
      </div>
    </div>
    <table class="table mt-2" id="print-section">
      <thead>
      <tr>
        <th>Barcode</th>
        <th>Item Name</th>
        <th>Item Cost</th>
        <th>Dead Stock Level</th>
        <th>Quantity</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let stock of subStocks,let i = index"
          [class.active]="i === selectedRow">
        <td>{{stock.barcode}}</td>
        <td>{{stock.itemName}}</td>
        <td>{{stock.itemCost}}</td>
        <td>{{stock.deadStockLevel}}</td>
        <td>{{stock.quantity}}</td>
      </tr>
      </tbody>
    </table>
    <div class="row g-3 text-end">
      <div class="col-md-12">
        <button type="button" class="btn btn-danger me-2" printSectionId="print-section" ngxPrint
                [useExistingCss]="true" printTitle="Stock Report">Print
        </button>
      </div>
    </div>
  </div>
</div>

