import {Component, OnInit} from '@angular/core';
import {Item} from "../../../inventory/model/item";
import {Stock} from "../../../inventory/model/stock";
import {Warehouse} from "../../../inventory/model/warehouse";
import {ItemCategory} from "../../../inventory/model/item-category";
import {Brand} from "../../../inventory/model/brand";
import {StockService} from "../../../inventory/service/stock.service";
import {WarehouseService} from "../../../inventory/service/warehouse.service";
import {ItemCategoryService} from "../../../inventory/service/item-category.service";
import {BrandService} from "../../../inventory/service/brand.service";
import {ItemService} from "../../../inventory/service/item.service";
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';

@Component({
  standalone: false,
  selector: 'app-reorder-report',
  templateUrl: './reorder-report.component.html',
  styleUrls: ['./reorder-report.component.css'],
  providers: [DialogService]
})
export class ReorderReportComponent implements OnInit {

  subStocks: Array<Stock> = [];
  warehouses: Array<Warehouse> = [];

  selectedRow: number;

  keyItemSearch: string;
  itemSearched: Array<Item> = [];

  selectedStock = new Stock();
  barcode: string;
  selectedWarehouse: Warehouse;

  keyItemCategory: string;
  itemCategories: Array<ItemCategory> = [];
  catCode = "";
  keyBrand: string;
  brands: Array<Brand> = [];
  brandCode = "";

  threshold: number;
  loading: boolean;
  ref: DynamicDialogRef;

  constructor(
    private stockService: StockService,
    private warehouseService: WarehouseService,
    private itemCategoryService: ItemCategoryService,
    private brandService: BrandService,
    private itemService: ItemService,
    public dialogService: DialogService
  ) {}

  ngOnInit() {
    this.loadWarehouses();
    this.selectedWarehouse = new Warehouse();
    this.findReorderList();
  }

  loadWarehouses() {
    this.warehouseService.findAllActive().subscribe((result: Array<Warehouse>) => {
      if (result.length === 1) {
        this.selectedWarehouse = result[0];
      }
      return this.warehouses = result;
    })
  }

  loadItemCategories() {
    return this.itemCategoryService.findByName(this.keyItemCategory).subscribe((data: Array<ItemCategory>) => {
      return this.itemCategories = data;
    });
  }

  setSelectedItemCategory(event) {
    this.catCode = event.item.code;
    this.findReorderList();
  }

  loadBrands() {
    this.brandService.findByName(this.keyBrand).subscribe((data: Array<Brand>) => {
      return this.brands = data;
    });
  }

  setSelectedBrand(event) {
    this.findReorderList();
  }

  loadItems() {
    return this.itemService.findAllActiveByNameLike(this.keyItemSearch).subscribe((data: Array<Item>) => {
      return this.itemSearched = data;
    });
  }

  loadItemByCode() {
    return this.itemService.findAllByBarcodeLike(this.barcode).subscribe((data: Array<Item>) => {
      return this.itemSearched = data;
    });
  }

  findReorderListByWhCode() {
    this.subStocks = [];
    this.stockService.findReorderListByWarehouse(this.selectedWarehouse.code, this.threshold).subscribe((result: Array<Stock>) => {
      this.subStocks = result;
      this.keyItemSearch = "";
    });
  }

  findReorderList() {
    if (this.threshold > 0) {
      this.loading = true;
      this.subStocks = [];
      this.stockService.findReorderList(this.threshold, this.catCode, this.brandCode).subscribe((result: Array<Stock>) => {
        this.subStocks = result;
        this.keyItemSearch = "";
        this.loading = false;
      });
    }
  }

  setSelectedItem(event) {
    this.selectedStock = event.item;
    this.searchSubStock();
  }

  searchSubStock() {
    this.subStocks = [];
    this.stockService.findByBarcodeAndWarehouse(this.selectedStock.barcode, this.selectedWarehouse.code)
      .subscribe((data: Stock) => {
        this.subStocks.push(data);
        this.keyItemSearch = "";
      });
  }

}
