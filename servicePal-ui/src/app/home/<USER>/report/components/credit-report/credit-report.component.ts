import {Component, OnInit} from '@angular/core';
import {BsModalRef, BsModalService, ModalOptions} from 'ngx-bootstrap/modal';
import {MetaData} from '../../../../core/model/metaData';
import {SalesInvoice} from "../../../trade/model/sales-invoice";
import {SalesInvoiceService} from "../../../trade/service/sales-invoice.service";
import {InvoiceComponent} from "../../../trade/component/invoice/invoice.component";
import {Customer} from "../../../trade/model/customer";
import {CustomerService} from "../../../trade/service/customer.service";
import {Job} from "../../../business/model/job";
import {ManageCustomerComponent} from "../../../trade/component/customer/manage-customer/manage-customer.component";

@Component({
  standalone: false,
  selector: 'app-manage-si',
  templateUrl: './credit-report.component.html',
  styleUrls: ['./credit-report.component.css']
})
export class CreditReportComponent implements OnInit {

  sis: Array<SalesInvoice> = [];
  selectedSi: SalesInvoice;

  keyCustomerSearch: string;
  customerSearchList: Array<Customer> = [];

  keyJobNo: string;

  sDate: Date;
  eDate: Date;

  totalAmount: number;

  selectedRow: number;
  modalRef: BsModalRef;

  constructor(private siService: SalesInvoiceService, private modalService: BsModalService,
              private customerService: CustomerService) {
  }

  ngOnInit() {
    this.selectedSi = new SalesInvoice();
    this.findAllSis();
  }

  findAllSis() {
    this.siService.findAllPendingSiForMonth().subscribe((result: any) => {
      this.sis = result.content;
      this.calculateTotalAmount();
    });
  }

  findPendingSiBetween() {
    this.siService.findAllPendingBetween(this.sDate.toLocaleDateString(),
      this.eDate.toLocaleDateString()).subscribe((result: Array<SalesInvoice>) => {
      this.sis = result;
      this.calculateTotalAmount();
    });
  }

  searchCustomer() {
    this.modalRef = this.modalService.show(ManageCustomerComponent, <ModalOptions>{class: 'modal-xl'});
    this.modalRef.content.disableSetCustomer = false;
    this.modalRef.content.modalRef = this.modalRef;
    this.modalService.onHide.subscribe(event => {
      this.searchByCustomer(this.modalRef.content.customer.id);
      this.keyCustomerSearch = this.modalRef.content.customer.name;
    })
  }

  searchCustomers(event?: any) {
    
    const query = event ? event.query : this.keyCustomerSearch;
    this.customerService.findByNameLike(query).subscribe((result: Array<Customer>) => {
      return this.customerSearchList = result;
    })
  }

  selectSi(si, index) {
    this.selectedRow = index;
    this.selectedSi = si;
  }

  setSelectedCustomer(event) {
    this.searchByCustomer(event.item.nicBr);
  }

  searchByCustomer(customerId) {
    this.siService.findByCustomerAndPending(customerId).subscribe((result: any) => {
      this.sis = result;
      this.calculateTotalAmount();
    });
  }

  calculateTotalAmount() {
    this.totalAmount = 0;
    if (this.sis != null) {
      for (let si of this.sis) {
        if (null != si.balance)
          this.totalAmount = this.totalAmount + si.balance;
      }
    }
  }

  print() {
    this.modalRef = this.modalService.show(InvoiceComponent, <ModalOptions>{class: 'modal-xl'});
    this.modalRef.content.invoiceNo = this.selectedSi.invoiceNo;
    this.modalRef.content.findInvoice();
  }

}
