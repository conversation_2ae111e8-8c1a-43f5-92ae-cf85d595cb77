import { Component, OnInit } from '@angular/core';
import { SalesInvoice } from '../../../trade/model/sales-invoice';
import { SalesInvoiceService } from '../../../trade/service/sales-invoice.service';
import { Customer } from '../../../trade/model/customer';
import { CustomerService } from '../../../trade/service/customer.service';
import { InvoiceComponent } from '../../../trade/component/invoice/invoice.component';
import { ManageCustomerComponent } from '../../../trade/component/customer/manage-customer/manage-customer.component';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';

@Component({
  standalone: false,
  selector: 'app-manage-si',
  templateUrl: './credit-report.component.html',
  styleUrls: ['./credit-report.component.css'],
  providers: [DialogService]
})
export class CreditReportComponent implements OnInit {

  sis: Array<SalesInvoice> = [];
  selectedSi: SalesInvoice;

  keyCustomerSearch: string;
  customerSearchList: Array<Customer> = [];

  keyJobNo: string;

  sDate: Date;
  eDate: Date;

  totalAmount: number;

  selectedRow: number;
  ref: DynamicDialogRef;

  constructor(
    private siService: SalesInvoiceService,
    private customerService: CustomerService,
    public dialogService: DialogService
  ) {}

  ngOnInit() {
    this.selectedSi = new SalesInvoice();
    this.findAllSis();
  }

  findAllSis() {
    this.siService.findAllPendingSiForMonth().subscribe((result: any) => {
      this.sis = result.content;
      this.calculateTotalAmount();
    });
  }

  findPendingSiBetween() {
    this.siService.findAllPendingBetween(this.sDate.toLocaleDateString(),
      this.eDate.toLocaleDateString()).subscribe((result: Array<SalesInvoice>) => {
      this.sis = result;
      this.calculateTotalAmount();
    });
  }

  searchCustomer() {
    this.ref = this.dialogService.open(ManageCustomerComponent, {
      header: 'Select Customer',
      width: '70%',
      contentStyle: { "max-height": "500px", "overflow": "auto" }
    });

    this.ref.onClose.subscribe((customer) => {
      if (customer) {
        this.searchByCustomer(customer.id);
        this.keyCustomerSearch = customer.name;
      }
    });
  }

  searchCustomers() {
    this.customerService.findByNameLike(this.keyCustomerSearch).subscribe((result: Array<Customer>) => {
      return this.customerSearchList = result;
    })
  }

  selectSi(si, index) {
    this.selectedRow = index;
    this.selectedSi = si;
  }

  setSelectedCustomer(event) {
    this.searchByCustomer(event.item.nicBr);
  }

  searchByCustomer(customerId) {
    this.siService.findByCustomerAndPending(customerId).subscribe((result: any) => {
      this.sis = result;
      this.calculateTotalAmount();
    });
  }

  calculateTotalAmount() {
    this.totalAmount = 0;
    if (this.sis != null) {
      for (let si of this.sis) {
        if (null != si.balance)
          this.totalAmount = this.totalAmount + si.balance;
      }
    }
  }

  print() {
    this.dialogService.open(InvoiceComponent, {
      header: 'Invoice',
      width: '70%',
      data: { invoiceNo: this.selectedSi.invoiceNo }
    });
  }
}