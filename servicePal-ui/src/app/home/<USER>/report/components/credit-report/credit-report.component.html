<div class="card">
  <div class="card-header">
    <strong>CREDIT REPORT</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <!--<div class="col-md-2 p-0 pe-1">
        <select type="text" required #duration="ngModel" [class.is-invalid]="duration.invalid && duration.touched"
                class="form-control" id="duration" [(ngModel)]="selectedDuration.id" name="duration"
                (ngModelChange)="filterByDuration()">
          <option *ngFor="let filter of durationFilter" [value]="filter.id">{{filter.value}}</option>
        </select>
      </div>-->

      <div class="col-md-3">
        <div class="input-group">
          <input [(ngModel)]="keyCustomerSearch"
                 [typeahead]="customerSearchList"
                 (typeaheadLoading)="searchCustomers()"
                 (typeaheadOnSelect)="setSelectedCustomer($event)"
                 [typeaheadOptionsLimit]="7"

                 typeaheadOptionField="name"
                 placeholder="Search Customers"
                 autocomplete="off"
                 size="16"
                 class="form-control" name="searchCustomer">
                  <button class="btn btn-success fa fa-search" (click)="searchCustomer()"
                          type="button"></button>
        </div>
      </div>

      <div class="col-md-3">
        <input required #startDate="ngModel" type="text" name="startDate" id="startDate"
               [(ngModel)]="sDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
               class="form-control" placeholder="To Date">
      </div>
      <div class="col-md-3">
        <input required #endDate="ngModel" type="text" name="endDate" id="endDate"
               [(ngModel)]="eDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
               class="form-control" placeholder="From Date" (ngModelChange)="findPendingSiBetween()">
      </div>
    </div>

    <div class="row mt-2">
      <table class="table table-striped">
        <thead align="center">
        <tr>
          <th scope="col">Invoice No</th>
          <th scope="col">Customer Name</th>
          <th scope="col">Date</th>
          <th scope="col">Amount</th>
          <th scope="col">Balance</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let si of sis,let i = index"
            (click)="selectSi(si,i)"
            [class.active]="i === selectedRow" align="center">
          <td>{{si.invoiceNo}}</td>
          <td>{{si.customerName}}</td>
          <td>{{si.date | date:'short': '+530'}}</td>
          <td>{{si.totalAmount | number : '1.2-2'}}</td>
          <td>{{si.balance | number: '1.2-2'}}</td>
        </tr>
        </tbody>
      </table>
      <label class="ms-1 mt-3 fw-bold">Total Amount</label>
      <label class="mt-3 ms-3 fw-bold">{{totalAmount | number : '1.2-2'}}</label>
    </div>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-danger float-end ms-2" (click)="print()">View</button>
      </div>
    </div>
  </div>
</div>
