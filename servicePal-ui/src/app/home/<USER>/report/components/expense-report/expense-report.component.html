<div class="card">
  <div class="card-header">
    <strong>Expense Report</strong>
  </div>
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-2 p-0 pe-1">
        <select type="text" required #duration="ngModel" [class.is-invalid]="duration.invalid && duration.touched"
                class="form-control" id="duration" [(ngModel)]="selectedDuration.id" name="duration"
                (ngModelChange)="filterByDuration()">
          <option *ngFor="let filter of durationFilter" [value]="filter.id">{{filter.value}}</option>
        </select>
      </div>

      <div class="col-md-2 p-0 pe-1">
        <!-- Updated to PrimeNG autocomplete -->
        <p-autocomplete
          [(ngModel)]="keyExpenseCat"
          [suggestions]="expenseCatList"
          (onSelect)="filterByExpenseCat($event)"
          [dropdown]="true"
          field="name"
          placeholder="Expense Category">
        </p-autocomplete>
      </div>

      <div class="col-md-2 p-0 pe-1">
        <!-- Updated to PrimeNG autocomplete -->
        <p-autocomplete
          [(ngModel)]="keyExpenseType"
          [suggestions]="expenseTypeList"
          (onSelect)="filterByExpenseType($event)"
          [dropdown]="true"
          field="name"
          placeholder="Expense Type">
        </p-autocomplete>
      </div>

      <div class="col-md-2 p-0 pe-1">
        <!-- Updated to PrimeNG autocomplete -->
        <p-autocomplete
          [(ngModel)]="keyEmpSearch"
          [suggestions]="empSearchList"
          (onSelect)="filterByEmployee($event)"
          [dropdown]="true"
          field="name"
          placeholder="Responsible Person">
        </p-autocomplete>
      </div>

      <div class="col-md-2 p-0 pe-1">
        <!-- Updated to PrimeNG calendar -->
        <p-calendar
          required
          #startDate="ngModel"
          [(ngModel)]="sDate"
          [showIcon]="true"
          [dateFormat]="'yy-mm-dd'"
          placeholder="Enter Job Date"
          class="form-control">
        </p-calendar>
      </div>
      <div class="col-md-2 p-0">
        <!-- Updated to PrimeNG calendar -->
        <p-calendar
          required
          #endDate="ngModel"
          [(ngModel)]="eDate"
          [showIcon]="true"
          [dateFormat]="'yy-mm-dd'"
          placeholder="Enter Job Date"
          (onSelect)="findExpenses(sDate,eDate)"
          class="form-control">
        </p-calendar>
      </div>
    </div>

    <div class="row g-3 mt-2" id="print-income-div">
      <table class="table table-striped">
        <thead align="center">
        <tr>
          <th scope="col">Date</th>
          <th scope="col">Category</th>
          <th scope="col">Type</th>
          <th scope="col">Responsible Person</th>
          <th scope="col">Amount</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let ex of expenses,let i = index"
            (click)="selectExp(ex,i)"
            [class.active]="i === selectedRow" align="center">
          <td>{{ex.date | date:'short'}}</td>
          <td>{{ex.type.category.name}}</td>
          <td>{{ex.type.name}}</td>
          <td>{{ex.responsiblePerson != null ? ex.responsiblePerson.name : "N/A"}}</td>
          <td>{{ex.amount | number : '1.2-2'}}</td>
        </tr>
        </tbody>
      </table>
      <label class="ms-1 mt-3 fw-bold">Total Amount</label>
      <label class="mt-3 ms-3 fw-bold">{{totalAmount | number : '1.2-2'}}</label>
    </div>
    <div class="row g-3">
      <div class="col-md-12">
        <!-- Updated to PrimeNG paginator -->
        <p-paginator
          [rows]="pageSize"
          [totalRecords]="collectionSize"
          [rowsPerPageOptions]="[10,20,50,100]"
          [(page)]="page"
          (onPageChange)="pageChanged($event)">
        </p-paginator>
      </div>
    </div>
    <div class="row g-3">
      <div class="col-md-12">
        <button type="button" class="btn btn-danger float-end ms-2" printSectionId="print-income-div" ngxPrint
                [useExistingCss]="true" printTitle="Expense Report">Print
        </button>
        <button type="button" class="btn btn-danger float-end ms-2" (click)="viewDetail()">View Detail</button>
      </div>
    </div>
  </div>
</div>
