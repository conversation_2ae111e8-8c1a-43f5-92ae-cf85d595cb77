<div class="card">
  <div class="card-header">
    <strong>EMPLOYEE COMMISSION REPORT</strong>
  </div>
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-4">
        <!-- Updated to PrimeNG autocomplete -->
        <p-autocomplete
          [(ngModel)]="keyEmpSearch"
          [suggestions]="empSearchList"
          (onSelect)="setSelectedEmp($event)"
          [dropdown]="true"
          field="name"
          (completeMethod)="searchEmployee()"
          placeholder="Search Employee Name"
          class="form-control" name="name"
          required
          #name="ngModel"
          [class.is-invalid]="name.invalid && name.touched">
        </p-autocomplete>
      </div>

      <div class="col-md-2">
        <input type="text" placeholder="Job No."
               class="form-control" name="jobNo" id="jobNo" [(ngModel)]="keyJobNo">
      </div>

      <div class="col-md-2">
        <!-- Updated to PrimeNG calendar -->
        <p-calendar
          [(ngModel)]="sDate"
          [dateFormat]="'yy-mm-dd'"
          [showIcon]="true"
          placeholder="Start Date"
          class="form-control" name="startDate">
        </p-calendar>
      </div>

      <div class="col-md-2">
        <!-- Updated to PrimeNG calendar -->
        <p-calendar
          [(ngModel)]="eDate"
          [dateFormat]="'yy-mm-dd'"
          [showIcon]="true"
          placeholder="End Date"
          class="form-control" name="endDate">
        </p-calendar>
      </div>

      <div class="col-md-2">
        <button type="button" class="btn btn-primary w-100" (click)="searchByEmpAndDateBetween()">Search</button>
      </div>
    </div>

    <div class="row g-3 mt-2">
      <div class="table-height">
        <table class="table table-striped">
          <thead align="center">
          <tr>
            <th scope="col">Employee</th>
            <th scope="col">Job No</th>
            <th scope="col">Date</th>
            <th scope="col">Amount</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let com of commissions; let i = index"
              (click)="selectRecord(com.jobNo, i)" [class.active]="i === selectedRow">
            <td>{{com.employee.name}}</td>
            <td>{{com.jobNo}}</td>
            <td>{{com.date | date : 'yyyy-MM-dd'}}</td>
            <td class="text-end">{{com.amount | number : '1.2-2'}}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="row">
      <div class="col-md-8"></div>
      <div class="col-md-2">
        <h5>Total Amount:</h5>
      </div>
      <div class="col-md-2 text-danger">
        <h5>{{totalAmount | number : '1.2-2'}}</h5>
      </div>
    </div>

    <div class="row mt-3" *ngIf="selectedRow != null">
      <div class="col-md-6">
        <button type="button" class="btn btn-success w-100" (click)="loadJob()">View Job Details</button>
      </div>
      <div class="col-md-6">
        <button type="button" class="btn btn-warning w-100" (click)="clearAll()">Clear</button>
      </div>
    </div>
  </div>
</div>