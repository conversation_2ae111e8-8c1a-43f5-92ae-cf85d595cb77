<div class="card">
  <div class="card-header">
    <strong>EMPLOYEE COMMISSION REPORT</strong>
  </div>

  <div class="card-body">
    <div class="row">
      <div class="col-md-3">
        <div class="form-group">
          <input [(ngModel)]="keyJobNo"
                 placeholder="search By Job No"
                 autocomplete="off" size="16"
                 class="form-control" name="invNo" (change)="searchByJobNo()">
        </div>
      </div>
      <div class="col-md-1">
        <button class="btn btn-primary" (click)="searchByJobNo()">Search</button>
      </div>
      <div class="col-md-3">
        <div class="form-group">
          <input [(ngModel)]="keyEmpSearch"
                 [typeahead]="empSearchList"
                 (typeaheadLoading)="searchEmployee()"
                 (typeaheadOnSelect)="setSelectedEmp($event)"
                 [typeaheadOptionsLimit]="7"

                 typeaheadOptionField="name"
                 placeholder="Enter Technician Name"
                 autocomplete="off"
                 size="16"
                 class="form-control" name="searchEmp">
        </div>
      </div>
      <div class="col-md-2">
        <input required #jobDate="ngModel" type="text" name="startDate" id="startDate"
               [(ngModel)]="sDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
               class="form-control" placeholder="Start Date">
      </div>
      <div class="col-md-2">
        <input required #jobDate="ngModel" type="text" name="endDate" id="endDate"
               [(ngModel)]="eDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
               class="form-control" placeholder="End Date">
      </div>
      <div class="col-md-1">
        <button class="btn btn-primary" (click)="searchByEmpAndDateBetween()">Search</button>
      </div>
    </div>
    <table class="table mt-2" id="print-section">
      <thead>
      <tr>
        <th>Date</th>
        <th>Employee Name</th>
        <th>Job No</th>
        <th>Commission</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let commission of commissions; let i = index" (click)="selectRecord(commission.jobNo,i)"
          [class.active]="i === selectedRow">
        <td>{{commission.date}}</td>
        <td>{{commission.empName}}</td>
        <td>{{commission.jobNo}}</td>
        <td>{{commission.amount}}</td>
      </tr>
      </tbody>
    </table>
    <div class="row">
      <div class="col-md-6">
        <div class="form-group">
          <label>Total Amount :</label>
          <label class="ms-2">{{totalAmount}}</label>
        </div>
      </div>
    </div>
    <div class="row float-end">
      <button type="button" class="btn btn-outline-danger me-2" (click)="loadJob()">View Job</button>
      <button type="button" class="btn btn-outline-danger me-2" ngxPrint
              printSectionId="print-section" printTitle="Employee Commission Report">Print
      </button>
    </div>
  </div>
</div>

