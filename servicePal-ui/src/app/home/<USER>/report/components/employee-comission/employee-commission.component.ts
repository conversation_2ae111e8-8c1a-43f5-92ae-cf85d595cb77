import { Component, OnInit } from '@angular/core';
import { EmpCommissionReportService } from '../../service/emp-commission-report.service';
import { Commission } from '../../../business/model/commission';
import { Employee } from '../../../hr/model/employee';
import { EmployeeService } from '../../../hr/service/employee.service';
import { DatePipe } from '@angular/common';
import { JobDetailsComponent } from '../../../business/component/job/job-details/job-details.component';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';

@Component({
  standalone: false,
  selector: 'app-employee-commission',
  templateUrl: './employee-commission.component.html',
  styleUrls: ['./employee-commission.component.css'],
  providers: [DialogService]
})
export class EmployeeCommissionComponent implements OnInit {

  commissions: Array<Commission> = [];

  page;
  pageSize;
  collectionSize;
  maxSize;

  keyEmpId: string;
  keyJobNo: string;
  keyEmpSearch: string;
  empSearchList: Array<Employee>;

  sDate: Date;
  eDate: Date;

  selectedEmpId: string;
  selectedJobNo: string;

  selectedRow: number;
  totalAmount: number;
  ref: DynamicDialogRef;

  constructor(
    private commissionService: EmpCommissionReportService,
    private employeeService: EmployeeService,
    public dialogService: DialogService,
    private datePipe: DatePipe
  ) {}

  ngOnInit(): void {
    this.empSearchList = [];
    this.commissions = [];
    this.findAll();
  }

  findAll() {
    this.commissionService.getAllCommissions().subscribe((result: Array<Commission>) => {
      this.commissions = result;
      this.calculateTotalAmount();
    });
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  selectRecord(jobNo, index) {
    this.selectedRow = index;
    this.selectedJobNo = jobNo;
  }

  searchEmployee() {
    this.employeeService.findByEmployeeNameLike(this.keyEmpSearch).subscribe((result: Array<Employee>) => {
      this.empSearchList = result;
      this.calculateTotalAmount();
    })
  }

  setSelectedEmp(event) {
    this.selectedEmpId = event.item.id;
  }

  searchByJobNo() {
    this.commissionService.findByJobNo(this.keyJobNo).subscribe((result: any) => {
      this.commissions = result;
      this.calculateTotalAmount();
    });
  }

  searchByEmpAndDateBetween() {
    this.commissionService.findByEmpIdAndDateBetween(this.selectedEmpId, this.datePipe.transform(this.sDate, 'yyyy-MM-dd'),
      this.datePipe.transform(this.eDate, 'yyyy-MM-dd')).subscribe((result: any) => {
      this.commissions = result;
      this.calculateTotalAmount();
    });
  }

  calculateTotalAmount() {
    this.totalAmount = 0.0;
    for (let com of this.commissions) {
      if (null != com.amount)
        this.totalAmount = this.totalAmount + com.amount;
    }
  }

  loadJob() {
    this.ref = this.dialogService.open(JobDetailsComponent, {
      header: 'Job Details',
      width: '70%',
      contentStyle: { "max-height": "500px", "overflow": "auto" },
      data: { jobNo: this.selectedJobNo }
    });

    this.ref.onClose.subscribe(() => {
      // Handle modal close if needed
    });
  }
}