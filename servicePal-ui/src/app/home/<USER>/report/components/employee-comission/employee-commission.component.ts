import {Component, OnInit} from '@angular/core';
import {EmpCommissionReportService} from "../../service/emp-commission-report.service";
import {Commission} from "../../../business/model/commission";
import {Employee} from "../../../hr/model/employee";
import {EmployeeService} from "../../../hr/service/employee.service";
import {DatePipe} from "@angular/common";
import {JobDetailsComponent} from "../../../business/component/job/job-details/job-details.component";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";

@Component({
  standalone: false,
  selector: 'app-employee-commission',
  templateUrl: './employee-commission.component.html',
  styleUrls: ['./employee-commission.component.css']
})
export class EmployeeCommissionComponent implements OnInit {
  firstRecordIndex: number = 0;
  itemsPerPage: number = 10;


  commissions: Array<Commission> = [];

  page;
  pageSize;
  collectionSize;
  maxSize;

  keyEmpId: string;
  keyJobNo: string;
  keyEmpSearch: string;

  sDate: Date;
  eDate: Date;

  empSearchList: Array<Employee>;

  selectedEmpId: string;
  selectedJobNo: string;

  selectedRow: number;
  totalAmount: number;
  modalRef: BsModalRef;

  constructor(private commissionService: EmpCommissionReportService, private employeeService: EmployeeService,
              private modalService: BsModalService, private datePipe: DatePipe) {
  }

  ngOnInit(): void {
    this.empSearchList = [];
    this.commissions = [];
    this.findAll();
  }

  findAll() {
    this.commissionService.getAllCommissions().subscribe((result: Array<Commission>) => {
      this.commissions = result;
      this.calculateTotalAmount();
    });
  }

  pageChanged(event: any) {
    this.page = event.page + 1;
    this.findAll();
  }
  onPageChange(event: any) {
    const page = event.page + 1; // PrimeNG paginator is 0-indexed
    this.page = page;
    // Call your existing pagination method
    this.pageChanged({ page: page });
  }


  selectRecord(jobNo, index) {
    this.selectedRow = index;
    this.selectedJobNo = jobNo;
  }

  searchEmployee(event?: any) {
    
    const query = event ? event.query : this.keyEmpSearch;
    this.employeeService.findByEmployeeNameLike(query).subscribe((result: Array<Employee>) => {
      return this.empSearchList = result;
      this.calculateTotalAmount();
    })
  }

  setSelectedEmp(event) {
    this.selectedEmpId = event.item.id;
  }

  searchByJobNo() {
    this.commissionService.findByJobNo(this.keyJobNo).subscribe((result: any) => {
      this.commissions = result;
      this.calculateTotalAmount();
    });
  }

  searchByEmpAndDateBetween() {
    this.commissionService.findByEmpIdAndDateBetween(this.selectedEmpId, this.datePipe.transform(this.sDate, 'yyyy-MM-dd'),
      this.datePipe.transform(this.eDate, 'yyyy-MM-dd')).subscribe((result: any) => {
      this.commissions = result;
      this.calculateTotalAmount();
    });
  }

  calculateTotalAmount() {
    this.totalAmount = 0.0;
    for (let com of this.commissions) {
      if (null != com.amount)
        this.totalAmount = this.totalAmount + com.amount;
    }
  }

  loadJob() {
    this.modalRef = this.modalService.show(JobDetailsComponent, <ModalOptions>{class: 'modal-lg'});
    this.modalRef.content.jobNo = this.selectedJobNo;
    this.modalRef.content.initiate();
  }
}
