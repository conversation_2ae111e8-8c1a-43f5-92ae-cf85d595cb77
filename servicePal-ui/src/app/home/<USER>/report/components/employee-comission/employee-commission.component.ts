import {Component, OnInit} from '@angular/core';
import {EmpCommissionReportService} from "../../service/emp-commission-report.service";
import {Commission} from "../../../business/model/commission";
import {Employee} from "../../../hr/model/employee";
import {EmployeeService} from "../../../hr/service/employee.service";
import {DatePipe} from "@angular/common";
import {JobDetailsComponent} from "../../../business/component/job/job-details/job-details.component";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";

@Component({
  standalone: false,
  selector: 'app-employee-commission',
  templateUrl: './employee-commission.component.html',
  styleUrls: ['./employee-commission.component.css']
})
export class EmployeeCommissionComponent implements OnInit {

  commissions: Array<Commission> = [];

  page;
  pageSize;
  collectionSize;
  maxSize;

  keyEmpId: string;
  keyJobNo: string;
  keyEmpSearch: string;

  sDate: Date;
  eDate: Date;

  empSearchList: Array<Employee>;

  selectedEmpId: string;
  selectedJobNo: string;

  selectedRow: number;
  totalAmount: number;
  modalRef: BsModalRef;

  constructor(private commissionService: EmpCommissionReportService, private employeeService: EmployeeService,
              private modalService: BsModalService, private datePipe: DatePipe) {
  }

  ngOnInit(): void {
    this.empSearchList = [];
    this.commissions = [];
    this.findAll();
  }

  findAll() {
    this.commissionService.getAllCommissions().subscribe((result: Array<Commission>) => {
      this.commissions = result;
      this.calculateTotalAmount();
    });
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  selectRecord(jobNo, index) {
    this.selectedRow = index;
    this.selectedJobNo = jobNo;
  }

  searchEmployee() {
    this.employeeService.findByEmployeeNameLike(this.keyEmpSearch).subscribe((result: Array<Employee>) => {
      return this.empSearchList = result;
      this.calculateTotalAmount();
    })
  }

  setSelectedEmp(event) {
    this.selectedEmpId = event.item.id;
  }

  searchByJobNo() {
    this.commissionService.findByJobNo(this.keyJobNo).subscribe((result: any) => {
      this.commissions = result;
      this.calculateTotalAmount();
    });
  }

  searchByEmpAndDateBetween() {
    this.commissionService.findByEmpIdAndDateBetween(this.selectedEmpId, this.datePipe.transform(this.sDate, 'yyyy-MM-dd'),
      this.datePipe.transform(this.eDate, 'yyyy-MM-dd')).subscribe((result: any) => {
      this.commissions = result;
      this.calculateTotalAmount();
    });
  }

  calculateTotalAmount() {
    this.totalAmount = 0.0;
    for (let com of this.commissions) {
      if (null != com.amount)
        this.totalAmount = this.totalAmount + com.amount;
    }
  }

  loadJob() {
    this.modalRef = this.modalService.show(JobDetailsComponent, <ModalOptions>{class: 'modal-lg'});
    this.modalRef.content.jobNo = this.selectedJobNo;
    this.modalRef.content.initiate();
  }
}
