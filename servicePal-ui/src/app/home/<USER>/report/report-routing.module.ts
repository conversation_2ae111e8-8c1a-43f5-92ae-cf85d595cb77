import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {ReorderReportComponent} from "./components/reorder-report/reorder-report.component";
import {EmployeeCommissionComponent} from "./components/employee-comission/employee-commission.component";
import {IncomeReportComponent} from "./components/income-report/income-report.component";
import {CreditReportComponent} from "./components/credit-report/credit-report.component";
import {StockReportComponent} from "./components/stock-report/stock-report.component";
import {ProfitReportComponent} from "./components/profit-report/profit-report.component";
import {ExpenseReportComponent} from "./components/expense-report/expense-report.component";
import {SummaryReportComponent} from "./components/summary-report/summary-report.component";
import {CashflowComponent} from "./components/cashflow/cashflow.component";
import {StockMovingReportComponent} from "./components/stock-moving-report/stock-moving-report.component";

const routes: Routes = [
  {
    path: 'stock_report',
    component: StockReportComponent
  },
  {
    path: 'stock_movement_report',
    component: StockMovingReportComponent
  },
  {
    path: 'reorder_report',
    component: ReorderReportComponent
  },
  {
    path: 'emp_com_report',
    component: EmployeeCommissionComponent
  },
  {
    path: 'income_report',
    component: IncomeReportComponent
  },
  {
    path: 'credit_report',
    component: CreditReportComponent
  },
  {
    path: 'profit_report',
    component: ProfitReportComponent
  },
  {
    path: 'expense_report',
    component: ExpenseReportComponent
  },
  {
    path: 'summary_report',
    component: SummaryReportComponent
  },
  {
    path: 'cash_flow',
    component: CashflowComponent
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class ReportRoutingModule {
}

export const reportRouteParams = [ReorderReportComponent, EmployeeCommissionComponent, IncomeReportComponent,
  CreditReportComponent, ExpenseReportComponent, SummaryReportComponent, CashflowComponent, StockReportComponent,
  ProfitReportComponent, StockMovingReportComponent];
