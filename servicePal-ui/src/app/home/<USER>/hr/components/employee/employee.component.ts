import {Component, OnInit} from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

import {Designation} from '../../model/designation';
import {SalaryScale} from '../../model/salary-scale';
import {Employee} from '../../model/employee';
import {MetaData} from '../../../../core/model/metaData';
import {Department} from '../../model/department';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';
import {MetaDataService} from '../../../../core/service/metaData.service';
import {EmployeeService} from '../../service/employee.service';
import {NotificationService} from '../../../../core/service/notification.service';
import {DesignationService} from '../../service/designation.service';
import {SalaryScaleService} from '../../service/salary-scale.service';
import {DepartmentService} from '../../service/department.service';
import {NgForm} from '@angular/forms';


@Component({
  standalone: false,
  selector: 'app-employee',
  templateUrl: './employee.component.html',
  styleUrls: ['./employee.component.css']
})
export class EmployeeComponent implements OnInit {
  designation: Designation;
  salarySale: SalaryScale;
  employee = new Employee();
  reportingManager: string;
  bloodGroups: Array<MetaData> = [];
  personType: MetaData = new MetaData();
  isNotDefaultEmployee = true;
  keyEmployee: string;
  employees: Array<Employee> = [];
  designations: Array<Designation> = [];
  salaryScales: Array<SalaryScale> = [];

  departments: Array<Department> = [];
  modalRef: BsModalRef;
  nicAvailability = false;
  epfAvailability = false;
  isView: false;


  constructor(
    public metaDataService: MetaDataService,
              public employeeService: EmployeeService,
              public notificationService: NotificationService,
              private designationService: DesignationService,
              private salaryScaleService: SalaryScaleService,
              private departmentService: DepartmentService,
              private modalService: BsModalService,
    public ref: DynamicDialogRef,
    public config: DynamicDialogConfig
  ) {
  }

  ngOnInit() {
    this.employee = new Employee();
    this.employee.department = new Department();
    this.employee.designation = new Designation();
    this.employee.salaryScale = new SalaryScale();
    this.isView = false;
    this.findAllBloodGroups();
    this.findAllDesignations();
    this.findAllSalaryScale();
    //  this.findAllJobRole();
    this.findAllDepartment();
  }

  gotoTop() {
    window.scroll({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });
  }

  save(form: NgForm) {
    this.employee.active = true;
    this.employeeService.save(this.employee).subscribe((result: any) => {
      if (result != null) {
        form.reset();
        this.notificationService.showSuccess('Employee Saved');
        this.hideModal();
        this.gotoTop();
        this.ngOnInit();
      } else if (result === null) {
        this.notificationService.showError('Employee saving failed');
      }
    });
  }


  findAllBloodGroups() {
    this.metaDataService.findByCategory('Blood Group').subscribe((data: Array<MetaData>) => {
      this.bloodGroups = data;
    });
  }


  findAllDesignations() {
    this.designationService.getAll().subscribe((data: Array<Designation>) => {
      this.designations = data;
    });
  }

  findAllSalaryScale() {
    this.salaryScaleService.findAllSalaryScale().subscribe((data: Array<SalaryScale>) => {
      this.salaryScales = data;
    });
  }

  findAllDepartment() {
    this.departmentService.getAll().subscribe((data: Array<Department>) => {
      this.departments = data;
    });
  }

  loadEmployees(event?: any) {
    
    const query = event ? event.query : this.keyEmployee;
    this.employeeService.findByEmployeeNameLike(query).subscribe((data: Array<Employee>) => {
      return this.employees = data;
    });
  }

  setSelectedEmployee(event) {
    this.employeeService.findByEmployeeId(event.item.id).subscribe((data: Employee) => {
      this.employee.reportingManagerId = data.id;
    });
  }

  onChangeType(event) {

    this.designationService.findById(event.target.value).subscribe((data: Designation) => {
      this.employee.designation = data;
    });
  }

  onChangeSalaryScale(event) {
    this.salaryScaleService.findById(event.target.value).subscribe((data: SalaryScale) => {
      this.employee.salaryScale = data;
    });
  }

  onChangeDepartment(event) {
    this.departmentService.findById(event.target.value).subscribe((data: Department) => {
      this.employee.department = data;
    });
  }

  checkNic() {
      this.employeeService.checkNic(this.employee.nic).subscribe((res: boolean) => {
        this.nicAvailability = res;
      });
  }

  checkEpf() {
      this.employeeService.checkEpf(this.employee.epfNo).subscribe((res: boolean) => {
        this.epfAvailability = res;
      });
  }

  hideModal() {
    this.ref.close();
  }
}
