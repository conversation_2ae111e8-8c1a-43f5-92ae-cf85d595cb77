<div class="card">
  <div class="card-header">
    <strong>Add Employee</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-12">
        <form #manageEmployeeForm="ngForm">
          <div class="row">
            <div class="mb-3 col-md-8">
              <label>Full Name</label>
              <input type="text" required #fname="ngModel" [class.is-invalid]="fname.invalid && fname.touched"
                     class="form-control" id="FirstName" [(ngModel)]="employee.name" name="fname"
                     placeholder="Enter First Name" [disabled]="isView">
              <small class="text-danger" [class.d-none]="fname.valid || fname.untouched">*Full Name is required
              </small>
            </div>
            <div class="mb-3 col-md-4">
              <label>NIC</label>
              <input required #nic="ngModel" type="text" name="nic" id="nic"
                     [(ngModel)]="employee.nic"
                     [class.is-invalid]="nic.invalid && nic.touched"
                     pattern="([0-9]{9}[x|X|v|V]|[0-9]{12})$"
                     (keyup)="checkNic()"
                     class="form-control" placeholder="Enter NIC No" [disabled]="isView">
              <small class="text-danger" [class.d-none]="nic.valid || nic.untouched">* NIC is required
              </small>
              <small *ngIf="nicAvailability" [class.is-none]="true" class="text-danger">* NIC
                already used
              </small>
            </div>
          </div>
          <div class="row">
            <div class="mb-3 col-md-4">
              <label>Joined Date </label>
              <input type="text" required #jdate="ngModel"
                     [class.is-invalid]="jdate.invalid &&jdate.touched"
                     class="form-control" id="jdate" [(ngModel)]="employee.joinedDate" name="jdate"
                     placeholder="Enter Joined Date" autocomplete="off" bsDatepicker
                     [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }" [disabled]="isView">
              <small class="text-danger" [class.d-none]="jdate.valid || jdate.untouched">*Last Name
                is required
              </small>
            </div>
            <div class="mb-3 col-md-4">
              <label>Gender</label>
              <select required #gender="ngModel" [class.is-invalid]="gender.invalid && gender.touched"
                      class="form-control" name="gender" [(ngModel)]="employee.gender" [disabled]="isView">
                <option value="">Gender</option>
                <option value="male">Male</option>
                <option value="female">Female</option>
              </select>
              <small class="text-danger" [class.d-none]="gender.valid || gender.untouched">*Gender is
                required
              </small>
            </div>
            <div class="mb-3 col-md-4">
              <label>Address Line 1 </label>
              <input type="text" required #address1="ngModel" class="form-control" id="address1"
                     name="address1" placeholder="Enter Address line 1"
                     [class.is-invalid]="address1.invalid && address1.touched"
                     [(ngModel)]="employee.addressLine1" [disabled]="isView">
              <small class="text-danger" [class.d-none]="address1.valid || address1.untouched">*Address Line 1 is
                required
              </small>
            </div>
          </div>
          <div class="row">
            <div class="mb-3 col-md-4">
              <label>Address Line 2 </label>
              <input type="text" required #address2="ngModel" class="form-control" id="address2"
                     name="address2" placeholder="Enter Address line 2"
                     [class.is-invalid]="address2.invalid && address2.touched"
                     [(ngModel)]="employee.addressLine2" [disabled]="isView">
              <small class="text-danger" [class.d-none]="address2.valid || address2.untouched">*Address Line 2 is
                required
              </small>
            </div>
            <div class="mb-3 col-md-4">
              <label>Address Line 3 </label>
              <input type="text" required #address3="ngModel" class="form-control" id="address3"
                     name="address3" placeholder="Enter Address line 3"
                     [class.is-invalid]="address3.invalid && address3.touched"
                     [(ngModel)]="employee.addressLine3" [disabled]="isView">
              <small class="text-danger" [class.d-none]="address3.valid || address3.untouched">*Address Line 3 is
                required
              </small>
            </div>
            <div class="mb-3 col-md-4">
              <label>Contact Number</label>
              <input required #contact="ngModel" type="text" class="form-control" id="contact"
                     placeholder="Enter Contact Number" name="contact" pattern="^\d{10}$"
                     [class.is-invalid]="contact.invalid && contact.touched"
                     [(ngModel)]="employee.telephone1" [disabled]="isView">
              <small class="text-danger" [class.d-none]="contact.valid || contact.untouched">*Contact number is
                required
              </small>
            </div>
          </div>

          <div class="row">
            <div class="mb-3 col-md-4">
              <label>Email </label>
              <input type="text" #email="ngModel" class="form-control" id="email" placeholder="Enter Email"
                     name="email" [(ngModel)]="employee.email"
                     pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$"
                     [class.is-invalid]="email.invalid " [disabled]="isView">
              <div *ngIf="email.errors && (email.invalid && email.touched)">
                <small class="text-danger" [class.d-none]="email.valid ">*Email Address is
                  required
                </small>
              </div>

            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label>EPF</label>
                <input type="number" required #epf="ngModel" class="form-control" id="epf" name="epf"
                       [(ngModel)]="employee.epfNo"
                       (keyup)="checkEpf()"
                       placeholder="EPF " [disabled]="isView">
                <small class="text-danger" [class.d-none]="epf.valid || epf.untouched">* EPF is required
                </small>
                <small *ngIf="epfAvailability" [class.is-none]="true" class="text-danger">* EPF
                  already used
                </small>
              </div>
            </div>

            <div class="mb-3 col-md-4">
              <label>Designation</label>
              <select required #desig="ngModel" [class.is-invalid]="desig.invalid && desig.touched"
                      class="form-control" name="designation" (change)="onChangeType($event)"
                      [(ngModel)]="employee.designation.id" [disabled]="isView">
                <option value="">Select</option>
                <option *ngFor="let des of designations" [value]="des.id">{{des.designationName}}</option>
              </select>
              <small class="text-danger" [class.d-none]="desig.valid || desig.untouched">*Designation is
                required
              </small>
            </div>
          </div>

          <div class="row">
            <div class="mb-3 col-md-4">
              <label>Department</label>
              <select required #department="ngModel" [class.is-invalid]="department.invalid && department.touched"
                      class="form-control" name="department" (change)="onChangeDepartment($event)"
                      [(ngModel)]="employee.department.id" [disabled]="isView">
                <option value="">Select</option>
                <option *ngFor="let dep of departments" [value]="dep.id">{{dep.departmentName}}</option>
              </select>
              <small class="text-danger" [class.d-none]="department.valid ||department.untouched">* Department is
                required
              </small>
            </div>
            <div class="mb-3 col-md-4">
              <label>Salary Scale</label>
              <select required #salaryScale="ngModel" [class.is-invalid]="salaryScale.invalid && salaryScale.touched"
                      class="form-control" name="salarySca" (change)="onChangeSalaryScale($event)"
                      [(ngModel)]="employee.salaryScale.id" [disabled]="isView">
                <option value="">Select</option>
                <option *ngFor="let sal of salaryScales" [value]="sal.id">{{sal.name}}</option>
              </select>
              <small class="text-danger" [class.d-none]="salaryScale.valid || salaryScale.untouched">*Salary Scale is
                required
              </small>
            </div>

            <div class="col-md-4">
              <label>Reporting Manager</label>
              <div class="input-group">
                <input [(ngModel)]="keyEmployee"
                       [typeahead]="employees"
                       (typeaheadLoading)="loadEmployees()"
                       (typeaheadOnSelect)="setSelectedEmployee($event)"
                       [typeaheadOptionsLimit]="7"

                       typeaheadOptionField="name"
                       placeholder="Search Employee"
                       autocomplete="off"
                       size="16"
                       class="form-control" name="brand" [disabled]="isView">
              </div>
            </div>
          </div>
          <div class="row float-end">
            <div class="col-sm-5">
              <button class="btn btn-primary" (confirm)="save(manageEmployeeForm)"
                      mwlConfirmationPopover [disabled]="!manageEmployeeForm.valid||isView||epfAvailability">
                Save
              </button>
            </div>
            <div class="col-sm-5">
              <button type="button" class="btn btn-warning" [disabled]="!manageEmployeeForm.reset||isView">Clear
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
