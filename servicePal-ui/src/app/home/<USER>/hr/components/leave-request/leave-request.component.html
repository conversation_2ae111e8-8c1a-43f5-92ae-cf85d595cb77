<div class="card">
  <div class="card-header">
    <strong>Leave Request</strong>
  </div>
  <div class="card-body">
    <form #leaveRequestForm="ngForm">
      <div class="row g-3">
        <div class="mb-3 col-md-4">
          <label>Leave From </label>
          <div class="input-group">
            <span class="input-group-text">
              <i class="fa fa-calendar"></i>
            </span>
            <p-calendar [(ngModel)]="leaveRequest.from" name="from" #from="ngModel"
                        [class.is-invalid]="from.invalid && from.touched"
                        dateFormat="yy-mm-dd" [showIcon]="true"
                        inputStyleClass="form-control" required></p-calendar>
          </div>
          <small class="text-danger" [class.d-none]="from.valid || from.untouched">*From Date is required
          </small>
        </div>
        <div class="mb-3 col-md-4">
          <label>Leave To </label>
          <div class="input-group">
            <span class="input-group-text">
              <i class="fa fa-calendar"></i>
            </span>
            <p-calendar [(ngModel)]="leaveRequest.to" name="to" #to="ngModel"
                        [class.is-invalid]="to.invalid && to.touched"
                        [minDate]="leaveRequest.from"
                        dateFormat="yy-mm-dd" [showIcon]="true"
                        inputStyleClass="form-control" required></p-calendar>
          </div>
          <small class="text-danger" [class.d-none]="to.valid || to.untouched">*To Date is required
          </small>
        </div>
        <div class="col-md-4">
          <label> Employee</label>
          <div class="input-group">
            <p-autoComplete [(ngModel)]="keyEmployee"
                   [suggestions]="employees"
                   (completeMethod)="loadEmployees($event)"
                   (onSelect)="setSelectedEmployee($event)"
                   [dropdown]="true"
                   field="name"
                   placeholder="Search Employee"
                   [style]="{'width':'100%'}"
                   styleClass="form-control"
                   name="emp" required>
            </p-autoComplete>
          </div>
        </div>
      </div>
      <div class="row g-3">
        <div class="mb-3 col-md-4">
          <label>EPF No</label>
          <input type="number" required #epfNo="ngModel"
                 [class.is-invalid]="epfNo.invalid && epfNo.touched"
                 class="form-control" id="epfNo" placeholder="Enter EPF No" name="epf"
                 [(ngModel)]="leaveRequest.employee.epfNo">
          <small class="text-danger" [class.d-none]="epfNo.valid ||epfNo.untouched">*EPF No is
            Required
          </small>
        </div>
        <div class="mb-3 col-md-4">
          <label>Leave Type</label>
          <select required #leaveType="ngModel" [class.is-invalid]="leaveType.invalid && leaveType.touched"
                  class="form-control" name="leaveType" (change)="onChangeType($event)" [(ngModel)]="typeId">
            <option value="">Select</option>
            <option *ngFor="let leave of leaveTypes" [value]="leave.id">{{leave.leaveType}}</option>
          </select>
          <small class="text-danger" [class.d-none]="leaveType.valid || leaveType.untouched">*Leave Type is
            required
          </small>
        </div>
        <div class="mb-3 col-md-4">
          <label>Reason</label>
          <textarea [(ngModel)]="leaveRequest.reason" type="text" rows="2" cols="10"
                    class="form-control" id="reason1" name="reason"
                    placeholder="Enter Reason"></textarea>
        </div>
      </div>
      <div class="row g-3">
        <div class="col-md-4">
          <label>Covering Employee</label>
          <div class="input-group">
            <p-autoComplete [(ngModel)]="coverEmployee"
                   [suggestions]="employees"
                   (completeMethod)="loadCoverEmployees($event)"
                   (onSelect)="setSelectedCoveringEmployee($event)"
                   [dropdown]="true"
                   field="name"
                   placeholder="Search Employee"
                   [style]="{'width':'100%'}"
                   styleClass="form-control"
                   name="cvrEmp" required>
            </p-autoComplete>
          </div>
        </div>
      </div>
      <div class="row g-3 float-end">
        <div class="col-md-4 ms-1">
          <button type="submit" (click)="save(leaveRequestForm);" [disabled]="!leaveRequestForm.form.valid"
                  class="btn btn-primary">Save
          </button>
        </div>
        <div class="col-md-3">
          <button type="button" class="btn btn-warning" [disabled]="!leaveRequestForm.reset">Clear
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
