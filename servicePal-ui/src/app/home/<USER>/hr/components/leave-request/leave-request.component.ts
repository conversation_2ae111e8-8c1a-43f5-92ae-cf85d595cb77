import {Component, OnInit} from '@angular/core';
import {LeaveRequest} from '../../model/leave-request';
import {NotificationService} from '../../../../core/service/notification.service';
import {LeaveRequestService} from '../../service/leave-request.service';
import {Employee} from '../../model/employee';
import {EmployeeService} from '../../service/employee.service';
import {MetaData} from '../../../../core/model/metaData';
import {MetaDataService} from '../../../../core/service/metaData.service';
import {Leave} from '../../model/leave';
import {LeaveService} from '../../service/leave.service';
import {NgForm} from '@angular/forms';

import {BsModalRef, BsModalService} from 'ngx-bootstrap/modal';

@Component({
  standalone: false,
  selector: 'app-leave-request',
  templateUrl: './leave-request.component.html',
})
export class LeaveRequestComponent implements OnInit {
  leaveRequest = new LeaveRequest();
  leaveTypes: Array<Leave> = [];
  employees: Array<Employee> = [];
  leaveType: Leave;
  typeId: string;
  keyEmployee: string;
  coverEmployee: string;
  selectedRow: number;
  setClickedRow: Function;
  collectionSize;
  page;
  pageSize;
  type: MetaData;
  checkAlreadyExist = false;
  modalRef: BsModalRef;

  constructor(private leaveRequestService: LeaveRequestService,
              private addLeaveService: LeaveService,
              private notificationService: NotificationService,
              public metaDataService: MetaDataService,
              public employeeService: EmployeeService,
              private modalService: BsModalService) {
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 10;
    this.leaveRequest = new LeaveRequest();
    this.leaveRequest.employee = new Employee();
    this.findAllLeaveTypes();
    this.findType();
    this.type = new MetaData();
  }

  save(form: NgForm) {
    this.leaveRequest.epf = this.leaveRequest.employee.epfNo;
    this.leaveRequest.type = this.leaveType;
    this.leaveRequestService.save(this.leaveRequest).subscribe(result => {
      this.notificationService.showSuccess(result);
      form.reset();
    });
  }

  onChangeType(event) {
    this.typeId = event.target.value;
    this.addLeaveService.findById(this.typeId).subscribe((data: Leave) => {
      this.leaveType = data;
    });
  }

  findAllLeaveTypes() {
    this.addLeaveService.getAll().subscribe((data: Array<Leave>) => {
      this.leaveTypes = data;
    });
  }

  findType() {
    this.metaDataService.findByValueAndCategory('employee', 'PersonType').subscribe((res: MetaData) => {
      this.type = res;
    });
  }

  loadEmployees() {
    this.employeeService.findByEmployeeNameLike(this.keyEmployee).subscribe((data: Array<Employee>) => {
      return this.employees = data;
    });
  }

  setSelectedEmployee(e) {
    this.employeeService.findByEmployeeId(e.item.id).subscribe((data: Employee) => {
      this.leaveRequest.employee = data;
      this.checkAlreadyRequestedLeave();
    });
  }

  private checkAlreadyRequestedLeave() {
    if (this.leaveRequest.from != null && this.leaveRequest.to != null) {
      this.leaveRequestService.findByDatesAndEmployee(this.leaveRequest.from.toLocaleDateString(),
        this.leaveRequest.to.toLocaleDateString(), this.leaveRequest.employee.id).subscribe((data: boolean) => {
        this.checkAlreadyExist = data;
      });
    }
  }

  loadCoverEmployees() {
    this.employeeService.findByEmployeeNameLike(this.coverEmployee).subscribe((data: Array<Employee>) => {
      return this.employees = data;
    });
  }

  setSelectedCoveringEmployee(e) {

    this.employeeService.findByEmployeeId(e.item.id).subscribe((data: Employee) => {
      if (this.leaveRequest.employee.id === data.id) {
        this.notificationService.showError('You Can\'t Add This Employee');
        this.coverEmployee = null;
      } else {
        this.leaveRequest.coveringEmp = data;
      }
    });
  }

  clear() {
    this.leaveRequest = new LeaveRequest();
  }
}
