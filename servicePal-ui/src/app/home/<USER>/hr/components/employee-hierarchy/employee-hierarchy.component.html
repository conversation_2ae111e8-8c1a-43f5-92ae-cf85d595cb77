<div class="card">
  <div class="card-header">
    <strong>Employee Hierarchy</strong>
  </div>
  <div class="card-body">
    <form #Hierarchy="ngForm">
      <div class="row">
        <div class="col-md-6">
          <div class="mb-3 ">
            <input [(ngModel)]="searchKeyReportingManager"
                   [typeahead]="employees"
                   (typeaheadLoading)="searchLoadReportingManager()"
                   (typeaheadOnSelect)="setSearchSelectedReportingManager($event)"
                   [typeaheadOptionsLimit]="7"

                   typeaheadOptionField="name"
                   placeholder="Search By Reporting Manager"
                   autocomplete="off"
                   size="16"
                   class="form-control" name="searchManager">
          </div>
          <table style="text-align: center" class="table table-bordered table-striped table-sm">
            <thead>
            <tr>
              <th>EPF No</th>
              <th>Employee Name</th>
              <th>Reporting Manager</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let ma of hierarchies,let i = index " (click)="hierarchyDetail(ma);setClickedRow(i)"
                [class.active]="i === selectedRow">
              <td>{{ma.employee.epfNo}}</td>
              <td>{{ma.employee.name}}</td>
              <td>{{ma.reportingManager.name}}</td>
            </tr>
            </tbody>
          </table>
        </div>
        <div class="col-md-6">
          <label> Employee</label>
          <div class="row form-group">
            <div class="input-group col-md-12">
              <input [(ngModel)]="keyEmployee"
                     [typeahead]="employees"
                     (typeaheadLoading)="loadEmployees()"
                     (typeaheadOnSelect)="setSelectedEmployee($event)"
                     [typeaheadOptionsLimit]="7"

                     typeaheadOptionField="name"
                     placeholder="Search Employee"
                     autocomplete="off"
                     size="16"
                     class="form-control" name="employee" required>
            </div>
          </div>
          <label>Reporting Manager</label>
          <div class="row form-group">
            <div class="input-group col-md-12">
              <input [(ngModel)]="keyReportingManager"
                     [typeahead]="employees"
                     (typeaheadLoading)="loadReportingManager()"
                     (typeaheadOnSelect)="setSelectedReportingManager($event)"
                     [typeaheadOptionsLimit]="7"

                     typeaheadOptionField="name"
                     placeholder="Search Reporting Manager"
                     autocomplete="off"
                     size="16"
                     class="form-control" name="reportingManager" required>
            </div>
          </div>
        </div>
      </div>
      <div class="row float-end">
        <div class="col-md-5">
          <button type="button" (click)="save(Hierarchy)"
                  class="btn btn-primary" [disabled]="!Hierarchy.form.valid">Save
          </button>
        </div>
        <div class="col-md-5">
          <button type="button" (click)="clear();Hierarchy.reset();" class="btn btn-warning">Clear
          </button>
        </div>
      </div>
    </form>
    <div class="row ms-3">
      <pagination class="pagination-sm justify-content-center"
        [totalItems]="collectionSize"
        [(ngModel)]="page"
        (pageChanged)="pageChanged($event)">
      </pagination>
    </div>
  </div>
</div>
