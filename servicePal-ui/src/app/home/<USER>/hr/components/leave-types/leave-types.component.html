<div class="card">
  <div class="card-header">
    <strong>Add Leave Types</strong></div>
  <div class="card-body">

    <div class="row">
      <div class="col-md-6">
        <table class="table table-striped table-bordered">
          <thead align="center">
          <tr>
            <th scope="col">Leave</th>
            <th scope="col">No of Leaves</th>
          </tr>
          </thead>
          <tbody>
          <tr style="text-align:center" *ngFor="let addLeave of addLeaves,let i = index " (click)="leaveDetail(addLeave);setClickedRow(i)"
              [class.active]="i === selectedRow" >
            <td>{{addLeave.leaveType}}</td>
            <td>{{addLeave.no}}</td>
          </tr>
          </tbody>
        </table>
        <div class="row">
          <div class="col-xs-12 col-12">
            <pagination class="pagination-sm justify-content-center"
              [totalItems]="collectionSize"
              [(ngModel)]="page"
              (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <form #AddLeave="ngForm">
          <div class="form-group">
            <label>Leave Type </label>
            <input type="text" required #lType="ngModel" [class.is-invalid]="lType.invalid && lType.touched"
                   class="form-control" id="lType"  [(ngModel)]="addLeave.leaveType" name="lType"
                   placeholder="Enter Leave Type " (keyup)="invalidAdd()">
            <div *ngIf="lType.errors && (lType.invalid || lType.touched)">
              <small class="text-danger" [class.d-none]="lType.valid || lType.untouched">*leave type is required
              </small>
            </div>
            <small *ngIf="invalidAddLeave" [class.is-none]="true" class="text-danger">* LeaveName is
              already used
            </small>
          </div>

          <div class="form-group">
          <label>No of Leaves</label>
          <input type="number" required #noL="ngModel" [class.is-invalid]="noL.invalid && noL.touched"
                 class="form-control" id="noL"   [(ngModel)]="addLeave.no" name="noL"
                 placeholder="Enter No of Leaves">
          <div *ngIf="noL.errors && (noL.invalid || noL.touched)">
            <small class="text-danger" [class.d-none]="noL.valid || noL.untouched">*No of leave is required
            </small>

          </div>
          </div>
          <div class="row ms-1">
            <div class="form-check checkbox me-5 col-md-6">
              <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                     [(ngModel)]="addLeave.active">
              <label class="form-check-label" for="check3">Is Active</label>
            </div>
          </div>
          <div class="row float-end mt-2">
            <div class="me-3">
              <button class="btn btn-success" [disabled]="!AddLeave.form.valid||invalidAddLeave" (click)="save(AddLeave)">save</button>
            </div>
            <div class="me-3">
              <button class="btn btn-warning" (click)="Clear()">Clear</button>
            </div>

          </div>
        </form>
      </div>
    </div>
  </div>
</div>
