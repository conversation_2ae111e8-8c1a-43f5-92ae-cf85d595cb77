import {Component, OnInit} from '@angular/core';
import {LeaveService} from '../../service/leave.service';
import {NotificationService} from '../../../../core/service/notification.service';
import {Leave} from '../../model/leave';
import {NgForm} from '@angular/forms';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';


@Component({
  standalone: false,
  selector: 'app-leave-types',
  templateUrl: './leave-types.component.html',
  styleUrls: ['./leave-types.component.css']
})
export class LeaveTypesComponent implements OnInit {

  addLeave = new Leave();
  addLeaves: Array<Leave> = [];
  fullName: string;
  selectedRow: number;
  setClickedRow: Function;
  collectionSize;
  page;
  pageSize;
  invalidAddLeave: boolean;
  modalRef: BsModalRef;


  constructor(public addLeaveService: LeaveService, public notificationService: NotificationService, private modalService: BsModalService) {
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 10;
    this.addLeave = new Leave();
    this.findAll();
    this.invalidAddLeave = false;
    this.addLeave.active = true;
  }

  findAll() {
    this.addLeaveService.findAllPagination(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.addLeaves = data.content;
      this.collectionSize = data.totalPages * 10;
    });
  }

  save(form: NgForm) {
    this.addLeaveService.save(this.addLeave).subscribe(result => {
      this.notificationService.showSuccess(result);
      console.log(result);
      this.findAll();
      form.reset();
      this.ngOnInit();
    });
  }

  invalidAdd() {
    this.addLeaveService.findByLeaveTypeName(this.addLeave.leaveType).subscribe((res: boolean) => {
      this.invalidAddLeave = res;
    });
  }

  leaveDetail(des) {
    this.addLeave = des;
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  Clear() {
    this.addLeave = new Leave();
  }

}
