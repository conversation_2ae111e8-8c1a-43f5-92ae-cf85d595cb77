import {Component, OnInit} from '@angular/core';

import {SalaryScale} from '../../model/salary-scale';
import {SalaryScaleService} from '../../service/salary-scale.service';
import {NotificationService} from '../../../../core/service/notification.service';
import {NgForm} from '@angular/forms';

import {BsModalRef} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';

@Component({
  standalone: false,
  selector: 'app-manage-salary-scale',
  templateUrl: './manage-salary-scale.component.html',
  styleUrls: ['./manage-salary-scale.component.css']
})
export class ManageSalaryScaleComponent implements OnInit {
  firstRecordIndex: number = 0;
  itemsPerPage: number = 10;


  salaryScale = new SalaryScale();
  salaryScaleArray: Array<SalaryScale> = [];
  page = 1;
  collectionSize;
  pageSize = 10;
  selectedRow: number;
  setClickedRow: Function;
  invalidScale: boolean;
  modalRef: BsModalRef;

  constructor(private salaryScaleService: SalaryScaleService,
              private notificationService: NotificationService,
              private modalService: BsModalService) {

  }

  ngOnInit() {
    this.salaryScale = new SalaryScale();
    this.salaryScale.active = true;
    this.findAll();
    this.invalidScale = false;
  }

  save(form: NgForm) {
    this.salaryScaleService.save(this.salaryScale).subscribe(result => {
      this.notificationService.showSuccess(result);
      this.ngOnInit();
      form.reset();
    });
  }

  findAll() {
    this.salaryScaleService.findAll(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.salaryScaleArray = data.content;
      this.collectionSize = data.totalPages * 10;
    });
  }

  pageChanged(event: any) {
    this.page = event.page + 1;
    this.findAll();
  }
  onPageChange(event: any) {
    const page = event.page + 1; // PrimeNG paginator is 0-indexed
    this.page = page;
    // Call your existing pagination method
    this.pageChanged({ page: page });
  }


  salaryDetail(selectedItem: any, index) {
    this.salaryScale = selectedItem;
    this.selectedRow = index;
  }

  clear() {
    this.ngOnInit();
  }

  invalidSalaryScale() {
    this.salaryScaleService.findBySalaryScaleName(this.salaryScale.name).subscribe((res: boolean) => {
      this.invalidScale = res;
    });
  }

}
