import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {HrApiConstants} from '../hr-constants';


@Injectable({
  providedIn: 'root'
})
export class HierarchyService {

  constructor(private http: HttpClient) {
  }

  save(hierarchy) {

    return this.http.post<any>(HrApiConstants.SAVE_HIERARCHY, hierarchy);

  }

  public findAll() {
    return this.http.get(HrApiConstants.GET_HIERARCHY);
  }

  public findAllPagination(page, pageSize) {
    return this.http.get(HrApiConstants.GET_HIERARCHIES, {params: {page, pageSize}});
  }

  public findByManager(id) {
    return this.http.get(HrApiConstants.SEARCH_MANAGER, {params: {id: id}});
  }
}
