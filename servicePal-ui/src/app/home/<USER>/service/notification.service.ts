import {Injectable} from '@angular/core';

import {ToastrService} from 'ngx-toastr';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {

  constructor(private toastrService: ToastrService) {
  }

  public showError(mess) {
    this.toastrService.error(mess, 'ERROR');
  }

  public showSuccess(msg) {
    this.toastrService.success(msg, 'OK');
  }

  public showWarning(msg) {
    this.toastrService.warning(msg, 'WARNING');
  }

}
