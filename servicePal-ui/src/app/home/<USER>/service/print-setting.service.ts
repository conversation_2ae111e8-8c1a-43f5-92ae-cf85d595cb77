import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {ApiConstants} from '../admin-constants';
import {Role} from '../model/role';
import {PrintSetting} from "../model/print-setting";

@Injectable({
  providedIn: 'root'
})
export class PrintSettingService {

  constructor(private http: HttpClient) {
  }

  public findAll() {
    return this.http.get(ApiConstants.GET_PRINT_SETTING);
  }

  public save(printSetting: PrintSetting) {
    return this.http.post<any>(ApiConstants.SAVE_PRINT_SETTING, printSetting);
  }

}
