import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, switchMap, tap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class TypeaheadService {
  constructor(private http: HttpClient) {}

  getSuggestions<T>(
    query: string,
    fetchFunction: (query: string) => Observable<T[]>,
    errorCallback?: (error: any) => void
  ): Observable<T[]> {
    if (!query || query.trim().length === 0) {
      return of([]); // Return empty if query is empty
    }

    return fetchFunction(query).pipe(
      map((data: T[]) => data), // Transform data as needed
      tap(() => {}, (err) => {
        // Handle errors
        if (errorCallback) {
          errorCallback(err);
        } else {
          console.error('Error in typeahead fetch:', err);
        }
      })
    );
  }
}
