import { Injectable } from '@angular/core';
import {CoreApiConstants} from '../core-constants';
import {HttpClient} from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class SequenceService {
  constructor(private http: HttpClient) {
  }

  save(sequence) {
    return this.http.post<any>(CoreApiConstants.SAVE_SEQUENCE, sequence);
  }

  public findAll(page, pageSize) {
    return this.http.get(CoreApiConstants.GET_SEQUENCES, {params: {page: page, pageSize: pageSize}});
  }

  findSequenceByName(name) {
    return this.http.get(CoreApiConstants.FIND_SEQUENCE, {params: {any: name}});
  }

}
