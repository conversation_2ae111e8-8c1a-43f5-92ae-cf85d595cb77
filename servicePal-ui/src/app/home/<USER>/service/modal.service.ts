import { Injectable } from '@angular/core';
import { DialogService, DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

/**
 * ModalService - A service to replace ngx-bootstrap's BsModalService
 * This service provides a similar API to BsModalService but uses PrimeNG's DialogService under the hood
 */
@Injectable({
  providedIn: 'root'
})
export class ModalService {
  private activeModals: DynamicDialogRef[] = [];

  constructor(private dialogService: DialogService) {}

  /**
   * Show a component in a modal dialog
   * @param component The component to show in the modal
   * @param config Configuration options for the modal
   * @returns A reference to the modal
   */
  show(component: any, config?: any): ModalRef {
    const dialogConfig: DynamicDialogConfig = {
      header: config?.title || '',
      width: this.getWidthFromClass(config?.class),
      data: config?.initialState || {},
      dismissableMask: !(config?.ignoreBackdropClick === true),
      styleClass: config?.class || ''
    };

    const dialogRef = this.dialogService.open(component, dialogConfig);
    this.activeModals.push(dialogRef);

    // Create a ModalRef that mimics BsModalRef
    const modalRef = new ModalRef(dialogRef);
    
    // Handle onHide event
    dialogRef.onClose.subscribe(() => {
      this.removeModal(dialogRef);
      if (this.onHideSubject) {
        this.onHideSubject.next(null);
      }
    });

    return modalRef;
  }

  /**
   * Get the width based on the modal class
   * @param className The class name from ngx-bootstrap modal
   * @returns The width for PrimeNG dialog
   */
  private getWidthFromClass(className: string): string {
    if (!className) return '500px';
    
    if (className.includes('modal-sm')) return '300px';
    if (className.includes('modal-lg')) return '800px';
    if (className.includes('modal-xl')) return '1140px';
    
    return '500px';
  }

  /**
   * Remove a modal from the active modals list
   * @param ref The modal reference to remove
   */
  private removeModal(ref: DynamicDialogRef): void {
    const index = this.activeModals.indexOf(ref);
    if (index > -1) {
      this.activeModals.splice(index, 1);
    }
  }

  /**
   * Hide all active modals
   */
  hideAll(): void {
    this.activeModals.forEach(modal => modal.close());
    this.activeModals = [];
  }

  // Mimic BsModalService's onHide event
  private onHideSubject: any;
  get onHide(): any {
    if (!this.onHideSubject) {
      this.onHideSubject = {
        next: (callback: any) => {
          setTimeout(() => {
            if (callback) callback();
          }, 0);
        },
        subscribe: (callback: any) => {
          const subscription = {
            unsubscribe: () => {}
          };
          this.onHideSubject.next = callback;
          return subscription;
        }
      };
    }
    return this.onHideSubject;
  }
}

/**
 * ModalRef - A class to replace ngx-bootstrap's BsModalRef
 * This class provides a similar API to BsModalRef but uses PrimeNG's DynamicDialogRef under the hood
 */
export class ModalRef {
  content: any = {};
  
  constructor(private dialogRef: DynamicDialogRef) {}

  /**
   * Hide the modal
   */
  hide(): void {
    this.dialogRef.close();
  }

  /**
   * Set content property
   * @param key The property name
   * @param value The property value
   */
  setContent(key: string, value: any): void {
    this.content[key] = value;
  }
}
