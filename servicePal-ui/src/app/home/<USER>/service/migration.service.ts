import { Injectable } from '@angular/core';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ConfirmationService } from 'primeng/api';
import { MessageService } from 'primeng/api';

/**
 * Migration service to help transition from ngx-bootstrap to PrimeNG
 * This service provides helper methods and compatibility layers
 */
@Injectable({
  providedIn: 'root'
})
export class MigrationService {

  constructor(
    private dialogService: DialogService,
    private confirmationService: ConfirmationService,
    private messageService: MessageService
  ) { }

  /**
   * Show modal dialog (replacement for BsModalService)
   */
  showModal(component: any, config?: any): DynamicDialogRef {
    return this.dialogService.open(component, {
      header: config?.title || 'Dialog',
      width: config?.width || '70%',
      height: config?.height || 'auto',
      data: config?.initialState || {},
      closable: config?.closable !== false,
      modal: true,
      dismissableMask: config?.backdrop !== 'static'
    });
  }

  /**
   * Show confirmation dialog (replacement for confirm modals)
   */
  showConfirmation(message: string, header: string = 'Confirmation'): Promise<boolean> {
    return new Promise((resolve) => {
      this.confirmationService.confirm({
        message: message,
        header: header,
        icon: 'pi pi-exclamation-triangle',
        accept: () => resolve(true),
        reject: () => resolve(false)
      });
    });
  }

  /**
   * Show toast notification (replacement for toastr)
   */
  showToast(severity: 'success' | 'info' | 'warn' | 'error', summary: string, detail?: string) {
    this.messageService.add({
      severity: severity,
      summary: summary,
      detail: detail,
      life: 3000
    });
  }

  /**
   * Format date for PrimeNG Calendar (replacement for bsDatepicker)
   */
  formatDateForCalendar(date: string | Date): Date | null {
    if (!date) return null;
    if (typeof date === 'string') {
      return new Date(date);
    }
    return date;
  }

  /**
   * Convert ngx-bootstrap modal config to PrimeNG dialog config
   */
  convertModalConfig(bsConfig: any): any {
    return {
      header: bsConfig.title || 'Dialog',
      width: bsConfig.size === 'lg' ? '90%' : bsConfig.size === 'sm' ? '50%' : '70%',
      height: 'auto',
      data: bsConfig.initialState || {},
      closable: bsConfig.keyboard !== false,
      modal: true,
      dismissableMask: bsConfig.backdrop !== 'static'
    };
  }
}
