import {Injectable} from '@angular/core';
import {ApiConstants} from '../admin-constants';
import {HttpClient} from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})

export class PermissionService {

  constructor(private http: HttpClient) {
  }

  findEnabledPermissions(username) {
    return this.http.get(ApiConstants.FIND_ENABLED_PERMISSIONS, {params: {'username': username}});
  }

  getEnabledModules(username) {
    return this.http.get(ApiConstants.GET_ENABLED_MODULES,{params: {'username': username}});
  }

  findPermsByModule(module) {
    return this.http.get(ApiConstants.FIND_PERMISSION_BY_MODULE, {params: {'moduleId': module}});
  }

}
