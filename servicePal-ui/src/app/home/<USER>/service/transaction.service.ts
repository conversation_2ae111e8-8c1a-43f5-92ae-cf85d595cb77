import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {CoreApiConstants} from '../core-constants';

@Injectable({
  providedIn: 'root'
})
export class TransactionService {

  constructor(private http: HttpClient) {
  }

  public findByRangeFilterAndOperator(rangeId: string, operator: string) {
    return this.http.get(CoreApiConstants.GET_TRANSACTIONS_BY_RANGE_FILTER_AND_OPERATOR,
      {params: {rangeId: rangeId, operator: operator}});
  }

  public findByDateRangeAndOperator(sDate: string, eDate: string, operator: string) {
    return this.http.get(CoreApiConstants.GET_TRANSACTIONS_BY_DATE_RANGE_AND_OPERATOR,
      {params: {sDate: sDate, eDate: eDate, operator: operator}});
  }

}
