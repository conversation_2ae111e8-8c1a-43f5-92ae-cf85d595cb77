import {Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {ApiConstants} from "../admin-constants";

@Injectable({
  providedIn: 'root'
})
export class CounterService {

  constructor(private http: HttpClient) {
  }

  findAll() {
    return this.http.get(ApiConstants.FIND_ALL_COUNTERS);
  }

  findByNameLike(counterNo) {
    return this.http.get(ApiConstants.SEARCH_COUNTER_BY_NUMBER, {params: {counterNo: counterNo}})
  }
}
