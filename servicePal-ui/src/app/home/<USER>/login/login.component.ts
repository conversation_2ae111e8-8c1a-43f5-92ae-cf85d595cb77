import {Component, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {AuthenticationService} from './authentication.service';
import {first} from 'rxjs/operators';
import {NotificationService} from '../service/notification.service';

@Component({
  standalone: false,
  selector: 'app-login',
  templateUrl: './login.component.html'
})
export class LoginComponent implements OnInit {

  username: string;
  password: string;
  loading = false;
  submitted = false;
  returnUrl: string;
  error: any;

  constructor(
    private route: ActivatedRoute,
    private router: Router, private notificationService: NotificationService,
    private authenticationService: AuthenticationService) {
  }

  ngOnInit() {

    //  reset login status
    this.authenticationService.logout();

    //  get return url from route parameters or default to '/'
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';
  }


  public login() {
    this.submitted = true;
    this.loading = true;
    this.authenticationService.login(this.username, this.password)
      .pipe(first())
      .subscribe(
        data => {
          this.router.navigate([this.returnUrl]);
        },
        error => {
          this.error = error;
          this.notificationService.showError(this.error.message);
          this.loading = false;
        });
  }

}
