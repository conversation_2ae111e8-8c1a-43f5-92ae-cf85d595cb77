 <div class="app-body mt-5">
   <main class="main d-flex align-items-center">
     <div class="container">
       <div class="row g-3">
         <div class="col-md-4 mx-auto card p-3">
           <div class="card-body">
             <div class="text-center mb-2">
               <img src="img/brand/company-logo.png" class="img-fluid" width="64" height="64">
             </div>
             <form #loginForm="ngForm" (ngSubmit)="login()">
               <div class="text-center">
                 <h2  class="text-center">Viganana ServicePal</h2>
               </div>
               <p class="text-center">Sign In to your account</p>
               <div class="input-group mb-3 ">
                 <span class="input-group-text"><i class="fa fa-user"></i></span>
                 <input type="text" class="form-control" placeholder="Username" autocomplete="username"
                        [(ngModel)]="username" name="username" required>
               </div>
               <div class="input-group mb-4">
                 <span class="input-group-text"><i class="fa fa-lock"></i></span>
                 <input type="password" class="form-control" placeholder="Password" autocomplete="current-password"
                        [(ngModel)]="password" required name="password">
               </div>
               <div class="float-end">
                 <button type="submit" class="btn btn-primary px-4">Login</button>
               </div>
             </form>
           </div>
           <div class="text-center">
             <h6 class="d-inline">Product of </h6>
             <h4 class="d-inline">S-</h4>
             <h5 class="d-inline">OUT </h5>
             <h6 class="d-inline">Solutions Pvt. Ltd</h6>
           </div>
         </div>
       </div>
     </div>
   </main>
 </div>
