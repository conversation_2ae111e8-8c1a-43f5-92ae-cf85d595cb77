import {Brand} from './brand';
import {ItemType} from './item-type';
import {ItemCategory} from './item-category';
import {UOM} from './uom';
import {SubCategory} from './sub-category';
import {Rack} from './rack';

export class Item {

  public id: string;
  public itemCode: string;
  public barcode: string;
  public itemName: string;
  public brand: Brand;
  public model: string;
  public quantity: number;
  public itemType: ItemType;
  public itemCategory: ItemCategory;
  public subCategory: SubCategory;
  public description: string;
  public uom: UOM;
  public rack: Rack;
  public deadStockLevel: number;
  public sellingPrice: number;
  public itemCost: number;
  public serviceCommission: number;
  public commonCode: string;
  public manageStock: boolean;
  public active: boolean;
  public autoInactive: boolean;
  public update: boolean;
}
