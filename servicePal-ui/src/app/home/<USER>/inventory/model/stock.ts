import {Item} from './item';

export class Stock {

  public id: string;

  public quantity: number;

  public itemCode: string;

  public barcode: string;

  public itemName: string;

  public warehouseCode: number;

  public warehouseName: string;

  public sellingPrice: number;

  public itemCost: number;

  public categoryCode: string;

  public brandCode: string;

  public modelCode: string;

  public colorCode: string;

  public commonCode: string;

  public deadStockLevel: number;

  public active: boolean;
}
