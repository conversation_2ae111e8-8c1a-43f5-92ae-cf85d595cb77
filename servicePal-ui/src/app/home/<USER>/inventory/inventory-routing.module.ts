import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {BrandComponent} from './components/brand/brand.component';
import {CreateItemComponent} from './components/Item/create-item/create-item.component';
import {ViewAllItemsComponent} from './components/Item/view-all-items/view-all-items.component';
import {UOMComponent} from './components/uom/uom.component';
import {WarehouseComponent} from './components/warehouse/warehouse.component';
import {ItemTypeComponent} from './components/Item/item-type/item-type.component';
import {ItemCategoryComponent} from './components/Item/item-category/item-category.component';
import {SubCategoryComponent} from './components/Item/sub-category/sub-category.component';
import {RackComponent} from './components/rack/rack.component';
import {BarcodeComponent} from './components/barcode/barcode.component';
import {ViewStockComponent} from './components/stock/view-stock/view-stock.component';
import {AdjustStockComponent} from './components/stock/adjust-stock/adjust-stock.component';
import {ManualStockComponent} from './components/stock/manual-stock/manual-stock.component';
import {TransferStockComponent} from './components/stock/transfer-stock/transfer-stock.component';
import {BatchStockTransferComponent} from './components/stock/batch-stock-transfer/batch-stock-transfer.component';

const routes: Routes = [
  {
    path: 'manual_stock_in',
    component: ManualStockComponent
  },
  {
    path: 'brand',
    component: BrandComponent,
  },
  {
    path: 'create_item',
    component: CreateItemComponent,
  },
  {
    path: 'item_details',
    component: ViewAllItemsComponent,
  },
  {
    path: 'view_main_stock',
    component: ViewStockComponent,
  },
  {
    path: 'uom',
    component: UOMComponent,
  },
  {
    path: 'manage_warehouse',
    component: WarehouseComponent
  },
  {
    path: 'item_type',
    component: ItemTypeComponent,
  },
  {
    path: 'item_category',
    component: ItemCategoryComponent,
  },
  {
    path: 'sub_category',
    component: SubCategoryComponent
  },
  {
    path: 'rack',
    component: RackComponent
  },
  {
    path: 'barcode',
    component: BarcodeComponent
  },
  {
    path: 'adjust_stock',
    component: AdjustStockComponent
  },
  {
    path: 'batch-stock-transfer',
    component: BatchStockTransferComponent
  },
  {
    path: 'adjust_main_stock',
    component: AdjustStockComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class InventoryRoutingModule {
}

export const invRouteParams = [ManualStockComponent, SubCategoryComponent, ItemCategoryComponent,
  ItemTypeComponent, WarehouseComponent, UOMComponent, ViewStockComponent, ViewAllItemsComponent,
  CreateItemComponent, BrandComponent, RackComponent, BarcodeComponent, TransferStockComponent,
  AdjustStockComponent, BatchStockTransferComponent];
