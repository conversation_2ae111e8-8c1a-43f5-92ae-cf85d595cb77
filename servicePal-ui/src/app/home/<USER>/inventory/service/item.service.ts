import {Injectable} from '@angular/core';
import {ApiConstants} from '../inventory-constants';
import {HttpClient} from '@angular/common/http';
import {Item} from '../model/item';

@Injectable({
  providedIn: 'root'
})
export class ItemService {

  constructor(private http: HttpClient) {
  }

  public findAll(page, pageSize) {
    return this.http.get(ApiConstants.GET_ITEM, {params: {page: page, pageSize: pageSize}});
  }

  public save(item: Item) {
    return this.http.post<any>(ApiConstants.SAVE_ITEM, item);
  }

  public delete(id) {
    return this.http.delete(ApiConstants.DELETE_ITEM, {params: {id: id}});
  }

  public findAllActiveByNameLike(search) {
    return this.http.get(ApiConstants.FIND_ACTIVE_BY_NAME_LIKE, {params: {name: search}});
  }

  public findAllServiceActiveByNameLike(search) {
    return this.http.get(ApiConstants.FIND_ACTIVE_SERVICE_BY_NAME_LIKE, {params: {name: search}});
  }

  public findAllServiceActiveByBarcodeLike(search) {
    return this.http.get(ApiConstants.FIND_ACTIVE_SERVICE_BY_BARCODE_LIKE, {params: {barcode: search}});
  }

  public findAllTransportActiveByNameLike(search) {
    return this.http.get(ApiConstants.FIND_ACTIVE_TRANSPORT_BY_NAME_LIKE, {params: {name: search}});
  }

  public findAllTransportActiveByBarcodeLike(search) {
    return this.http.get(ApiConstants.FIND_ACTIVE_TRANSPORT_BY_BARCODE_LIKE, {params: {barcode: search}});
  }

  public findAllByBarcodeLike(search) {
    return this.http.get(ApiConstants.FIND_ALL_BY_BARCODE_LIKE, {params: {barcode: search}});
  }

  public findOneByBarcode(barcode) {
    return this.http.get(ApiConstants.FIND_ONE_BY_BARCODE, {params: {barcode: barcode}});
  }

  public findOneById(search) {
    return this.http.get(ApiConstants.FIND_ONE_BY_ID, {params: {id: search}});
  }

  findAllByNameLike(search) {
    return this.http.get(ApiConstants.FIND_ALL_BY_NAME_LIKE, {params: {name: search}});
  }

  public findAllByCategory(category) {
    return this.http.post<any>(ApiConstants.FIND_BY_CATEGORY, category);
  }

  public findAllByBrand(brand) {
    return this.http.post<any>(ApiConstants.FIND_BY_BRAND, brand);
  }

  findAllByAny(keyItem) {
    return this.http.get(ApiConstants.FIND_ALL_BY_NAME_OR_CODE_LIKE, {params: {name: keyItem}});
  }

}

