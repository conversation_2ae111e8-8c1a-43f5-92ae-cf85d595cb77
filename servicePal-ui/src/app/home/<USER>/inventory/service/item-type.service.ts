import {Injectable} from '@angular/core';
import {ApiConstants} from '../inventory-constants';
import {ItemType} from '../model/item-type';
import {HttpClient} from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})

export class ItemTypeService {


  public findByName (name) {
    return this.http.get(ApiConstants.SEARCH_ITEM_TYPE, {params: {any: name}});
  }

  constructor (private http: HttpClient) {
  }

  public findAll (page, pageSize) {
    return this.http.get(ApiConstants.GET_ITEM_TYPE, {params: {page: page, pageSize: pageSize}});
  }

  public findAllForSelect () {
    return this.http.get(ApiConstants.GET_ITEM_TYPE_FOR_SELECT);
  }

  public save (itemType: ItemType) {
    return this.http.post<any>(ApiConstants.SAVE_ITEM_TYPE, itemType);
  }

  public delete (id) {
    return this.http.delete(ApiConstants.DELETE_ITEM_TYPE, {params: {id: id}});
  }

}
