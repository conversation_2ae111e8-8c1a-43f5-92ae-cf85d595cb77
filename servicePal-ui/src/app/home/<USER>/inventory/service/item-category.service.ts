import {Injectable} from '@angular/core';
import {ApiConstants} from '../inventory-constants';
import {HttpClient} from '@angular/common/http';
import {ItemCategory} from '../model/item-category';
import {map} from 'rxjs/operators';
import {noop, tap} from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ItemCategoryService {

  public findByName (name) {
    return this.http.get<ItemCategory[]>(ApiConstants.SEARCH_ITEM_CATEGORY, {params: {any: name}});
  }


  constructor (private http: HttpClient) {
  }

  public findAll (page, pageSize) {
    return this.http.get(ApiConstants.GET_ITEM_CATEGORY, {params: {page: page, pageSize: pageSize}});
  }

  public save (itemCategory: ItemCategory) {
    return this.http.post<any>(ApiConstants.SAVE_ITEM_CATEGORY, itemCategory);
  }

  public delete (id) {
    return this.http.delete(ApiConstants.DELETE_ITEM_CATEGORY, {params: {id: id}});
  }

  findAllCategories() {
    return this.http.get(ApiConstants.FIND_ITEM_CATEGORY);
  }

  findById(id) {
    return this.http.get(ApiConstants.GET_CATEGORY, {params: {id: id}});
  }

  checkDuplicate(code){
    return this.http.get(ApiConstants.CHECK_DUPLICATE_CATEGORY_CODE, {params: {code: code}})
  }

  findByCode(code){
    return this.http.get(ApiConstants.FIND_ITEM_CATEGORY_BY_CODE, {params: {code: code}});
  }
}
