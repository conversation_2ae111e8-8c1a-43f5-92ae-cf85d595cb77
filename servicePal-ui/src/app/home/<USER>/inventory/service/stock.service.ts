import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {ApiConstants} from '../inventory-constants';
import {TransferStock} from "../model/transfer-stock";
import {BatchStockTransfer} from "../model/batch-stock-transfer";
import {Stock} from "../model/stock.model";

@Injectable({
  providedIn: 'root'
})
export class StockService {

  constructor(private http: HttpClient) {
  }

  public findAll(page, pageSize) {
    return this.http.get(ApiConstants.GET_MAIN_STOCK, {params: {page: page, pageSize: pageSize}});
  }

  public findAllByItemCode(itemCode) {
    return this.http.get(ApiConstants.FIND_MAIN_STOCK_BY_ITEM_CODE, {params: {itemCode}});
  }

  addStockManual(manualStock) {
    return this.http.post<any>(ApiConstants.ADD_MAIN_STOCK_MANUAL, manualStock);
  }

  public findAllByWarehouse(warehouseCode, page, pageSize) {
    return this.http.get(ApiConstants.FIND_BY_WAREHOUSE, {
      params: {
        warehouseCode: warehouseCode,
        page: page, pageSize: pageSize
      }
    });
  }

  findByBarcodeAndWarehouse(item, warehouseCode) {
    return this.http.get(ApiConstants.FIND_ONE_STOCK_BY_BARCODE_AND_WAREHOUSE, {
      params: {
        barcode: item,
        warehouseCode: warehouseCode
      }
    });
  }

  adjustStock(actualQuantity, stockId, remark) {
    return this.http.get(ApiConstants.ADJUST_MAIN_STOCK, {params: {actualQuantity, stockId, remark}});
  }

  public searchByWhAndBarcodeLike(barcode) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_BARCODE_LIKE, {params: {barcode: barcode}});
  }

  public findByBarcodeAndUserWarehouse(barcode) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_BARCODE_AND_USER_WH, {params: {barcode: barcode}});
  }

  public searchByWhAndNameLike(name) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_ITEM_NAME_LIKE, {params: {name: name}});
  }

  stockTransfer(transferStock: TransferStock) {
    return this.http.post<any>(ApiConstants.STOCK_TRANSFER, transferStock);
  }

  batchStockTransfer(batchStockTransfer: BatchStockTransfer) {
    return this.http.post<any>(ApiConstants.BATCH_STOCK_TRANSFER, batchStockTransfer);
  }

  findOneByBarcodeAndWarehouse(item, warehouseCode) {
    return this.http.get(ApiConstants.FIND_ONE_STOCK_BY_BARCODE_AND_WAREHOUSE, {
      params: {
        barcode: item,
        warehouseCode: warehouseCode
      }
    });
  }

  findByBarcodeAndWarehouseLike(barcode, warehouseCode) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_BARCODE_AND_WAREHOUSE_LIKE, {
      params: {
        barcode: barcode,
        warehouseCode: warehouseCode
      }
    });
  }

  findByNameAndWarehouseLike(name, warehouseCode) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_NAME_AND_WAREHOUSE_LIKE, {
      params: {
        name: name,
        warehouseCode: warehouseCode
      }
    });
  }

  findStockByItemCategoryAndWh(catCode, whCode) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_ITEM_CATEGORY_WH_CODE, {
      params: {
        catCode: catCode,
        whCode: whCode
      }
    })
  }

  findStockByBrandAndWh(brandCode, whCode) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_BRAND_WH_CODE, {
      params: {
        brandCode: brandCode,
        whCode: whCode
      }
    })
  }

  findStockByItemCodeAndWarehouse(itemCode, warehouseCode) {
    return this.http.get(ApiConstants.FIND_STOCK_BY_ITEM_CODE_AND_WAREHOUSE, {
      params: {
        itemCode: itemCode,
        warehouseCode: warehouseCode
      }
    })
  }

  findStockSummary() {
    return this.http.get(ApiConstants.FIND_STOCK_SUMMARY);
  }

  public findReorderListByWarehouse(warehouseCode, threshold) {
    return this.http.get(ApiConstants.FIND_REORDER_LIST_BY_WAREHOUSE, {
      params: {
        warehouseCode: warehouseCode,
        threshold: threshold
      }
    });
  }

  public findReorderList(threshold, catCode, brandCode) {
    return this.http.get(ApiConstants.FIND_REORDER_LIST, {
      params: {
        threshold: threshold,
        catCode: catCode,
        brandCode: brandCode
      }
    });
  }

  bulkUpdateStocks(stocks: Array<Stock>) {
    return this.http.post<any>(ApiConstants.BULK_UPDATE_STOCKS, stocks);
  }

}
