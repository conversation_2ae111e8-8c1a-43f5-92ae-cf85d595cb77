import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {ApiConstants} from '../inventory-constants';

@Injectable({
  providedIn: 'root'
})

export class RackService {

  constructor(private http: HttpClient) {
  }

  save(rack) {
    return this.http.post<any>(ApiConstants.SAVE_RACK, rack);
  }

  public findAll(page, pageSize) {
    return this.http.get(ApiConstants.GET_RACKS, {params: {page: page, pageSize: pageSize}});
  }
  public findAllRack() {
    return this.http.get(ApiConstants.GET_All_RACKS);
  }

  findById (id) {
    return this.http.get(ApiConstants.FIND_RACK, {params: {id: id}});
  }

  public findByRackNo(name) {
    return this.http.get(ApiConstants.SEARCH_RACK, {params: {any: name}});
  }



}
