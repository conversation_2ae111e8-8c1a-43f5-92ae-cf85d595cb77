import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {ApiConstants} from '../inventory-constants';

@Injectable({
  providedIn: 'root'
})

export class BrandService {

  constructor (private http: HttpClient) {
  }

  save (brand) {
    return this.http.post<any>(ApiConstants.SAVE_BRAND, brand);
  }

  public findAll (page, pageSize) {
    return this.http.get(ApiConstants.GET_BRANDS, {params: {page: page, pageSize: pageSize}});
  }

  public findByName (name) {
    return this.http.get(ApiConstants.SEARCH_BRAND, {params: {any: name}});
  }

  checkDuplicate(code){
    return this.http.get(ApiConstants.CHECK_DUPLICATE_BRAND_CODE, {params: {code: code}})
  }

  findByCode(code){
    return this.http.get(ApiConstants.FIND_BRAND_BY_CODE, {params: {code: code}});
  }

  findAllBySupplier(id){
    return this.http.get(ApiConstants.FIND_BRAND_BY_SUPPLIER, {params: {id: id}});
  }

}
