<div class="card">
  <div class="card-header">
    <strong>MANAGE RACKS</strong>
  </div>
  <div class="card-body">
    <div class="row g-4">
      <!-- Left Section -->
      <div class="col-md-6">
        <div class="input-group mb-3">
          <input [(ngModel)]="keyRack"
                 [typeahead]="racks"
                 (typeaheadLoading)="loadRacks()"
                 (typeaheadOnSelect)="setSelectedRack($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadOptionField="name"
                 placeholder="Search Rack"
                 autocomplete="off"
                 size="16"
                 required
                 class="form-control" name="rack">
        </div>
        <table class="table table-striped">
          <thead>
          <tr>
            <th>Rack No</th>
            <th>Description</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let rack of racks, let i=index" (click)="rackDetail(rack, i)"
              [class.active]="i === selectedRow">
            <td>{{rack.rackNo}}</td>
            <td>{{rack.description}}</td>
          </tr>
          </tbody>
        </table>
        <div class="row g-3">
          <div class="col-12">
            <pagination class="pagination-sm justify-content-center"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page"
                        [boundaryLinks]="true"

                        (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>

      <!-- Right Section -->
      <div class="col-md-6">
        <form #manageRackForm="ngForm">
          <div class="mb-3">
            <label for="rNo" class="form-label">Rack No</label>
            <input type="text" required #rNo="ngModel"
                   [class.is-invalid]="rNo.invalid && rNo.touched"
                   class="form-control" id="rNo"
                   [(ngModel)]="rack.rackNo" name="rNo"
                   placeholder="Rack No">
            <div *ngIf="rNo.errors && (rNo.invalid || rNo.touched)">
              <small class="text-danger">*Rack No is required</small>
            </div>
          </div>

          <div class="mb-3">
            <label for="Description" class="form-label">Description</label>
            <textarea required #Description="ngModel"
                      [class.is-invalid]="Description.invalid && Description.touched"
                      class="form-control" id="Description"
                      [(ngModel)]="rack.description" name="Description"
                      placeholder="Description"></textarea>
            <div *ngIf="Description.errors && (Description.invalid || Description.touched)">
              <small class="text-danger">*Description is required</small>
            </div>
          </div>

          <div class="form-check mb-3">
            <input class="form-check-input" id="check3" name="check3"
                   type="checkbox" [(ngModel)]="rack.active">
            <label class="form-check-label" for="check3">Is Active</label>
          </div>

          <div class="d-flex justify-content-end gap-3">
            <button type="button" class="btn btn-success"
                    (click)="saveRack(); manageRackForm.reset()"
                    [disabled]="!manageRackForm.form.valid">Save</button>
            <button type="button" class="btn btn-primary active"
                    [disabled]="!manageRackForm.form.valid"
                    (click)="updateRack()">Update</button>
            <button type="button" class="btn btn-warning" (click)="clear()">Clear</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

