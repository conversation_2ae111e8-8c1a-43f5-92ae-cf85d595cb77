import {Component, OnInit} from '@angular/core';
import {Rack} from '../../model/rack';
import {RackService} from '../../service/rack.service';
import {NotificationService} from '../../../../core/service/notification.service';


@Component({
  standalone: false,
  selector: 'app-rack',
  templateUrl: './rack.component.html',
  styleUrls: ['./rack.component.css']
})
export class RackComponent implements OnInit {
  firstRecordIndex: number = 0;
  itemsPerPage: number = 10;


  rack = new Rack();
  racks: Array<Rack> = [];
  setClickedRow: Function;
  selectedRow: number;
  keyRack: string;
  collectionSize;
  page;
  pageSize;

  constructor (private rackService: RackService,
               private notificationService: NotificationService) {
  }

  ngOnInit () {
    this.page = 1;
    this.pageSize = 8;
    this.rack = new Rack();
    this.rack.active = true;
    this.findAll();
  }

  saveRack () {
    this.rackService.save(this.rack).subscribe(result => {
      console.log(result);
      this.notificationService.showSuccess(result);
      this.ngOnInit();
    }, error => {
      console.log(error);
    });


  }

  loadRacks () {
    this.rackService.findByRackNo(this.keyRack).subscribe((data: Array<Rack>) => {
      return this.racks = data;
    });
  }

  findAll () {
    this.rackService.findAll(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.racks = data.content;
      this.collectionSize = data.totalPages * 8;
      console.log(this.racks);
    });
  }

  pageChanged(event: any) {
    this.page = event.page + 1;
    this.findAll();
  }
  onPageChange(event: any) {
    const page = event.page + 1; // PrimeNG paginator is 0-indexed
    this.page = page;
    // Call your existing pagination method
    this.pageChanged({ page: page });
  }


  rackDetail (rack,index) {
    this.rack = rack;
    this.selectedRow = index;
  }

  setSelectedRack (event) {
    this.rack = event.item;
  }


  updateRack () {
    this.saveRack();
  }

  clear () {
    this.rack = new Rack();
  }


}
