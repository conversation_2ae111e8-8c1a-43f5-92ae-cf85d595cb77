import {Component, OnInit} from '@angular/core';
import {Rack} from '../../model/rack';
import {RackService} from '../../service/rack.service';
import {NotificationService} from '../../../../core/service/notification.service';


@Component({
  standalone: false,
  selector: 'app-rack',
  templateUrl: './rack.component.html',
  styleUrls: ['./rack.component.css']
})
export class RackComponent implements OnInit {

  rack = new Rack();
  racks: Array<Rack> = [];
  setClickedRow: Function;
  selectedRow: number;
  keyRack: string;
  collectionSize;
  page;
  pageSize;

  constructor (private rackService: RackService,
               private notificationService: NotificationService) {
  }

  ngOnInit () {
    this.page = 1;
    this.pageSize = 8;
    this.rack = new Rack();
    this.rack.active = true;
    this.findAll();
  }

  saveRack () {
    this.rackService.save(this.rack).subscribe(result => {
      console.log(result);
      this.notificationService.showSuccess(result);
      this.ngOnInit();
    }, error => {
      console.log(error);
    });


  }

  loadRacks () {
    this.rackService.findByRackNo(this.keyRack).subscribe((data: Array<Rack>) => {
      return this.racks = data;
    });
  }

  findAll () {
    this.rackService.findAll(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.racks = data.content;
      this.collectionSize = data.totalPages * 8;
      console.log(this.racks);
    });
  }

  pageChanged (event: any) {
    this.page = event.page;
    this.findAll();
  }

  rackDetail (rack,index) {
    this.rack = rack;
    this.selectedRow = index;
  }

  setSelectedRack (event) {
    this.rack = event.item;
  }


  updateRack () {
    this.saveRack();
  }

  clear () {
    this.rack = new Rack();
  }


}
