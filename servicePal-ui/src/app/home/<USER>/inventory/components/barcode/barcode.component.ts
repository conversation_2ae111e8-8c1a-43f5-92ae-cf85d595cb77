import {Component, OnInit} from '@angular/core';

@Component({
  standalone: false,
  selector: 'app-barcode',
  templateUrl: './barcode.component.html',
  styleUrls: ['./barcode.component.css']
})
export class BarcodeComponent implements OnInit {

  barcode = '';
  itemName = '';
  price = '';
  noOfCopies: 1;
  elementType = 'svg';
  format = 'CODE128';
  lineColor = '#000000';
  width = 2;
  height = 60;
  displayValue = true;
  fontOptions = '';
  font = 'monospace';
  textAlign = 'center';
  textPosition = 'bottom';
  textMargin = 1;
  fontSize = 12;
  background = '#ffffff';
  margin = 0;

  /*  marginTop = 1;
    marginBottom = 1;
    marginLeft = 1;
    marginRight = 1;*/

  constructor() {
  }

  ngOnInit() {
  }

}
