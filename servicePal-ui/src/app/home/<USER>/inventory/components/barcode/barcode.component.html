<div class="card">
  <div class="card-header">
    <strong>Barcoding</strong>
  </div>
  <div class="card-body">
    <div class="modal-body row g-3">
      <div class="col-md-5 form-group">
        <label>BarCode</label>
        <input class="form-control" [(ngModel)]="barcode" disabled>
        <label>No Of Copies</label>
        <input class="form-control" [(ngModel)]="noOfCopies">
      </div>
      <!--      <div class="col-md-7" id="print-section" style="padding: 15px">-->
      <div class="col-md-7" id="print-section">
        <table style="width:100%">
          <th style="width:100%; text-align: center">{{itemName}}</th>
          <tr style="width:100%; text-align: center">
            <td>
              <ngx-barcode6 [bc-value]="barcode" [bc-display-value]="true"
                           [bc-format]="format" [bc-line-color]="lineColor"
                           [bc-width]="width" [bc-height]="height" [bc-display-value]="displayValue"
                           [bc-font-options]="fontOptions" [bc-font]="font" [bc-text-align]="textAlign"
                           [bc-text-position]="textPosition" [bc-text-margin]="textMargin" [bc-font-size]="fontSize"
                           [bc-background]="background" [bc-margin]="margin">
              </ngx-barcode6>
            </td>
          </tr>
          <tr style="width:100%; text-align: center">
            <td>
              Rs. {{price}}
            </td>
          </tr>
        </table>
      </div>
    </div>
    <div class="row g-3 mt-3 align-content-end d-block me-2">
      <button class="btn btn-success float-end" printSectionId="print-section" ngxPrint>
        Print
      </button>
    </div>
  </div>
</div>
