import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';

import {Item} from '../../../model/item';
import {ItemService} from '../../../service/item.service';
import {NotificationService} from '../../../../../core/service/notification.service';
import {StockService} from '../../../service/stock.service';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {Warehouse} from "../../../model/warehouse";
import {WarehouseService} from "../../../service/warehouse.service";
import {TransferStock} from "../../../model/transfer-stock";
import {Stock} from "../../../model/stock";
import {BatchStockTransfer} from "../../../model/batch-stock-transfer";

@Component({
  standalone: false,
  selector: 'app-transfer-stock',
  templateUrl: './batch-stock-transfer.component.html',
  styleUrls: ['./batch-stock-transfer.component.css']
})
export class BatchStockTransferComponent implements OnInit {

  @ViewChild('quantityInput') qty: ElementRef;
  @ViewChild('barcodeInput') barcode: ElementRef;

  batchStockTransfer: BatchStockTransfer;

  availableQtySource: number;
  transferringQty: number;
  availableQtyTarget: number;

  keyBarcodeSearch: string;
  itemSearchList: Array<Item> = [];
  warehouses: Array<Warehouse> = [];
  transferStock: TransferStock;

  modalRef: BsModalRef;

  warehouse: Warehouse;
  sourceWarehouse: Warehouse;
  targetWarehouse: Warehouse;
  item: Item;

  selectedRow: number;

  constructor(private itemService: ItemService,
              private notificationService: NotificationService,
              private stockService: StockService,
              private warehouseService: WarehouseService) {
  }

  ngOnInit(): void {
    this.batchStockTransfer = new BatchStockTransfer();
    this.batchStockTransfer.batch = [];
    this.transferStock = new TransferStock();
    this.warehouse = new Warehouse();
    this.loadWarehouses();
  }

  searchItems(event?: any) {
    
    const query = event ? event.query : this.keyBarcodeSearch;
    this.itemService.findAllByBarcodeLike(query).subscribe((result: Array<Item>) => {
      return this.itemSearchList = result;
    })
  }

  setSelectedItem(event) {
    this.transferStock.barcode = event.item.barcode;
    this.transferStock.itemName = event.item.itemName;
    this.transferStock.itemCode = event.item.itemCode;
    this.qty.nativeElement.focus();
  }

  save() {
    this.transferStock.transferQty = this.transferringQty;
    this.stockService.batchStockTransfer(this.batchStockTransfer).subscribe(result => {
      if (result.code === 200) {
        this.notificationService.showSuccess(result.message);
        this.ngOnInit();
      } else {
        this.notificationService.showError(result.message);
        console.log(result.data)
      }
    })
  }

  getSubStockAvlQty() {
    this.transferStock.targetWarehouseName = this.targetWarehouse.name;
    this.transferStock.targetWarehouseCode = this.targetWarehouse.code;
    this.stockService.findOneByBarcodeAndWarehouse(this.transferStock.barcode,
      this.transferStock.targetWarehouseCode).subscribe(
      (result: Stock) => {
        this.availableQtyTarget = (null === result ? 0 : result.quantity);
      })
  }

  setSource() {
    this.transferStock.sourceWarehouseName = this.sourceWarehouse.name;
    this.transferStock.sourceWarehouseCode = this.sourceWarehouse.code;
  }

  loadWarehouses(event?: any) {
    this.warehouseService.findAll().subscribe((data: Array<Warehouse>) => {
      this.warehouses = data;
    })
  }

  addToList() {
    if (this.transferStock.transferQty) {
      this.batchStockTransfer.batch.push(this.transferStock);
      this.transferStock = new TransferStock();
      this.transferStock.sourceWarehouseCode = this.sourceWarehouse.code;
      this.transferStock.sourceWarehouseName = this.sourceWarehouse.name;
      this.transferStock.targetWarehouseName = this.targetWarehouse.name;
      this.transferStock.targetWarehouseCode = this.targetWarehouse.code;
      this.keyBarcodeSearch = "";
      this.barcode.nativeElement.focus();
    }
  }

  selectedRec(tl, i) {
    this.selectedRow = i;
  }

  remove() {
    if (this.selectedRow) {
      this.batchStockTransfer.batch.splice(this.selectedRow, 1);
    }
  }

}
