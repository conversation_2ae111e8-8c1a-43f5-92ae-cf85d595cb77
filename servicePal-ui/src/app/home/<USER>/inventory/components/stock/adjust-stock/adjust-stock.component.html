<div class="card">
  <div class="card-header">
    <strong>Adjust Stock</strong>
  </div>
  <div class="card-body">
    <form #adjustStkForm="ngForm" (ngSubmit)="adjustStock(adjustStkForm);">
      <div class="row">
        <div class="mb-3 col-md-6">
          <label>Item Code</label>
          <input required #itemCode="ngModel" type="text" name="itemCode" id="itemCode"
                 [(ngModel)]="stock.itemCode"
                 [class.is-invalid]="itemCode.invalid && itemCode.touched"
                 class="form-control" disabled>
        </div>
        <div class="mb-3 col-md-6">
          <label>Item Name</label>
          <input required #itemName="ngModel" type="text" name="itemName" id="itemName"
                 [(ngModel)]="stock.itemName"
                 [class.is-invalid]="itemName.invalid && itemName.touched"
                 class="form-control" disabled>
        </div>
        <div class="mb-3 col-md-6">
          <label>Available Quantity</label>
          <input required #avlQty="ngModel" type="text" name="avlQty" id="avlQty"
                 [(ngModel)]="stock.quantity"
                 [class.is-invalid]="avlQty.invalid && avlQty.touched"
                 class="form-control" disabled>
        </div>
        <div class="mb-3 col-md-6">
          <label>Actual Quantity</label>
          <input required #actualQty="ngModel" type="number" name="actualQty" id="actualQty"
                 [(ngModel)]="actualQuantity"
                 [class.is-invalid]="actualQty.invalid && actualQty.touched"
                 class="form-control" required>
        </div>
        <div class="mb-3 col-md-12">
          <label>Remark</label>
          <input required #remarkTxt="ngModel" type="text" name="remarkTxt" id="remarkTxt"
                 [(ngModel)]="remark"
                 [class.is-invalid]="remarkTxt.invalid && remarkTxt.touched"
                 class="form-control" required>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12 text-end">
          <button class="btn btn-success" type="submit" [disabled]="!adjustStkForm.valid">Adjust</button>
        </div>
      </div>
    </form>
  </div>
</div>
