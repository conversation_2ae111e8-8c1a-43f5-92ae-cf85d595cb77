import { Component, OnInit } from '@angular/core';
import { Item } from '../../../model/item';
import { NotificationService } from '../../../../../core/service/notification.service';
import { StockService } from '../../../service/stock.service';
import { PurchaseInvoice } from '../../../../trade/model/purchase-invoice';
import { PurchaseInvoiceRecord } from '../../../../trade/model/purchase-invoice-record';
import { ItemService } from '../../../service/item.service';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { CreateItemComponent } from '../../../../inventory/components/item/create-item.component';
import { NgForm } from '@angular/forms';

@Component({
  standalone: false,
  selector: 'app-manual-stock',
  templateUrl: './manual-stock.component.html',
  styleUrls: ['./manual-stock.component.css'],
  providers: [DialogService]
})
export class ManualStockComponent implements OnInit {

  items: Array<Item> = [];
  purchaseInvoice = new PurchaseInvoice();
  piRecords: Array<PurchaseInvoiceRecord> = [];
  piRecord = new PurchaseInvoiceRecord();
  itemCode: string;
  itemName: string;
  totalBuyingPrice: number;
  percentageValue: number;
  isDuplicated: boolean = false;
  selectedRow: number;
  keyBarcode: string;
  ref: DynamicDialogRef;

  constructor(
    private itemService: ItemService,
    private notificationService: NotificationService,
    private stockService: StockService,
    public dialogService: DialogService
  ) {}

  ngOnInit() {
    this.purchaseInvoice = new PurchaseInvoice();
    this.purchaseInvoice.purchaseInvoiceRecords = [];
  }

  openCreateItemModal() {
    this.ref = this.dialogService.open(CreateItemComponent, {
      header: 'Create Item',
      width: '70%',
      contentStyle: { "max-height": "500px", "overflow": "auto" }
    });
    
    this.ref.onClose.subscribe(() => {
      // Handle modal close if needed
    });
  }

  loadItemByCode() {
    this.itemService.findAllByBarcodeLike(this.keyBarcode).subscribe((data: Array<Item>) => {
      this.items = data;
    });
  }

  loadItems() {
    this.itemService.findAllByNameLike(this.itemName).subscribe((data: Array<Item>) => {
      this.items = data;
    });
  }

  setSellingPrice() {
    this.piRecord.sellingPrice = Math.round((this.piRecord.itemCost * 100 /
      (100 - this.percentageValue)) * 100) / 100;
  }

  setItemCost() {
      this.piRecord.itemCost = Math.round((this.piRecord.sellingPrice -
        (this.piRecord.sellingPrice * this.percentageValue / 100)) * 100) / 100;
  }

  saveStock(form: NgForm) {
    this.purchaseInvoice.purchaseInvoiceRecords = this.piRecords;
    this.purchaseInvoice.totalAmount = this.totalBuyingPrice;
    this.purchaseInvoice.payment = this.totalBuyingPrice; //  assume already paid for these items
    this.stockService.addStockManual(this.purchaseInvoice).subscribe((data: any) => {
      if (data.code === 200)
        this.notificationService.showSuccess(data.message);
      else
        this.notificationService.showError(data.message);
      
      this.clearTable();
      form.resetForm();
    });
  }

  addEntry() {
    let duplicateFound = false;
    let index;
    if (this.piRecords.length > 0) {
      for (let idx in this.piRecords) {
        this.totalBuyingPrice = this.totalBuyingPrice + this.piRecord.itemCost;
        if (this.piRecords[idx].item.id === this.piRecord.item.id) {
          duplicateFound = true;
          index = idx;
        }
      }
      if (duplicateFound) {
        this.piRecords[index].quantity = this.piRecords[index].quantity + this.piRecord.quantity;
      } else {
        this.piRecords.push(this.piRecord);
      }
    } else {
      this.totalBuyingPrice = this.piRecord.itemCost;
      this.piRecords.push(this.piRecord);
    }
    this.piRecord = new PurchaseInvoiceRecord();
  }

  removeStockRecord() {
    if (this.selectedRow != null) {
      this.piRecords.splice(this.selectedRow, 1);

    } else {
      this.notificationService.showError('select inventory record first');
    }
  }

  clearTable() {
    this.piRecords.length = 0;
  }

  setSelectedItem(event) {
    this.piRecord.item = new Item();
    this.piRecord.item.id = event.item.id;
    this.piRecord.itemName = event.item.itemName;
    this.piRecord.barcode = event.item.barcode;
    this.piRecord.itemCode = event.item.itemCode;
    this.piRecord.itemCost = event.item.itemCost;
    this.piRecord.sellingPrice = event.item.sellingPrice;
    this.keyBarcode = event.item.barcode;
    this.itemName = event.item.itemName;
  }

  selectEntry(entry, index) {
    this.selectedRow = index;
    this.piRecord = entry;
    this.itemCode = entry.itemCode;
    this.itemName = entry.itemName;
  }
}
