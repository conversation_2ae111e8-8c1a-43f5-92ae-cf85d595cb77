import {Component, OnInit, TemplateRef} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';
import {Item} from '../../../model/item';
import {ItemService} from '../../../service/item.service';
import {NotificationService} from '../../../../../core/service/notification.service';
import {StockService} from '../../../service/stock.service';
import {PurchaseInvoice} from '../../../../trade/model/purchase-invoice';
import {PurchaseInvoiceRecord} from '../../../../trade/model/purchase-invoice-record';
import {NgForm} from "@angular/forms";

@Component({
  standalone: false,
  selector: 'app-manual-stock',
  templateUrl: './manual-stock.component.html',
  styleUrls: ['./manual-stock.component.css']
})
export class ManualStockComponent implements OnInit {

  items: Array<Item> = [];
  modalRef: BsModalRef;
  purchaseInvoice = new PurchaseInvoice();
  piRecords: Array<PurchaseInvoiceRecord> = [];
  piRecord = new PurchaseInvoiceRecord();
  itemCode: string;
  itemName: string;
  totalBuyingPrice: number;
  percentageValue: number;
  isDuplicated: boolean = false;
  selectedRow: number;
  keyBarcode: string;

  constructor(private modalService: BsModalService,
              private itemService: ItemService,
              private notificationService: NotificationService,
              private stockService: StockService
  ) {
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template);
  }

  ngOnInit() {
    this.purchaseInvoice = new PurchaseInvoice();
    this.purchaseInvoice.purchaseInvoiceRecords = [];
  }

  loadItemByCode(event?: any) {
    
    const query = event ? event.query : this.keyBarcode;
    this.itemService.findAllByBarcodeLike(query).subscribe((data: Array<Item>) => {
      this.items = data;
    });
  }

  loadItems() {
    this.itemService.findAllByNameLike(this.itemName).subscribe((data: Array<Item>) => {
      return this.items = data;
    });
  }

  setSellingPrice() {
    this.piRecord.sellingPrice = Math.round((this.piRecord.itemCost * 100 /
      (100 - this.percentageValue)) * 100) / 100;
  }

  setItemCost() {
      this.piRecord.itemCost = Math.round((this.piRecord.sellingPrice -
        (this.piRecord.sellingPrice * this.percentageValue / 100)) * 100) / 100;
  }

  saveStock(form: NgForm) {
    this.purchaseInvoice.purchaseInvoiceRecords = this.piRecords;
    this.purchaseInvoice.totalAmount = this.totalBuyingPrice;
    this.purchaseInvoice.payment = this.totalBuyingPrice; //  assume already paid for these items
    this.stockService.addStockManual(this.purchaseInvoice).subscribe((data: any) => {
      if (data.code === 200) {
        this.notificationService.showSuccess(data.message);
        form.resetForm();
      } else {
        this.notificationService.showError(data.message);
      }
      this.clearTable();
    });
  }

  addEntry() {
    let duplicateFound = false;
    let index;
    if (this.piRecords.length > 0) {
      for (let idx in this.piRecords) {
        this.totalBuyingPrice = this.totalBuyingPrice + this.piRecord.itemCost;
        if (this.piRecords[idx].item.id === this.piRecord.item.id) {
          duplicateFound = true;
          index = idx;
        }
      }
      if (duplicateFound) {
        this.piRecords[index].quantity = this.piRecords[index].quantity + this.piRecord.quantity;
      } else {
        this.piRecords.push(this.piRecord);
      }
    } else {
      this.totalBuyingPrice = this.piRecord.itemCost;
      this.piRecords.push(this.piRecord);
    }
    this.piRecord = new PurchaseInvoiceRecord();
  }

  removeStockRecord() {
    if (this.selectedRow != null) {
      this.piRecords.splice(this.selectedRow, 1);

    } else {
      this.notificationService.showError('select inventory record first');
    }
  }

  clearTable() {
    this.piRecords.length = 0;
  }

  setSelectedItem(event) {
    this.piRecord.item = new Item();
    this.piRecord.item.id = event.item.id;
    this.piRecord.itemName = event.item.itemName;
    this.piRecord.barcode = event.item.barcode;
    this.piRecord.itemCode = event.item.itemCode;
    this.piRecord.itemCost = event.item.itemCost;
    this.piRecord.sellingPrice = event.item.sellingPrice;
    this.keyBarcode = event.item.barcode;
    this.itemName = event.item.itemName;
  }

  selectEntry(entry, index) {
    this.selectedRow = index;
    this.piRecord = entry;
    this.itemCode = entry.itemCode;
    this.itemName = entry.itemName;
  }
}
