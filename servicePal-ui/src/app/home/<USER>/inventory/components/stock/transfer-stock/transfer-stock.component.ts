import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {NgForm} from '@angular/forms';
import {Item} from '../../../model/item';
import {ItemService} from '../../../service/item.service';
import {NotificationService} from '../../../../../core/service/notification.service';
import {StockService} from '../../../service/stock.service';
import {BsModalRef, BsModalService} from 'ngx-bootstrap/modal';
import {Warehouse} from "../../../model/warehouse";
import {WarehouseService} from "../../../service/warehouse.service";
import {TransferStock} from "../../../model/transfer-stock";
import {Stock} from "../../../model/stock";

@Component({
  standalone: false,
  selector: 'app-transfer-stock',
  templateUrl: './transfer-stock.component.html',
  styleUrls: ['./transfer-stock.component.css']
})
export class TransferStockComponent implements OnInit {

  availableQtySource: number;
  transferringQty: number;
  availableQtyTarget: number;

  keyBarcodeSearch: string;
  itemSearchList: Array<Item> = [];
  warehouses: Array<Warehouse> = [];
  transferStock: TransferStock;
  modalRef: BsModalRef;
  transItemName: string;
  warehouse: Warehouse;
  item: Item;
  page;
  pageSize;

  constructor(private itemService: ItemService,
              private notificationService: NotificationService,
              private stockService: StockService,
              private modalService: BsModalService,
              private warehouseService: WarehouseService) {
  }

  ngOnInit(): void {
    this.transferStock = new TransferStock();
    this.warehouse = new Warehouse();
    this.loadWarehouses();
  }

  searchItems(event?: any) {
    
    const query = event ? event.query : this.keyBarcodeSearch;
    this.itemService.findAllByBarcodeLike(query).subscribe((result: Array<Item>) => {
      return this.itemSearchList = result;
    })
  }

  setSelectedItem(event) {
    this.transferStock.barcode = event.item.barcode;
    this.transferStock.itemName = event.item.itemName;
    this.transferStock.itemCode = event.item.itemCode;
  }

  save(form: NgForm) {
    this.transferStock.transferQty = this.transferringQty;
    this.stockService.stockTransfer(this.transferStock).subscribe(result => {
      if (result.code === 200) {
        this.notificationService.showSuccess(result.message);
        form.reset();
        this.modalRef.hide();
      } else {
        this.notificationService.showError(result.message);
        console.log(result.data)
      }
    })
  }

  getSubStockAvlQty() {
    this.stockService.findOneByBarcodeAndWarehouse(this.transferStock.barcode,
      this.transferStock.targetWarehouseCode).subscribe(
      (result: Stock) => {
        this.availableQtyTarget = (null === result ? 0 : result.quantity);
      })
  }

  loadWarehouses(event?: any) {
    this.warehouseService.findAll().subscribe((data: Array<Warehouse>) => {
      this.warehouses = data;
    })
  }
}
