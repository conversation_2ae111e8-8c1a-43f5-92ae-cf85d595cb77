import {Component, OnInit} from '@angular/core';
import {Item} from '../../../model/item';
import {ItemService} from '../../../service/item.service';
import {Warehouse} from '../../../model/warehouse';
import {WarehouseService} from '../../../service/warehouse.service';
import {TransferStockComponent} from "../transfer-stock/transfer-stock.component";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {Stock} from "../../../model/stock";
import {StockService} from "../../../service/stock.service";
import {AdjustStockComponent} from "../adjust-stock/adjust-stock.component";
import {User} from "../../../../../admin/model/user";
import {ItemCategory} from "../../../model/item-category";
import {Brand} from "../../../model/brand";
import {ItemCategoryService} from "../../../service/item-category.service";
import {BrandService} from "../../../service/brand.service";

@Component({
  standalone: false,
  selector: 'app-view-sub-stock',
  templateUrl: './view-stock.component.html',
  styleUrls: ['./view-stock.component.css']
})
export class ViewStockComponent implements OnInit {
  firstRecordIndex: number = 0;
  itemsPerPage: number = 10;


  page;
  pageSize;
  collectionSize;
  displayPagination = true;

  subStocks: Array<Stock> = [];
  warehouses: Array<Warehouse> = [];

  selectedRow: number;

  keyItemSearch: string;
  itemSearched: Array<Item> = [];

  selectedStock = new Stock();
  barcode: string;
  selectedWarehouse: Warehouse;

  keyItemCategory: string;
  itemCategories: Array<ItemCategory> = [];
  selectedItemCategory = new ItemCategory();
  keyBrand: string;
  brands: Array<Brand> = [];
  selectedBrand = new Brand();

  user: User;

  modalRef: BsModalRef;

  loading: boolean;

  permittedRoles: Array<string> = ['ADMIN', 'MANAGER'];
  isPermitted: boolean;

  constructor(private stockService: StockService, private warehouseService: WarehouseService,
              private itemCategoryService: ItemCategoryService,
              private brandService: BrandService,
              private itemService: ItemService, private modalService: BsModalService) {
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 9;
    this.loadWarehouses();
    this.selectedWarehouse = new Warehouse();
    this.user = JSON.parse(localStorage.getItem('currentUser')).user;
    this.loadUserWarehouse();
    this.isPermitted = false;
    this.checkUserRole();
  }

  checkUserRole() {
    for (let role of this.user.userRoles) {
      if (this.permittedRoles.includes(role.name)) {
        this.isPermitted = true;
      }
    }
  }

  loadUserWarehouse() {
    this.warehouseService.findByCode(this.user.warehouseCode).subscribe((result: Warehouse) => {
      this.selectedWarehouse = result;
      this.filterByWarehouse();
    })
  }

  pageChanged(event: any) {
    this.page = event.page + 1;
    this.filterByWarehouse();
  }
  onPageChange(event: any) {
    const page = event.page + 1; // PrimeNG paginator is 0-indexed
    this.page = page;
    // Call your existing pagination method
    this.pageChanged({ page: page });
  }


  loadWarehouses(event?: any) {
    this.warehouseService.findAllActive().subscribe((result: Array<Warehouse>) => {
      if (result.length === 1) {
        this.selectedWarehouse = result[0];
      }
      return this.warehouses = result;
    })
  }

  loadItemCategories() {
    return this.itemCategoryService.findByCode(this.keyItemCategory).subscribe((data: Array<ItemCategory>) => {
      return this.itemCategories = data;
    });
  }

  setSelectedItemCategory(event) {
    this.loading = true;
    this.stockService.findStockByItemCategoryAndWh(event.item.code, this.selectedWarehouse.code).subscribe((data: Array<Stock>) => {
      this.subStocks = data;
      this.collectionSize = 1;
      this.keyItemCategory = "";
      this.selectedItemCategory = new ItemCategory();
      this.loading = false;
    });
  }

  loadBrands(event?: any) {
    
    const query = event ? event.query : this.keyBrand;
    this.brandService.findByCode(query).subscribe((data: Array<Brand>) => {
      return this.brands = data;
    });
  }

  setSelectedBrand(event) {
    this.loading = true;
    this.stockService.findStockByBrandAndWh(event.item.code, this.selectedWarehouse.code).subscribe((data: Array<Stock>) => {
      this.subStocks = data;
      this.collectionSize = 1;
      this.keyBrand = "";
      this.selectedBrand = new Brand();
      this.loading = false;
    });
  }

  selectStockRecord(stock, index) {
    this.selectedStock = stock;
    this.selectedRow = index;
  }

  loadItems() {
    return this.itemService.findAllActiveByNameLike(this.keyItemSearch).subscribe((data: Array<Item>) => {
      return this.itemSearched = data;
    });
  }

  loadItemByCode(event?: any) {
    
    const query = event ? event.query : this.barcode;
    return this.itemService.findAllByBarcodeLike(query).subscribe((data: Array<Item>) => {
      return this.itemSearched = data;
    });
  }

  setSelectedItem(event) {
    this.selectedStock = event.item;
    this.searchSubStock();
  }

  clearTable() {
    this.subStocks = [];
  }

  filterByWarehouse() {
    this.subStocks = [];
    this.displayPagination = true;
    this.stockService.findAllByWarehouse(this.selectedWarehouse.code, this.page - 1, this.pageSize).subscribe((result: any) => {
      this.subStocks = result.content;
      this.collectionSize = result.totalPages * 10;
      this.keyItemSearch = "";
      this.selectedStock = new Stock();
    });
  }

  searchSubStock() {
    this.subStocks = [];
    this.stockService.findOneByBarcodeAndWarehouse(this.selectedStock.barcode, this.selectedWarehouse.code)
      .subscribe((data: Stock) => {
        this.subStocks.push(data);
        this.keyItemSearch = "";
        this.selectedStock = new Stock();
      });
  }

  adjustStock() {
    this.modalRef = this.modalService.show(AdjustStockComponent, <ModalOptions>{class: 'modal-lg'});
    this.modalRef.content.stock = this.selectedStock;
    this.modalRef.content.modalRef = this.modalRef;
  }

  searchByBarcodeToTable() {
    if (this.barcode.length > 2) {
      this.subStocks = [];
      this.displayPagination = false;
      this.stockService.findByBarcodeAndWarehouseLike(this.barcode, this.selectedWarehouse.code)
        .subscribe((data: Array<Stock>) => {
          this.subStocks = data;
        });
    }
  }

  searchByNameToTable() {
    if (this.keyItemSearch.length > 2) {
      this.subStocks = [];
      this.displayPagination = false;
      this.stockService.findByNameAndWarehouseLike(this.keyItemSearch, this.selectedWarehouse.code)
        .subscribe((data: Array<Stock>) => {
          this.subStocks = data;
        });
    }
  }

  transferStock() {
    this.modalRef = this.modalService.show(TransferStockComponent, <ModalOptions>{class: 'modal-lg'});
    this.modalRef.content.availableQtySource = this.selectedStock.quantity;
    this.modalRef.content.transferStock.sourceWarehouseCode = this.selectedWarehouse.code;
    this.modalRef.content.transferStock.itemCode = this.selectedStock.itemCode;
    this.modalRef.content.transferStock.itemName = this.selectedStock.itemName;
    this.modalRef.content.transferStock.barcode = this.selectedStock.barcode;
    this.modalRef.content.keyBarcodeSearch = this.selectedStock.barcode;
    this.modalRef.content.modalRef = this.modalRef;
    this.modalService.onHide.subscribe(result => {
      this.filterByWarehouse();
    })
  }

}
