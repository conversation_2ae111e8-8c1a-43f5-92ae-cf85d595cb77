import {Component, OnInit} from '@angular/core';
import {NgForm} from '@angular/forms';
import {NotificationService} from '../../../../../core/service/notification.service';
import {StockService} from '../../../service/stock.service';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {Stock} from "../../../model/stock";

@Component({
  standalone: false,
  selector: 'app-adjust-main-stock',
  templateUrl: './adjust-stock.component.html',
  styleUrls: ['./adjust-stock.component.css']
})
export class AdjustStockComponent implements OnInit {

  stock: Stock;

  actualQuantity: number;
  remark: string;
  modalRef: BsModalRef;

  constructor(private notificationService: NotificationService, private stockService: StockService) {
  }

  ngOnInit(): void {
    this.stock = new Stock();
  }

  adjustStock(form: NgForm) {
    this.stockService.adjustStock(this.actualQuantity, this.stock.id, this.remark).subscribe((result: any) => {
      if (result.code === 200) {
        this.notificationService.showSuccess(result.message);
        form.reset();
        this.modalRef.hide();
      } else {
        this.notificationService.showError(result.message);
        console.log(result.data)
      }
    })
  }

}
