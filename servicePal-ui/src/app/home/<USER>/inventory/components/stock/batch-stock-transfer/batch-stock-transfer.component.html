<div class="card">
  <div class="card-header">
    <strong>Batch Stock Transfer</strong>
  </div>
  <div class="card-body">
    <form #transferStkForm="ngForm">
      <div class="row">
        <div class="mb-3 col-md-2">
          <label>Source</label>
          <select class="form-control" required #sourceWh="ngModel"
                  [class.is-invalid]="sourceWh.invalid && sourceWh.touched" name="sourceWh"
                  [(ngModel)]="sourceWarehouse" (change)="setSource()">
            <option [value]="undefined" disabled>-Select-</option>
            <option *ngFor="let wh of warehouses" [ngValue]="wh">{{wh.name}}</option>
          </select>
        </div>
        <div class="mb-3 col-md-2">
          <label>Target</label>
          <select class="form-control" required #selectedWh="ngModel" (change)="getSubStockAvlQty()"
                  [class.is-invalid]="selectedWh.invalid && selectedWh.touched" name="selectedWh"
                  [(ngModel)]="targetWarehouse">
            <option [value]="undefined" disabled>-Select-</option>
            <option *ngFor="let wh of warehouses" [ngValue]="wh">{{wh.name}}</option>
          </select>
        </div>
        <div class="mb-3 col-md-2">
          <label>Barcode</label>
          <input [(ngModel)]="keyBarcodeSearch"
                 [typeahead]="itemSearchList"
                 (typeaheadLoading)="searchItems()"
                 (typeaheadOnSelect)="setSelectedItem($event)"
                 [typeaheadOptionsLimit]="7"

                 typeaheadOptionField="barcode"
                 autocomplete="off"
                 placeholder="Select the Item" #barcodeInput
                 type="text" required #code="ngModel" [class.is-invalid]="code.invalid && code.touched"
                 class="form-control" name="code" id="code">
        </div>

        <div class="mb-3 col-md-3">
          <label>Item Name</label>
          <input required #itemName="ngModel" type="text" name="itemName" id="itemName"
                 [(ngModel)]="transferStock.itemName"
                 [class.is-invalid]="itemName.invalid && itemName.touched"
                 class="form-control" disabled>
        </div>

        <div class="mb-3 col-md-2">
          <label>Transferring Qty</label>
          <input required #trnsQty="ngModel" type="number" name="trnsQty" id="trnsQty" #quantityInput
                 [(ngModel)]="transferStock.transferQty" [attr.required]="transferringQty <= availableQtySource"
                 [class.is-invalid]="trnsQty.invalid && trnsQty.touched" [defaultValue]="1"
                 class="form-control" required (keyup.enter)="addToList()">
        </div>
        <div class="mb-3 col-md-1 mt-2">
          <button type="button" class="btn btn-primary mt-4" (click)="addToList()">Add</button>
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-md-12">
          <div class="table-height">
            <table class="table table-striped">
              <thead align="center">
              <tr>
                <th scope="col">Source</th>
                <th scope="col">Target</th>
                <th scope="col">Barcode</th>
                <th scope="col">Name</th>
                <th scope="col">Quantity</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let tl of batchStockTransfer.batch,let i = index"
                  (click)="selectedRec(tl,i)"
                  [class.active]="i === selectedRow" align="center">
                <td>{{tl.sourceWarehouseName}}</td>
                <td>{{tl.targetWarehouseName}}</td>
                <td>{{tl.barcode}}</td>
                <td>{{tl.itemName}}</td>
                <td>{{tl.transferQty}}</td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12 text-end">
          <button class="btn btn-danger me-2" type="button" (confirm)="remove()" mwlConfirmationPopover>Remove</button>
          <button class="btn btn-success" type="button" (confirm)="save()" mwlConfirmationPopover>Transfer</button>
        </div>
      </div>
    </form>
  </div>
</div>
