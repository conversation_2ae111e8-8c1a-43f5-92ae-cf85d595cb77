<div class="card">
  <div class="card-header">
    <strong>Transfer Stock</strong>
  </div>
  <div class="card-body">
    <form #transferStkForm="ngForm" (ngSubmit)="save(transferStkForm);">
      <div class="row">
        <div class="mb-3 col-md-3">
          <label>Source Warehouse</label>
          <select class="form-control" required #sourceWh="ngModel"
                  [class.is-invalid]="sourceWh.invalid && sourceWh.touched" name="sourceWh"
                  [(ngModel)]="transferStock.sourceWarehouseCode" disabled>
            <option [value]="undefined" disabled>-Select-</option>
            <option *ngFor="let wh of warehouses" [value]="wh.code">{{wh.name}}</option>
          </select>
        </div>

        <div class="mb-3 col-md-3">
          <label>Barcode</label>
          <input required #barcode="ngModel" type="text" name="barcode" id="barcode"
                 [(ngModel)]="transferStock.barcode"
                 [class.is-invalid]="barcode.invalid && barcode.touched"
                 class="form-control" disabled>
        </div>

        <div class="mb-3 col-md-3">
          <label>Item Name</label>
          <input required #itemName="ngModel" type="text" name="itemName" id="itemName"
                 [(ngModel)]="transferStock.itemName"
                 [class.is-invalid]="itemName.invalid && itemName.touched"
                 class="form-control" disabled>
        </div>
        <div class="mb-3 col-md-3">
          <label>Available Qty in Source</label>
          <input required #avlQty="ngModel" type="text" name="avlQty" id="avlQty"
                 [(ngModel)]="availableQtySource"
                 [class.is-invalid]="avlQty.invalid && avlQty.touched"
                 class="form-control" disabled>
        </div>
        <div class="mb-3 col-md-3">
          <label>Target Warehouse</label>
          <select class="form-control" required #selectedWh="ngModel" (change)="getSubStockAvlQty()"
                  [class.is-invalid]="selectedWh.invalid && selectedWh.touched" name="selectedWh"
                  [(ngModel)]="transferStock.targetWarehouseCode">
            <option [value]="undefined" disabled>-Select-</option>
            <option *ngFor="let wh of warehouses" [value]="wh.code">{{wh.name}}</option>
          </select>
        </div>
        <div class="mb-3 col-md-3">
          <label>Available Qty in Target</label>
          <input required #avlQtySub="ngModel" type="text" name="avlQtySub" id="avlQtySub"
                 [(ngModel)]="availableQtyTarget"
                 [class.is-invalid]="avlQtySub.invalid && avlQtySub.touched"
                 class="form-control" disabled>
        </div>

        <div class="mb-3 col-md-3">
          <label>Transferring Quantity</label>
          <input required #trnsQty="ngModel" type="number" name="trnsQty" id="trnsQty"
                 [(ngModel)]="transferringQty" [attr.required]="transferringQty <= availableQtySource"
                 [class.is-invalid]="trnsQty.invalid && trnsQty.touched"
                 class="form-control" required>
        </div>
      </div>

      <div class="row">
        <div class="col-md-12 text-end">
          <button class="btn btn-success" type="submit" [disabled]="!transferStkForm.valid">Transfer</button>
        </div>
      </div>
    </form>
  </div>
</div>
