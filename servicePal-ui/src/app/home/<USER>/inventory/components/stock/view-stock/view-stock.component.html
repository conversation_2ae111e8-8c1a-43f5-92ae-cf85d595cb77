<div class="card">
  <div class="card-header">
    <strong>VIEW STOCK</strong>
  </div>
  <div class="card-body">
    <div class="row mb-2">
      <div class="col-md-2">
        <select class="form-select" required #selectedWh="ngModel"
                [class.is-invalid]="selectedWh.invalid && selectedWh.touched" name="selectedWh"
                [(ngModel)]="selectedWarehouse" (change)="filterByWarehouse()">
          <option [ngValue]="">Select Warehouse</option>
          <option *ngFor="let wh of warehouses" [ngValue]="wh">{{wh.name}}</option>
        </select>
      </div>
      <div class="col-md-3">
        <input [(ngModel)]="keyItemSearch"
               (ngModelChange)="searchByNameToTable()"
               placeholder="Search By Item Name"
               autocomplete="off"
               size="16"
               class="form-control" name="searchItem">
      </div>
      <div class="col-md-3">
        <input [(ngModel)]="barcode"
               (ngModelChange)="searchByBarcodeToTable()"
               autocomplete="off"
               placeholder="Search By Barcode"
               class="form-control" name="barcode">
      </div>
      <div class="col-md-2">
        <input [(ngModel)]="keyItemCategory"
               [typeahead]="itemCategories"
               (typeaheadLoading)="loadItemCategories()"
               (typeaheadOnSelect)="setSelectedItemCategory($event)"
               [typeaheadOptionsLimit]="15"
               typeaheadOptionField="code"
               placeholder="Category"
               autocomplete="off"
               size="16"
               #category="ngModel"
               class="form-control" name="category">
      </div>
      <div class="col-md-2">
        <input [(ngModel)]="keyBrand"
               [typeahead]="brands"
               (typeaheadLoading)="loadBrands()"
               (typeaheadOnSelect)="setSelectedBrand($event)"
               [typeaheadOptionsLimit]="10"
               typeaheadOptionField="code"
               placeholder="Brand"
               autocomplete="off"
               id="appendedInputButtons" size="16"
               class="form-control" name="brand">
      </div>
    </div>
    <table class="table mt-2">
      <thead>
      <tr>
        <th>Barcode</th>
        <th>Item Name</th>
        <th>Selling Price</th>
        <th>Dead Stock Level</th>
        <th>Quantity</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let stock of subStocks,let i = index"
          (click)="selectStockRecord(stock,i)"
          [class.active]="i === selectedRow">
        <td>{{stock.barcode}}</td>
        <td>{{stock.itemName}}</td>
        <td>{{stock.sellingPrice}}</td>
        <td>{{stock.deadStockLevel}}</td>
        <td>{{stock.quantity}}</td>
      </tr>
      </tbody>
    </table>
    <div class="row">
      <div class="col-xs-12 col-12">
        <pagination class="pagination-sm justify-content-center" *ngIf="displayPagination"
                    [totalItems]="collectionSize"
                    [(ngModel)]="page"
                    (pageChanged)="pageChanged($event)"
                    [maxSize]="15"
                    [boundaryLinks]="true">
        </pagination>
      </div>
    </div>
    <div class="row text-end">
      <div class="col-md-12">
        <button type="button" class="btn btn-outline-primary me-1" (click)="clearTable()">Reset Table</button>
        <button type="button" class="btn btn-outline-danger me-1" *ngIf="isPermitted" (click)="transferStock()">
          Transfer
        </button>
        <button type="button" class="btn btn-outline-danger" *ngIf="isPermitted"
                (click)="adjustStock()">Adjust Stock
        </button>
      </div>
    </div>
  </div>
</div>

