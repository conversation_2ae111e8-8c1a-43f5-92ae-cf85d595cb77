<div class="card">
  <div class="card-header">
    <strong>STOCK</strong>
  </div>
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-4">
        <select type="text" required #warehouseSelect="ngModel"
                [class.is-invalid]="warehouseSelect.invalid && warehouseSelect.touched"
                class="form-control form-select" id="warehouse" [(ngModel)]="selectedWarehouse.id"
                name="warehouse" (ngModelChange)="filterByWarehouse()">
          <option *ngFor="let wh of warehouses" [value]="wh.id">{{wh.name}}</option>
        </select>
      </div>

      <div class="col-md-4">
        <!-- Updated to PrimeNG autocomplete -->
        <p-autocomplete
          [(ngModel)]="keyItemCategory"
          [suggestions]="itemCategories"
          (onSelect)="setSelectedItemCategory($event)"
          [dropdown]="true"
          field="name"
          (completeMethod)="loadItemCategories()"
          placeholder="Search Item Category">
        </p-autocomplete>
      </div>

      <div class="col-md-4">
        <!-- Updated to PrimeNG autocomplete -->
        <p-autocomplete
          [(ngModel)]="keyBrand"
          [suggestions]="brands"
          (onSelect)="setSelectedItem($event)"
          [dropdown]="true"
          field="name"
          (completeMethod)="loadBrands()"
          placeholder="Search Brand">
        </p-autocomplete>
      </div>
    </div>

    <div class="row g-3 mt-2">
      <div class="col-md-5">
        <div class="input-group">
          <!-- Updated to PrimeNG autocomplete -->
          <p-autocomplete
            [(ngModel)]="keyItemSearch"
            [suggestions]="itemSearched"
            (onSelect)="setSelectedItem($event)"
            [dropdown]="true"
            field="itemName"
            (completeMethod)="searchByNameToTable()"
            placeholder="Search Item Name"
            class="form-control" name="name"
            required
            #name="ngModel"
            [class.is-invalid]="name.invalid && name.touched">
          </p-autocomplete>
          <button class="btn btn-primary fa fa-plus" (click)="openCreateItemModal()" type="button"></button>
        </div>
      </div>

      <div class="col-md-4">
        <div class="input-group">
          <input [(ngModel)]="barcode" (keyup.enter)="searchByBarcodeToTable()"
                 placeholder="Search Barcode" class="form-control" name="barcode"
                 required #code="ngModel" [class.is-invalid]="code.invalid && code.touched">
          <button class="btn btn-primary fa fa-plus" (click)="openCreateItemModal()" type="button"></button>
        </div>
      </div>

      <div class="col-md-3 text-end">
        <button type="button" class="btn btn-danger me-2" (click)="adjustStock()">Adjust Stock</button>
        <button type="button" class="btn btn-success" (click)="toggleQuickEdit()">{{quickEditMode ? 'Cancel Quick Edit' : 'Quick Edit'}}</button>
      </div>
    </div>

    <div class="row g-3 mt-2">
      <div class="table-height">
        <div class="row g-3">
          <div class="col-md-12">
            <table class="table table-striped">
              <thead align="center">
              <tr>
                <th scope="col">#</th>
                <th scope="col">Barcode</th>
                <th scope="col">Name</th>
                <th scope="col">Code</th>
                <th scope="col">Buying Price</th>
                <th scope="col">Selling Price</th>
                <th scope="col">Qty</th>
                <th scope="col">Reorder Level</th>
                <th scope="col">Actions</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let stock of subStocks; let i = index"
                  (click)="selectStockRecord(stock, i)" [class.active]="i === selectedRow">
                <td>{{i + 1}}</td>
                <td>{{stock.barcode}}</td>
                <td>{{stock.itemName}}</td>
                <td>{{stock.itemCode}}</td>
                <td>
                  <input type="number" [(ngModel)]="stock.itemCost" [disabled]="!quickEditMode"
                         (change)="saveStock(stock)" class="form-control">
                </td>
                <td>
                  <input type="number" [(ngModel)]="stock.sellingPrice" [disabled]="!quickEditMode"
                         (change)="saveStock(stock)" class="form-control">
                </td>
                <td>
                  <input type="number" [(ngModel)]="stock.quantity" [disabled]="!quickEditMode"
                         (change)="saveStock(stock)" class="form-control">
                </td>
                <td>
                  <input type="number" [(ngModel)]="stock.reorderLevel" [disabled]="!quickEditMode"
                         (change)="saveStock(stock)" class="form-control">
                </td>
                <td>
                  <button type="button" class="btn btn-sm btn-danger ms-1" (click)="transferStock()">Transfer</button>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <div class="row" *ngIf="displayPagination">
      <div class="col-xs-12 col-12 ">
        <!-- Updated to PrimeNG paginator -->
        <p-paginator
          [(page)]="page"
          [totalRecords]="collectionSize"
          [rows]="pageSize"
          [rowsPerPageOptions]="[5,10,20,50]"
          (onPageChange)="pageChanged($event)">
        </p-paginator>
      </div>
    </div>

    <div class="row" *ngIf="loading">
      <div class="col-12 text-center">
        <i class="fa fa-spinner fa-spin fa-2x"></i>
      </div>
    </div>

    <div class="row" *ngIf="!displayPagination && !loading">
      <div class="col-md-12 text-end">
        <button type="button" class="btn btn-success me-2" (click)="saveAllStocks()">Save All Changes</button>
        <button type="button" class="btn btn-warning" (click)="resetStocks()">Reset</button>
      </div>
    </div>
  </div>
</div>