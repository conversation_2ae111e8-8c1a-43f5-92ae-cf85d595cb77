<div class="card">
  <div class="card-header">
    <strong>MANAGE BRANDS</strong>
  </div>
  <div class="card-body">
    <div class="row g-4">
      <!-- Left Section: Search and Table -->
      <div class="col-md-6">
        <div class="row g-3">
          <div class="col-md-6">
            <div class="input-group">
              <input [(ngModel)]="keyBrand"
                     [typeahead]="brands"
                     (typeaheadLoading)="loadBrands()"
                     (typeaheadOnSelect)="setSelectedBrand($event)"
                     placeholder="Search By Name"
                     autocomplete="off"
                     size="16"
                     class="form-control">
            </div>
          </div>
          <div class="col-md-6">
            <div class="input-group">
              <input [(ngModel)]="keyBrandCode"
                     [typeahead]="brands"
                     (typeaheadLoading)="loadBrandsByCode()"
                     (typeaheadOnSelect)="setSelectedBrand($event)"
                     placeholder="Search By Code"
                     autocomplete="off"
                     size="16"
                     class="form-control">
            </div>
          </div>
        </div>

        <table class="table table-striped mt-3">
          <thead>
          <tr>
            <th>Brand Name</th>
            <th>Code</th>
            <th>Supplier</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let brand of brands, let i=index"
              (click)="brandDetail(brand, i)"
              [class.active]="i === selectedRow">
            <td>{{brand.name}}</td>
            <td>{{brand.code}}</td>
            <td>{{brand.supplier ? brand.supplier.name : 'N/A'}}</td>
          </tr>
          </tbody>
        </table>

        <div class="d-flex justify-content-center mt-3">
          <pagination class="pagination-sm"
                      [totalItems]="collectionSize"
                      [(ngModel)]="page"
                      [boundaryLinks]="true"
                      [maxSize]="10"
                      (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>

      <!-- Right Section: Form -->
      <div class="col-md-6">
        <form #manageBrandForm="ngForm" (ngSubmit)="saveBrand(); manageBrandForm.reset()">
          <div class="mb-3">
            <label for="bName" class="form-label">Brand Name</label>
            <input type="text" required #bName="ngModel"
                   [class.is-invalid]="bName.invalid && bName.touched"
                   class="form-control" id="bName"
                   [(ngModel)]="brand.name" name="bName"
                   placeholder="Brand Name">
            <div *ngIf="bName.errors && (bName.invalid || bName.touched)">
              <small class="text-danger">*Brand Name is required</small>
            </div>
          </div>

          <div class="mb-3">
            <label for="code" class="form-label">Code</label>
            <input type="text" required #brandCode="ngModel"
                   class="form-control" id="code"
                   [(ngModel)]="brand.code" maxlength="2"
                   name="itemCost" (ngModelChange)="checkDuplicate()"
                   placeholder="Code"
                   [class.is-invalid]="brandCode.invalid && brandCode.touched">
            <small class="text-danger" [class.d-none]="!isDuplicate">*This code is already added</small>
          </div>

          <div class="mb-3">
            <label for="supplier" class="form-label">Supplier</label>
            <input [(ngModel)]="keySupplier"
                   [typeahead]="suppliers"
                   (typeaheadLoading)="loadSuppliers()"
                   (typeaheadOnSelect)="setSelectedItem($event)"
                   [typeaheadOptionsLimit]="7"
                   typeaheadOptionField="name"
                   placeholder="Search Supplier"
                   autocomplete="off"
                   size="16"
                   class="form-control"
                   id="supplier" name="supplier"
                   required>
          </div>

          <div class="form-check mb-3">
            <input class="form-check-input" id="check3" name="check3" type="checkbox"
                   [(ngModel)]="brand.active">
            <label class="form-check-label" for="check3">Active</label>
          </div>

          <div class="d-flex justify-content-end gap-2">
            <button type="button" class="btn btn-warning" (click)="clear()">Clear</button>
            <button type="button" class="btn btn-primary"
                    [disabled]="!manageBrandForm.form.valid"
                    (click)="updateBrand()">Update</button>
            <button type="submit" class="btn btn-success"
                    [disabled]="!manageBrandForm.form.valid || !selectedSupplier.id">Save</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
