<div class="card">
  <div class="card-header">
    <strong>MANAGE BRANDS</strong>
  </div>
  <div class="card-body">
    <form #brandForm="ngForm" (ngSubmit)="saveBrand(); brandForm.reset()">
      <div class="row g-3">
        <div class="col-md-6">
          <div class="form-group">
            <!-- Updated to PrimeNG autocomplete -->
            <p-autocomplete
              [(ngModel)]="keyBrandCode"
              [suggestions]="brands"
              (onSelect)="setSelectedBrand($event)"
              [dropdown]="true"
              field="code"
              (completeMethod)="loadBrandsByCode()"
              placeholder="Search Brand Code"
              class="form-control" name="searchBrandCode"
              required
              #searchBrandCode="ngModel"
              [class.is-invalid]="searchBrandCode.invalid && searchBrandCode.touched">
            </p-autocomplete>
          </div>

          <div class="form-group">
            <!-- Updated to PrimeNG autocomplete -->
            <p-autocomplete
              [(ngModel)]="keyBrand"
              [suggestions]="brands"
              (onSelect)="setSelectedBrand($event)"
              [dropdown]="true"
              field="name"
              (completeMethod)="searchBrands($event)"
              placeholder="Search Brand Name"
              class="form-control" name="searchBrandName"
              required
              #searchBrandName="ngModel"
              [class.is-invalid]="searchBrandName.invalid && searchBrandName.touched">
            </p-autocomplete>
          </div>

          <div class="form-group">
            <label>Brand Name</label>
            <input type="text" required #name="ngModel"
                   [class.is-invalid]="name.invalid && name.touched"
                   class="form-control" id="name" [(ngModel)]="brand.name" name="name"
                   placeholder="Brand Name">
            <small class="text-danger" [class.d-none]="name.valid || name.untouched">*Name is required
            </small>
          </div>

          <div class="form-group">
            <label>Brand Code</label>
            <input type="text" required #code="ngModel"
                   [class.is-invalid]="code.invalid && code.touched"
                   class="form-control" id="code" [(ngModel)]="brand.code" name="code"
                   placeholder="Brand Code">
            <small class="text-danger" [class.d-none]="code.valid || code.untouched">*Code is required
            </small>
          </div>

          <div class="form-check checkbox me-2 mb-3">
            <input class="form-check-input" id="check1" name="check1" type="checkbox" value=""
                   [(ngModel)]="brand.active">
            <label class="form-check-label" for="check1">Active</label>
          </div>

          <div class="row float-end">
            <div class="me-3">
              <button type="submit" class="btn btn-success" [disabled]="!brandForm.form.valid">{{brand.id ? 'Update' : 'Save'}}</button>
            </div>
            <div class="me-3">
              <button type="button" class="btn btn-warning" (click)="clear(); brandForm.reset()">Clear</button>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="table-height">
            <table class="table table-striped">
              <thead align="center">
              <tr>
                <th scope="col">Name</th>
                <th scope="col">Code</th>
                <th scope="col">Active</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let br of brands; let i = index"
                  (click)="brandDetail(br, i)" [class.active]="i === selectedRow">
                <td>{{br.name}}</td>
                <td>{{br.code}}</td>
                <td>
                  <span [class.text-success]="br.active" [class.text-danger]="!br.active">
                    {{br.active ? 'Yes' : 'No'}}
                  </span>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>