import {Component, OnInit} from '@angular/core';
import {Brand} from '../../model/brand';
import {BrandService} from '../../service/brand.service';
import {NotificationService} from "../../../../core/service/notification.service";
import {Supplier} from "../../../trade/model/supplier";
import {SupplierService} from "../../../trade/service/supplier.service";

@Component({
  standalone: false,
  selector: 'app-brand',
  templateUrl: './brand.component.html',
  styleUrls: ['./brand.component.css']
})


export class BrandComponent implements OnInit {
  firstRecordIndex: number = 0;
  itemsPerPage: number = 10;


  brand = new Brand();
  brands: Array<Brand> = [];
  setClickedRow: Function;
  selectedRow: number;
  keyBrand: string;
  collectionSize;
  page;
  pageSize;
  isDuplicate: boolean;
  keyBrandCode: string;
  suppliers: Array<Supplier>;
  keySupplier: string;
  selectedSupplier: Supplier;

  constructor(private brandService: BrandService,
              private notificationService: NotificationService,
              private supplierService: SupplierService) {

  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 8;
    this.brand = new Brand();
    this.selectedSupplier = new Supplier();
    this.brand.active = true;
    this.findAll();
  }

  saveBrand() {
    if(this.selectedSupplier.id){
      this.brand.supplier = new Supplier();
      this.brand.supplier.id = this.selectedSupplier.id;
    }
    this.brandService.save(this.brand).subscribe(result => {
      this.notificationService.showSuccess("Brand Saved Successfully");
      this.ngOnInit();
    }, error => {
      this.notificationService.showError("Brand Saved Failed");
      console.log(error);
    });

  }

  loadBrands(event?: any) {
    const query = event ? event.query : this.keyBrand;
    this.brandService.findByName(query).subscribe((data: Array<Brand>) => {
      return this.brands = data;
    });
  }

  findAll() {
    this.brandService.findAll(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.brands = data.content;
      this.collectionSize = data.totalPages * 8;
      console.log(this.brands);

    });
  }

  pageChanged(event: any) {
    this.page = event.page + 1;
    this.findAll();
  }
  onPageChange(event: any) {
    const page = event.page + 1; // PrimeNG paginator is 0-indexed
    this.page = page;
    // Call your existing pagination method
    this.pageChanged({ page: page });
  }


  brandDetail(brand, index) {
    this.brand = brand;
    this.keySupplier = this.brand.supplier.name;
    this.selectedRow = index;
  }

  setSelectedBrand(event) {
    this.brand = event.item;
  }

  updateBrand() {
    this.saveBrand();
  }

  clear() {
    this.brand = new Brand();
  }

  checkDuplicate() {
    this.brandService.checkDuplicate(this.brand.code).subscribe((val: boolean) => {
      this.isDuplicate = val;
    });
  }

  loadBrandsByCode(event?: any) {
    const query = event ? event.query : this.keyBrandCode;
    this.brandService.findByCode(query).subscribe((data: Array<Brand>) => {
      this.brands = data;
    });
  }

  loadSuppliers(event?: any) {
    const query = event ? event.query : this.keySupplier;
    this.supplierService.findByNameLike(query).subscribe((data: Array<Supplier>) => {
      this.suppliers = data;
    });
  }

  setSelectedItem(event: any) {
    this.selectedSupplier = event.item;
  }
}
