import {Component, OnInit} from '@angular/core';
import {Brand} from '../../model/brand';
import {BrandService} from '../../service/brand.service';
import {NotificationService} from "../../../../core/service/notification.service";
import {Supplier} from "../../../trade/model/supplier";
import {SupplierService} from "../../../trade/service/supplier.service";

@Component({
  standalone: false,
  selector: 'app-brand',
  templateUrl: './brand.component.html',
  styleUrls: ['./brand.component.css']
})


export class BrandComponent implements OnInit {

  brand = new Brand();
  brands: Array<Brand> = [];
  setClickedRow: Function;
  selectedRow: number;
  keyBrand: string;
  totalRecords: number;
  rows: number;
  isDuplicate: boolean;
  keyBrandCode: string;
  suppliers: Array<Supplier>;
  keySupplier: string;
  selectedSupplier: Supplier;

  constructor(private brandService: BrandService,
              private notificationService: NotificationService,
              private supplierService: SupplierService) {

  }

  ngOnInit() {
    this.rows = 8;
    this.brand = new Brand();
    this.selectedSupplier = new Supplier();
    this.brand.active = true;
    this.findAll();
  }

  // Add missing method for PrimeNG AutoComplete
  searchBrands(event) {
    this.brandService.findByNameLike(event.query).subscribe((data: Array<Brand>) => {
      this.brands = data;
    });
  }

  saveBrand() {
    if(this.selectedSupplier.id){
      this.brand.supplier = new Supplier();
      this.brand.supplier.id = this.selectedSupplier.id;
    }
    this.brandService.save(this.brand).subscribe(result => {
      this.notificationService.showSuccess("Brand Saved Successfully");
      this.ngOnInit();
    }, error => {
      this.notificationService.showError("Brand Saved Failed");
      console.log(error);
    });

  }

  loadBrands() {
    this.brandService.findByName(this.keyBrand).subscribe((data: Array<Brand>) => {
      return this.brands = data;
    });
  }

  findAll() {
    this.brandService.findAll(0, this.rows).subscribe((data: any) => {
      this.brands = data.content;
      this.totalRecords = data.totalElements;
    });
  }

  onPageChange(event: any) {
    const page = event.first / event.rows;
    this.rows = event.rows;
    this.brandService.findAll(page, this.rows).subscribe((data: any) => {
      this.brands = data.content;
    });
  }

  brandDetail(brand, index) {
    this.brand = brand;
    this.keySupplier = this.brand.supplier.name;
    this.selectedRow = index;
  }

  setSelectedBrand(event) {
    this.brand = event.item;
  }

  updateBrand() {
    this.saveBrand();
  }

  clear() {
    this.brand = new Brand();
  }

  checkDuplicate() {
    this.brandService.checkDuplicate(this.brand.code).subscribe((val: boolean) => {
      this.isDuplicate = val;
    });
  }

  loadBrandsByCode() {
    this.brandService.findByCode(this.keyBrandCode).subscribe((data: Array<Brand>) => {
      this.brands = data;
    });
  }

  loadSuppliers() {
    this.supplierService.findByNameLike(this.keySupplier).subscribe((data: Array<Supplier>) => {
      this.suppliers = data;
    });
  }

  setSelectedItem(event: any) {
    this.selectedSupplier = event.item;
  }
}
