<div class="card">
  <div class="card-header">
    <strong>MANAGE WAREHOUSE</strong>
  </div>
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-6">
        <form #manageWareHouseForm="ngForm" (ngSubmit)="saveWarehouse(); manageWareHouseForm.reset()">
          <div class="row g-3">
            <div class="col-md-12 form-group">
              <label>Name</label>
              <input type="text" required #Name="ngModel" [class.is-invalid]="Name.invalid && Name.touched"
                     class="form-control" id="wName" [(ngModel)]="warehouse.name" name="Name"
                     placeholder="Name ">
              <div *ngIf="Name.errors && (Name.invalid || Name.touched)">
                <small class="text-danger" [class.d-none]="Name.valid || Name.untouched">*Name is required
                </small>
              </div>
            </div>

            <!--<div class="mb-3 col-md-6">
              <label>Warehouse Manager</label>
              <input [(ngModel)]="keyEmpSearch"
                     [typeahead]="empSearchList"
                     (typeaheadLoading)="searchEmployee()"
                     (typeaheadOnSelect)="setSelectedEmp($event)"
                     [typeaheadOptionsLimit]="7"

                     typeaheadOptionField="name"
                     placeholder="Enter Warehouse Manager Name"
                     autocomplete="off"
                     size="16"
                     class="form-control" name="searchEmp" required #searchEmp="ngModel">
              <div *ngIf="searchEmp.errors && (searchEmp.invalid || searchEmp.touched)">
                <small class="text-danger" [class.d-none]="searchEmp.valid || searchEmp.untouched">*Store Manager is
                  required
                </small>
              </div>
            </div>-->

            <div class="col-md-12 form-group">
              <label>Address</label>
              <input type="text" #Address1="ngModel" [class.is-invalid]="Address1.invalid && Address1.touched"
                     class="form-control" id="Address1" [(ngModel)]="warehouse.address" name="Address1"
                     placeholder="Address Line 1 ">
            </div>

            <div class="col-md-6 form-group">
              <label>Telephone 1 </label>
              <input type="text" #Address2="ngModel" [class.is-invalid]="Address2.invalid && Address2.touched"
                     class="form-control" id="Address2" [(ngModel)]="warehouse.telephone1" name="Address2"
                     placeholder="Telephone 1">
            </div>

            <!--            <div class="col-md-6 form-group">-->
            <!--              <label>Telephone 2 </label>-->
            <!--              <input type="text" #Address3="ngModel" [class.is-invalid]="Address3.invalid && Address3.touched"-->
            <!--                     class="form-control" id="Address3" [(ngModel)]="warehouse.telephone2" name="Address3"-->
            <!--                     placeholder="Telephone 2 ">-->
            <!--            </div>-->

            <div class="col-md-6 form-group">
              <label>Extra Data </label>
              <input type="text" #param1="ngModel"
                     [class.is-invalid]="param1.invalid && param1.touched"
                     class="form-control" id="param1" [(ngModel)]="warehouse.param1" name="param1"
                     placeholder="Extra Data">
            </div>

            <div class="col-md-12 text-end">
              <button type="submit" class="btn btn-primary"
                      [disabled]="!manageWareHouseForm.form.valid"> Save
              </button>
              <button type="button" class="btn btn-warning ms-2" (click)="clearAll()">Clear</button>
            </div>
          </div>
        </form>
      </div>
      <div class="col-md-6">
        <div class="row g-3">
          <div class="col-md-12">
            <input [(ngModel)]="keyWarehouse"
                   [typeahead]="warehouses"
                   (typeaheadLoading)="loadWarehouses()"
                   (typeaheadOnSelect)="setSelectedWarehouse($event)"
                   [typeaheadOptionsLimit]="7"

                   typeaheadOptionField="name"
                   placeholder="Search Warehouse"
                   autocomplete="off"
                   size="16"
                   required
                   class="form-control m-" name="warehouse">
          </div>
          <div class="col-md-12 mt-2">
            <table class="table table-bordered table-striped table-sm">
              <thead>
              <tr style="text-align: center">
                <th scope="col">WareHouse</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let warehouse of warehouses,let i=index"
                  (click)="selectWarehouse(warehouse,i) "
                  [class.active]="i === selectedRow">
                <td align="center">{{warehouse.name}}</td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="row g-3 text-center">
          <pagination class="pagination-sm justify-content-center" [ngModelOptions]="{standalone: true}"
                      [totalItems]="collectionSize"
                      [(ngModel)]="page"
                      [boundaryLinks]="true"
                      [maxSize]="10"
                      (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>
    </div>
  </div>
</div>
