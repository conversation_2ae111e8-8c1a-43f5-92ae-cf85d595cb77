<div class="card">
  <div class="card-header">
    <strong>MANAGE WAREHOUSES</strong>
  </div>
  <div class="card-body">
    <form #warehouseForm="ngForm" (ngSubmit)="saveWarehouse(); warehouseForm.reset()">
      <div class="row g-3">
        <div class="col-md-6">
          <div class="form-group">
            <label>Search</label>
            <!-- Updated to PrimeNG autocomplete -->
            <p-autocomplete
              [(ngModel)]="keySearch"
              [suggestions]="warehouses"
              (onSelect)="setSelectedItem($event)"
              [dropdown]="true"
              field="name"
              (completeMethod)="searchWarehouses()"
              placeholder="Search Warehouse"
              class="form-control" name="searchWarehouse"
              required
              #searchWarehouse="ngModel"
              [class.is-invalid]="searchWarehouse.invalid && searchWarehouse.touched">
            </p-autocomplete>
          </div>

          <div class="form-group">
            <label>Name</label>
            <input type="text" required #name="ngModel"
                   [class.is-invalid]="name.invalid && name.touched"
                   class="form-control" id="name" [(ngModel)]="warehouse.name" name="name"
                   placeholder="Warehouse Name">
            <small class="text-danger" [class.d-none]="name.valid || name.untouched">*Name is required
            </small>
          </div>

          <div class="form-group">
            <label>Code</label>
            <input type="text" required #code="ngModel"
                   [class.is-invalid]="code.invalid && code.touched"
                   class="form-control" id="code" [(ngModel)]="warehouse.code" name="code"
                   placeholder="Warehouse Code">
            <small class="text-danger" [class.d-none]="code.valid || code.untouched">*Code is required
            </small>
          </div>

          <div class="form-check checkbox me-2 mb-3">
            <input class="form-check-input" id="check1" name="check1" type="checkbox" value=""
                   [(ngModel)]="warehouse.active">
            <label class="form-check-label" for="check1">Active</label>
          </div>

          <div class="row float-end">
            <div class="me-3">
              <button type="submit" class="btn btn-success" [disabled]="!warehouseForm.form.valid">{{warehouse.id ? 'Update' : 'Save'}}</button>
            </div>
            <div class="me-3">
              <button type="button" class="btn btn-warning" (click)="clearAll(); warehouseForm.reset()">Clear</button>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="table-height">
            <table class="table table-striped">
              <thead align="center">
              <tr>
                <th scope="col">Name</th>
                <th scope="col">Code</th>
                <th scope="col">Active</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let wh of warehouses; let i = index"
                  (click)="selectRecord(wh, i)" [class.active]="i === selectedRow">
                <td>{{wh.name}}</td>
                <td>{{wh.code}}</td>
                <td>
                  <span [class.text-success]="wh.active" [class.text-danger]="!wh.active">
                    {{wh.active ? 'Yes' : 'No'}}
                  </span>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
