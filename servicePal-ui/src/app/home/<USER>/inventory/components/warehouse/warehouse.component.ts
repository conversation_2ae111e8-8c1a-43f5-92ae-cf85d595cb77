import {Component, OnInit} from '@angular/core';
import {WarehouseService} from '../../service/warehouse.service';
import {NotificationService} from '../../../../core/service/notification.service';
import {Employee} from '../../../hr/model/employee';
import {EmployeeService} from '../../../hr/service/employee.service';
import {Warehouse} from "../../model/warehouse";

@Component({
  standalone: false,
  selector: 'app-ware-house',
  templateUrl: './warehouse.component.html',
  styleUrls: ['./warehouse.component.css']
})
export class WarehouseComponent implements OnInit {
  firstRecordIndex: number = 0;
  itemsPerPage: number = 10;


  warehouse: Warehouse;
  warehouses: Array<Warehouse> = [];
  setClickedRow: Function;
  selectedRow: number;
  keyWarehouse: string;
  page;
  collectionSize;
  pageSize;

  keyEmpSearch: string;
  empSearchList: Array<Employee> = [];

  constructor(private warehouseService: WarehouseService, private notificationService: NotificationService,
              private employeeService: EmployeeService) {
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 8;
    this.warehouse = new Warehouse();
    this.warehouse.storeKeeper = new Employee();
    this.findAllPagination();
  }

  saveWarehouse() {
    this.warehouseService.save(this.warehouse).subscribe(result => {
      this.notificationService.showSuccess(result);
      this.ngOnInit();
    }, error => {
      console.log(error);
    });
  }

  loadWarehouses(event?: any) {
    
    const query = event ? event.query : this.keyWarehouse;
    this.warehouseService.findAllByName(query).subscribe((data: Array<Warehouse>) => {
      return this.warehouses = data;
    });
  }

  findAllPagination() {
    this.warehouseService.findAllPagination(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.warehouses = data.content;
      this.collectionSize = data.totalPages * 8;
    });
  }

  pageChanged(event: any) {
    this.page = event.page + 1;
    this.findAllPagination();
  }
  onPageChange(event: any) {
    const page = event.page + 1; // PrimeNG paginator is 0-indexed
    this.page = page;
    // Call your existing pagination method
    this.pageChanged({ page: page });
  }


  searchEmployee(event?: any) {
    
    const query = event ? event.query : this.keyEmpSearch;
    this.employeeService.findByEmployeeNameLike(query).subscribe((result: Array<Employee>) => {
      return this.empSearchList = result;
    })
  }

  setSelectedEmp(event) {
    this.warehouse.storeKeeper.id = event.item.id;
  }

  selectWarehouse(warehouse, index) {
    this.clearAll();
    this.warehouse = warehouse;
    this.keyEmpSearch = this.warehouse.storeKeeper.name;
    this.selectedRow = index;
  }

  clearAll() {
    this.ngOnInit();
    this.keyEmpSearch = "";
  }

  setSelectedWarehouse(e) {
    this.warehouse = e.item;
  }
}
