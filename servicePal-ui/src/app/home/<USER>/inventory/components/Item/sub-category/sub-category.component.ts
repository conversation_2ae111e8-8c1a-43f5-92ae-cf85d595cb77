import {Component, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';
import {SubCategory} from '../../../model/sub-category';
import {ItemCategoryService} from '../../../service/item-category.service';
import {SubCategoryService} from '../../../service/sub-category.service';
import {ItemCategory} from '../../../model/item-category';
import {NotificationService} from '../../../../../core/service/notification.service';

@Component({
  standalone: false,
  selector: 'app-sub-category',
  templateUrl: './sub-category.component.html',
  styleUrls: ['./sub-category.component.css']
})

export class SubCategoryComponent implements OnInit {
  firstRecordIndex: number = 0;
  itemsPerPage: number = 10;


  currentUser = JSON.parse(localStorage.getItem('currentUser'));

  subCategory: SubCategory;
  keySubCategory: string;
  subItemCategories: Array<SubCategory> = [];
  itemCategories: Array<ItemCategory> = [];
  collectionSize;
  page;
  pageSize;
  selectedSubCategory: SubCategory;
  modalRef: BsModalRef;
  subCategoriesSearched: Array<SubCategory> = [];
  categoryId: string;
  itemCategory = new ItemCategory();

  constructor(private itemCategoryService: ItemCategoryService, private notificationService: NotificationService,
              private modalService: BsModalService, private subCategoryService: SubCategoryService) {
  }

  ngOnInit() {
    this.findAllItemCategories();
    this.page = 1;
    this.pageSize = 10;
    this.selectedSubCategory = null;
    this.findAll();
    this.subCategory = new SubCategory();
    this.subCategory.itemCategory = this.itemCategory;
    this.subCategory.active = true;
  }

  openModal(template: TemplateRef<any>) {
    if (this.selectedSubCategory) {
      this.modalRef = this.modalService.show(template, {class: 'modal-sm'});
    } else {
      this.notificationService.showError('please select a row!');
    }
  }

  confirmDelete(): void {
    this.modalRef.hide();
    this.subCategoryService.delete(this.selectedSubCategory.id).subscribe((result) => {
      this.notificationService.showError(result);
    });
    this.ngOnInit();
  }

  confirmUpdate() {
    if (this.selectedSubCategory) {
      if (this.itemCategory.id != null) {
        this.subCategory.itemCategory = this.itemCategory;
        this.modalRef.hide();
        this.subCategoryService.save(this.subCategory).subscribe((result) => {
          this.notificationService.showSuccess(result);
          this.ngOnInit();
        });
      } else if (this.itemCategory.id === null) {
        this.modalRef.hide();
        this.subCategoryService.save(this.subCategory).subscribe((result) => {
          this.notificationService.showSuccess(result);
          this.ngOnInit();
        });
      }
    } else {
      this.notificationService.showError('please select a row');
    }
  }

  decline(): void {
    this.modalRef.hide();
    this.ngOnInit();
  }


  findAllItemCategories() {
    this.itemCategoryService.findAllCategories().subscribe((data: Array<ItemCategory>) => {
      this.itemCategories = data;
    });
  }

  pageChanged(event: any) {
    this.page = event.page + 1;
    //  this.findAll();
  }
  onPageChange(event: any) {
    const page = event.page + 1; // PrimeNG paginator is 0-indexed
    this.page = page;
    // Call your existing pagination method
    this.pageChanged({ page: page });
  }


  save() {
    if (this.itemCategory != null) {
      this.subCategory.itemCategory = this.itemCategory;
      this.subCategoryService.save(this.subCategory).subscribe(result => {
        this.notificationService.showSuccess(result);
        this.ngOnInit();
        this.clear();
      });
    } else {
      this.notificationService.showError('Select Parent Item Category First')
    }
  }

  findAll() {
    this.subCategoryService.findAll(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.subItemCategories = result.content;
      this.collectionSize = result.totalPages * 10;
    });
  }

  onChange(event) {
    this.categoryId = event.target.value;
    this.itemCategoryService.findById(this.categoryId).subscribe((data: ItemCategory) => {
      this.itemCategory = data;

    });
  }

  clear() {
    this.ngOnInit();
    this.keySubCategory = null;
  }

  onSelect(cat: SubCategory) {
    this.selectedSubCategory = cat;
    this.subCategory = this.selectedSubCategory;
  }

  loadSubCategories() {
    return this.subCategoryService.findByName(this.keySubCategory).subscribe((data: Array<SubCategory>) => {
      return this.subCategoriesSearched = data;
    });
  }

  setSelectedSubCategory(event) {
    this.selectedSubCategory = event.item;
    this.subCategory = this.selectedSubCategory;
  }

}
