import {Component, OnInit, TemplateRef} from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

import {BsModalRef, BsModalService, ModalOptions} from 'ngx-bootstrap/modal';
import {UomService} from '../../../service/uom.service';
import {SubCategory} from '../../../model/sub-category';
import {ItemType} from '../../../model/item-type';
import {ItemService} from '../../../service/item.service';
import {UOM} from '../../../model/uom';
import {ItemCategory} from '../../../model/item-category';
import {Brand} from '../../../model/brand';
import {Item} from '../../../model/item';
import {Response} from '../../../../../core/model/response';
import {BrandService} from '../../../service/brand.service';
import {ItemTypeService} from '../../../service/item-type.service';
import {SubCategoryService} from '../../../service/sub-category.service';
import {ItemCategoryService} from '../../../service/item-category.service';
import {ActivatedRoute} from '@angular/router';
import {RackService} from '../../../service/rack.service';
import {Rack} from '../../../model/rack';
import {NotificationService} from '../../../../../core/service/notification.service';
import {NgForm} from '@angular/forms';
import {BarcodeComponent} from '../../barcode/barcode.component';
import {RackComponent} from '../../rack/rack.component';

@Component({
  standalone: false,
  selector: 'app-create-item',
  templateUrl: './create-item.component.html',
  styleUrls: ['./create-item.component.css']
})

export class CreateItemComponent implements OnInit {

  keyUOM: string;
  keyBrand: string;
  keyItemSearch: string;
  keySubCategory: string;
  keyItemCategory: string;

  modalRef: BsModalRef;

  item: Item = new Item();

  selectedUom = new UOM();
  itemSearched: Array<Item> = [];

  unitOfMeasure: Array<UOM>;
  itemTypes: Array<ItemType> = [];

  categories: Array<ItemCategory>;
  subCategories: Array<SubCategory>;

  items: Array<Item> = [];
  brands: Array<Brand> = [];
  racks: Array<Rack> = [];

  selectedSubCategory = new SubCategory();
  selectedItemCategory = new ItemCategory();
  selectedBrand = new Brand();

  itemCategory: ItemCategory;
  code: string;
  rackId: string;
  itemTypeId: string;
  rack: Rack;
  isService: boolean;
  itemAvailable = false;
  isEditing = false;
  discount: number;

  constructor(
    private uomService: UomService,
              private itemService: ItemService,
              private brandService: BrandService,
              private modalService: BsModalService,
              private itemTypeService: ItemTypeService,
              private activatedRoute: ActivatedRoute,
              private subCategoryService: SubCategoryService,
              private notificationService: NotificationService,
              private itemCategoryService: ItemCategoryService,
              private rackService: RackService,
    public ref: DynamicDialogRef,
    public config: DynamicDialogConfig
  ) {
  }

  ngOnInit() {
    /*this.item.uom = new UOM();
    this.item.itemCategory = new ItemCategory();
    this.item.itemType = new ItemType();
    this.item.sellingPrice = 0;
    this.item.itemCost = 0;

    this.itemCategory = new ItemCategory();
    this.item.active = true;
    this.item.manageStock = true;*/

    this.loadItemTypes();
    this.loadRacks();
  }

  openModalExtraLarge(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, {class: 'modal-xl'} as ModalOptions);
  }

  openModalLarge(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, {class: 'modal-lg'} as ModalOptions);
  }

  newRack() {
    this.modalRef = this.modalService.show(RackComponent, {class: 'modal-md'} as ModalOptions);
  }

  loadBrands(event?: any) {
    
    const query = event ? event.query : this.keyBrand;
    this.brandService.findByName(query).subscribe((data: Array<Brand>) => {
      return this.brands = data;
    });
  }

  loadItemCategories() {
    this.itemCategoryService.findByName(this.keyItemCategory).subscribe((data: Array<ItemCategory>) => {
      return this.categories = data;
    });
  }

  setSelectedBrand(event) {
    this.item.brand = new Brand();
    this.selectedBrand = new Brand();
    this.selectedBrand.id = event.item.id;
  }

  loadSubCategories() {
    if (this.selectedItemCategory.id) {
      return this.subCategoryService.findByNameAndParent(this.keySubCategory, this.selectedItemCategory.id).subscribe((data: Array<SubCategory>) => {
        return this.subCategories = data;
      });
    } else {
      this.notificationService.showError('Please Select a Category');
    }
  }

  setSelectedItemCategory(event) {
    this.selectedItemCategory = new ItemCategory();
    this.selectedItemCategory.id = event.item.id;
    this.item.itemCategory = new ItemCategory();
    this.loadSubCategories();
  }

  setSelectedSubCategory(event) {
    this.selectedSubCategory = new SubCategory();
    this.selectedSubCategory.id = event.item.id;
    this.item.subCategory = new SubCategory();
  }

  //  uom
  loadUOM() {
    return this.uomService.findByName(this.keyUOM).subscribe((data: Array<UOM>) => {
      return this.unitOfMeasure = data;
    });
  }

  loadRacks() {
    this.rackService.findAllRack().subscribe((data: Array<Rack>) => {
      return this.racks = data;
    });
  }

  setSelectedUOM(event) {
    this.selectedUom = new UOM();
    this.selectedUom.id = event.item.id;
    this.item.uom = new UOM();
  }

  //  Item Type
  loadItemTypes() {
    this.itemTypeService.findAllForSelect().subscribe((data: Array<ItemType>) => {
      return this.itemTypes = data;
    });
  }

  setSelectedItemType(event) {
    this.item.itemType = new ItemType();
    this.item.itemType.id = event.target.value;
    let serviceId = '';
    for (const index in this.itemTypes) {
      if (this.itemTypes[index].name === 'Service') {
        serviceId = this.itemTypes[index].id;
        break;
      }
    }
    if (serviceId === event.target.value) {
      this.isService = true;
    } else {
      this.isService = false;
    }
  }

  saveItem(form: NgForm) {
    try {
      // checking whether saving or updating
      if (null != this.item.id) {
        this.item.update = true;
      } else {
        this.item.update = false;
      }

      if (this.selectedBrand.id) {
        this.item.brand.id = this.selectedBrand.id;
      }
      if (this.selectedSubCategory.id) {
        this.item.subCategory.id = this.selectedSubCategory.id;
      }
      this.item.itemCategory.id = this.selectedItemCategory.id;
      this.item.uom.id = this.selectedUom.id;
      this.item.sellingPrice = Math.round(this.item.sellingPrice * 100) / 100;
      this.item.itemCost = Math.round(this.item.itemCost * 100) / 100;
      this.itemService.save(this.item).subscribe((result: Response) => {
        if (result.code === 200) {
          this.notificationService.showSuccess(result.message);
          if (this.isEditing) {
            this.ref.close();
          }
          form.resetForm();
          this.clearForm();
        } else {
          this.notificationService.showError(result.message);
        }
      });
    } catch (error) {
      this.notificationService.showError('Please enter required data');
    }
  }

  saveAndBarcode(form: NgForm) {
    const tempBarcode = this.item.barcode;
    this.saveItem(form);
    this.modalRef = this.modalService.show(BarcodeComponent, {class: 'modal-md'} as ModalOptions);
    // TODO: Handle barcode via config.data or component properties = tempBarcode;
  }

  checkItemAvailable() {
    return this.itemService.findOneByBarcode(this.item.barcode).subscribe((data: Item) => {
      if (null != data) {
        this.itemAvailable = true;
      } else {
        this.itemAvailable = false;
      }
    });
  }

  clearForm() {
    this.ngOnInit();
    this.keyBrand = '';
    this.itemTypeId = '';
    this.keyItemSearch = '';
    this.keyItemCategory = '';
    this.itemSearched = [];
    this.selectedUom = new UOM();
    this.selectedBrand = new Brand();
    this.selectedItemCategory = new ItemCategory();
    this.selectedSubCategory = new SubCategory();
    this.itemAvailable = false;
    this.isEditing = false;
  }

  //  confirmation functions --------------------------------------------------------
  confirmItemUpdate() {
    if (this.isEditing) {
      this.ref.close();
      this.itemService.save(this.item).subscribe((result) => {
        if (result.status === 200) {
          this.notificationService.showSuccess(result.message);
          this.clearForm();
        } else {
          this.notificationService.showError(result.message);
          console.log(result.data);
        }
        this.ngOnInit();
        this.clearForm();
      });
    }
  }

  decline(): void {
    this.ref.close();
  }

  isCheckActivate(e) {
    if (e.target.checked) {
      this.item.active = false;
    } else {
      this.item.active = true;
    }
  }

  onChangeRack(id) {
    this.item.rack = new Rack();
    this.item.rack.id = id;
  }

  setSellingPrice() {
    if (this.discount) {
      this.item.sellingPrice = this.item.itemCost + (this.item.itemCost * (this.discount / 100));
    }
  }

}
