import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Item } from '../../../model/item';
import { ItemCategory } from '../../../model/item-category';
import { Brand } from '../../../model/brand';
import { ItemService } from '../../../service/item.service';
import { ItemCategoryService } from '../../../service/item-category.service';
import { BrandService } from '../../../service/brand.service';
import { NotificationService } from '../../../../../core/service/notification.service';
import {BsModalRef, BsModalService, ModalOptions} from 'ngx-bootstrap/modal';
import { CreateItemComponent } from '../create-item/create-item.component';
import { BarcodeComponent } from '../../barcode/barcode.component';

@Component({
  standalone: false,
  selector: 'app-view-all-items',
  templateUrl: './view-all-items.component.html',
  styleUrls: ['./view-all-items.component.css']
})
export class ViewAllItemsComponent implements OnInit {
  firstRecordIndex: number = 0;
  itemsPerPage: number = 10;


  page: number = 1;
  pageSize: number = 10;
  collectionSize: number = 0;
  keyItemSearch: string = '';
  itemSearched: Item[] = [];
  items: Item[] = [];
  selectedItem: Item | null = null;
  keyItemCategory: string = '';
  itemCategories: ItemCategory[] = [];
  selectedItemCategory: ItemCategory | null = null;
  keyBrand: string = '';
  brands: Brand[] = [];
  selectedBrand: Brand | null = null;
  barcode: string = '';
  modalRef: BsModalRef | null = null;
  selectedRow: number | null = null;

  constructor(
    private itemService: ItemService,
    private itemCategoryService: ItemCategoryService,
    private brandService: BrandService,
    private _router: Router,
    private notificationService: NotificationService,
    private modalService: BsModalService
  ) {}

  ngOnInit() {
    this.findAllItems();
  }

  pageChanged(event: any) {
    this.page = event.page + 1;
    this.findAllItems();
  }
  onPageChange(event: any) {
    const page = event.page + 1; // PrimeNG paginator is 0-indexed
    this.page = page;
    // Call your existing pagination method
    this.pageChanged({ page: page });
  }


  findAllItems() {
    this.itemService.findAll(this.page - 1, this.pageSize).subscribe(
      (result: any) => {
        this.items = result.content;
        this.collectionSize = result.totalElements;
      },
      (error) => {
        this.notificationService.showError('Failed to load items');
      }
    );
  }

  loadItems() {
    this.itemService.findAllActiveByNameLike(this.keyItemSearch).subscribe(
      (data: Item[]) => {
        this.itemSearched = data;
      },
      (error) => {
        this.notificationService.showError('Failed to load items');
      }
    );
  }

  loadItemBarcode() {
    this.itemService.findAllByBarcodeLike(this.barcode).subscribe(
      (data: Item[]) => {
        this.itemSearched = data;
      },
      (error) => {
        this.notificationService.showError('Failed to load items by barcode');
      }
    );
  }

  setSelectedItem(event: { item: Item }) {
    this.selectedItem = event.item;
    this.items = [];
    this.itemService.findAllActiveByNameLike(this.keyItemSearch).subscribe(
      (data: Item[]) => {
        this.items = data;
        this.collectionSize = 1;
        this.keyItemSearch = "";
        this.selectedItem = null;
      },
      (error) => {
        this.notificationService.showError('Failed to load items');
      }
    );
  }

  setSelectedItemByBarcode(event: { item: Item }) {
    this.selectedItem = event.item;
    this.items = [];
    this.itemService.findAllByBarcodeLike(this.barcode).subscribe(
      (data: Item[]) => {
        this.items = data;
        this.collectionSize = 1;
        this.barcode = "";
        this.selectedItem = null;
      },
      (error) => {
        this.notificationService.showError('Failed to load items by barcode');
      }
    );
  }

  loadItemCategories() {
    this.itemCategoryService.findByName(this.keyItemCategory).subscribe(
      (data: ItemCategory[]) => {
        this.itemCategories = data;
      },
      (error) => {
        this.notificationService.showError('Failed to load item categories');
      }
    );
  }

  setSelectedItemCategory(event: { item: ItemCategory }) {
    this.selectedItemCategory = event.item;
    this.searchByCategory();
  }

  loadBrands(event?: any) {
    
    const query = event ? event.query : this.keyBrand;
    this.brandService.findByName(query).subscribe(
      (data: Brand[]) => {
        this.brands = data;
      },
      (error) => {
        this.notificationService.showError('Failed to load brands');
      }
    );
  }

  setSelectedBrand(event: { item: Brand }) {
    this.selectedBrand = event.item;
    this.searchByBrand();
  }

  searchByCategory() {
    this.items = [];
    this.itemService.findAllByCategory(this.selectedItemCategory!).subscribe(
      (data: Item[]) => {
        this.items = data;
        this.collectionSize = 1;
        this.keyItemCategory = "";
        this.selectedItemCategory = null;
      },
      (error) => {
        this.notificationService.showError('Failed to search by category');
      }
    );
  }

  searchByBrand() {
    this.items = [];
    this.itemService.findAllByBrand(this.selectedBrand!).subscribe(
      (data: Item[]) => {
        this.items = data;
        this.collectionSize = 1;
        this.keyBrand = "";
        this.selectedBrand = null;
      },
      (error) => {
        this.notificationService.showError('Failed to search by brand');
      }
    );
  }

  selectRecord(item: Item, index: number) {
    this.itemService.findOneById(item.id).subscribe(
      (itm: Item) => {
        this.selectedItem = itm;
      },
      (error) => {
        this.notificationService.showError('Failed to select item');
      }
    );
    this.selectedRow = index;
  }

  edit() {
    if (this.selectedItem?.id) {
      this.modalRef = this.modalService.show(CreateItemComponent, {
        class: 'modal-xl',
        initialState: {
          item: this.selectedItem,
          isEditing: true,
          isActive: this.selectedItem.active,
          isManageStock: this.selectedItem.manageStock,
          keyUOM: this.selectedItem.uom?.name || '', // Use optional chaining and fallback
          keyItemCategory: this.selectedItem.itemCategory?.categoryName || '', // Use optional chaining and fallback
          selectedItemCategory: this.selectedItem.itemCategory,
          discount: this.calculateDiscount(),
          keySubCategory: this.selectedItem.subCategory?.subCategoryName || '', // Use optional chaining and fallback
          itemTypes: this.selectedItem.itemType?.id,
          isService: this.selectedItem.itemType?.name === 'Service', // Use optional chaining
          selectedUom: this.selectedItem.uom,
          selectedBrand: this.selectedItem.brand,
          itemTypeId: this.selectedItem.itemType?.id,
          rackId: this.selectedItem.rack?.id // Use optional chaining
        }
      } as ModalOptions);

      this.modalService.onHide.subscribe(() => {
        this.findAllItems();
      });

      this.modalRef.content.modalRef = this.modalRef;
    } else {
      this.notificationService.showError('Please select an Item');
    }
  }

  openModalBarcode() {
    if (this.selectedItem?.id) {
      this.modalRef = this.modalService.show(BarcodeComponent, {
        class: 'modal-md',
        initialState: {
          barcode1: this.selectedItem.barcode,
          price1: this.selectedItem.sellingPrice,
          itemName1: this.selectedItem.itemName,
          itemCode1: this.selectedItem.itemCode,
          barcode2: this.selectedItem.barcode,
          price2: this.selectedItem.sellingPrice,
          itemName2: this.selectedItem.itemName,
          itemCode2: this.selectedItem.itemCode
        }
      } as ModalOptions);

      this.modalRef.content.modalRef = this.modalRef;
    } else {
      this.notificationService.showError('Please select an Item');
    }
  }

  private calculateDiscount(): number {
    if (this.selectedItem?.itemCost && this.selectedItem.sellingPrice) {
      return (this.selectedItem.sellingPrice - this.selectedItem.itemCost) * 100 / this.selectedItem.itemCost;
    }
    return 0;
  }
}
