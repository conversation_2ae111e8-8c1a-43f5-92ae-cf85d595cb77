<div class="card">
  <div class="card-header">
    <strong>VIEW ALL ITEMS</strong>
  </div>
  <div class="card-body">
    <div class="row">

      <div class="col-md-3">
        <div class="input-group mb-2">
          <input [(ngModel)]="keyItemSearch"
                 [typeahead]="itemSearched"
                 (typeaheadLoading)="loadItems()"
                 (typeaheadOnSelect)="setSelectedItem($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadOptionField="itemName"
                 placeholder="Search By Items"
                 autocomplete="off"
                 size="16"
                 class="form-control" name="searchItem">
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-group">
          <input [(ngModel)]="barcode"
                 [typeahead]="itemSearched"
                 (typeaheadLoading)="loadItemBarcode()"
                 (typeaheadOnSelect)="setSelectedItemByBarcode($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadOptionField="barcode"
                 autocomplete="off"
                 placeholder="Search By Barcode"
                 class="form-control" name="category">
        </div>
      </div>
      <div class="col-md-3">
        <div class="input-group">
          <input [(ngModel)]="keyItemCategory"
                 [typeahead]="itemCategories"
                 (typeaheadLoading)="loadItemCategories()"
                 (typeaheadOnSelect)="setSelectedItemCategory($event)"
                 [typeaheadOptionsLimit]="7"
                 typeaheadOptionField="categoryName"
                 placeholder="Search By Category"
                 autocomplete="off"
                 size="16"
                 #category="ngModel"
                 class="form-control " name="category">
        </div>
      </div>
      <div class="col-md-3">
        <div class="input-group">
          <input [(ngModel)]="keyBrand"
                 [typeahead]="brands"
                 (typeaheadLoading)="loadBrands()"
                 (typeaheadOnSelect)="setSelectedBrand($event)"
                 [typeaheadOptionsLimit]="10"
                 typeaheadOptionField="name"
                 placeholder="search By Brand"
                 autocomplete="off"
                 id="appendedInputButtons" size="16"
                 #brand="ngModel"
                 class="form-control m-" name="brand">
        </div>
      </div>
    </div>

    <div class="modal-body" (load)="findAllItems()">
      <table class="table table-striped">
        <thead align="center">
        <tr>
          <th scope="col">BarCode</th>
          <th scope="col">Item Name</th>
          <th scope="col">Category</th>
          <th scope="col">Brand</th>
          <th scope="col">Price</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let item of items,let i = index"
            (click)="selectRecord(item,i)"
            [class.active]="i === selectedRow" align="center">
          <td>{{item.barcode}}</td>
          <td>{{item.itemName}}</td>
          <td>{{item.itemCategory ? item.itemCategory.categoryName : 'N/A'}}</td>
          <td>{{item.brand ? item.brand.name : 'N/A'}}</td>
          <td>{{item.sellingPrice}}</td>
        </tr>
        </tbody>
      </table>
      <div class="row g-3">
        <div class="col-md-12 d-flex justify-content-center">
          <pagination
            class="pagination-sm"
            [totalItems]="collectionSize"
            [(ngModel)]="page"
            [maxSize]="15"
            [boundaryLinks]="true"
            (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>
      <div class="text-end">
        <button type="button" class="btn btn-primary me-1" (click)="loadItems()">Reset Table</button>
        <button type="button" class="btn btn-warning me-1" (click)="edit()">Edit</button>
        <button type="button" class="btn btn-success" (click)="openModalBarcode()">Barcode</button>
      </div>
    </div>
  </div>
</div>




