import {Component, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';
import {ItemCategory} from '../../../model/item-category';
import {ItemCategoryService} from '../../../service/item-category.service';
import {NotificationService} from '../../../../../core/service/notification.service';

@Component({
  standalone: false,
  selector: 'app-item-category',
  templateUrl: './item-category.component.html',
  styleUrls: ['./item-category.component.css']
})

export class ItemCategoryComponent implements OnInit {
  firstRecordIndex: number = 0;
  itemsPerPage: number = 10;


  currentUser = JSON.parse(localStorage.getItem('currentUser'));
  itemCategory: ItemCategory;
  keyItemCategory: string;
  itemCategories: Array<ItemCategory> = [];
  collectionSize;
  page;
  pageSize;
  selectedCategory: ItemCategory;
  modalRef: BsModalRef;
  itemCategoriesSearched: Array<ItemCategory> = [];

  constructor(private itemCategoryService: ItemCategoryService, private notification: NotificationService,
              private modalService: BsModalService) {
  }

  openModal(template: TemplateRef<any>) {
    if (this.selectedCategory) {
      this.modalRef = this.modalService.show(template, {class: 'modal-sm'});
    } else {
      this.notification.showError('please select a row!');
    }
  }


  confirmUpdate() {

    if (this.selectedCategory) {
      this.modalRef.hide();
      this.itemCategoryService.save(this.itemCategory).subscribe((result) => {
        this.notification.showSuccess(result);
      });
      this.ngOnInit();
    } else {
      this.notification.showError('plase select a row');
    }

  }

  decline(): void {
    this.modalRef.hide();
    this.ngOnInit();
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 10;
    this.selectedCategory = null;
    this.itemCategory = new ItemCategory();
    this.itemCategory.active = true;
    this.findAll();
  }

  pageChanged(event: any) {
    this.page = event.page + 1;
    this.findAll();
  }
  onPageChange(event: any) {
    const page = event.page + 1; // PrimeNG paginator is 0-indexed
    this.page = page;
    // Call your existing pagination method
    this.pageChanged({ page: page });
  }


  save() {
    this.itemCategoryService.save(this.itemCategory).subscribe(result => {
      this.notification.showSuccess(result);
      this.ngOnInit();
      this.clear();
    });
  }

  findAll() {
    this.itemCategoryService.findAll(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.itemCategories = result.content;
      this.collectionSize = result.totalPages * 10;
    });
  }

  clear() {
    this.ngOnInit();
    this.keyItemCategory = null;
  }

  onSelect(cat: ItemCategory) {
    this.selectedCategory = cat;
    this.itemCategory = this.selectedCategory;
  }

  loadItemCategories() {
    return this.itemCategoryService.findByName(this.keyItemCategory).subscribe((data: Array<ItemCategory>) => {
      return this.itemCategoriesSearched = data;
    });
  }

  setSelectedItemCategory(event) {
    this.itemCategory = event.item;
  }

  isCheckActivate(event){

  }

}
