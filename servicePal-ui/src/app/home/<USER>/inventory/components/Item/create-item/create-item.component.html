<div class="card">
  <div class="card-header">
    <strong>CREATE ITEM</strong>
  </div>
  <div class="card-body">
    <form (ngSubmit)="saveItem(createItemForm)" #createItemForm="ngForm">
      <div class="row g-3">
        <div class="mb-3 col-md-3">
          <label>Item Type</label>
          <div class="input-group">
            <select class="form-control" (change)="setSelectedItemType($event)" name="itemTypeSelect"
                    [(ngModel)]="itemTypeId" required #itemTypeSelect="ngModel"
                    [class.is-invalid]="itemTypeSelect.invalid && itemTypeSelect.touched">
              <option>-Select-</option>
              <option *ngFor="let ity of itemTypes" [value]="ity.id">
                {{ ity.name }}
              </option>
            </select>
          </div>
        </div>
        <div class="mb-3 col-md-3">
          <label>Barcode</label>
          <input type="text" required #barcode="ngModel"
                 [class.is-invalid]="barcode.invalid && barcode.touched"
                 class="form-control" id="barcode" [(ngModel)]="item.barcode" name="barcode"
                 placeholder="barcode" (ngModelChange)="checkItemAvailable()" autocomplete="off">
          <small class="text-danger" [class.d-none]="!itemAvailable">This item is already available</small>
        </div>
        <div class="mb-3 col-md-3">
          <label>Item Name</label>
          <input type="text" required #itemName="ngModel" [class.is-invalid]="itemName.invalid && itemName.touched"
                 class="form-control" id="itemName" [(ngModel)]="item.itemName" name="itemName"
                 placeholder="item Name" autocomplete="off">
        </div>
        <div class="mb-3 col-md-3">
          <label>Item Category</label>
          <div class="input-group">
            <input [(ngModel)]="keyItemCategory"
                   [typeahead]="categories"
                   (typeaheadLoading)="loadItemCategories()"
                   (typeaheadOnSelect)="setSelectedItemCategory($event)"
                   typeaheadOptionField="categoryName"
                   placeholder="Search Item Categories"
                   autocomplete="off" size="16"
                   #category="ngModel"
                   [class.is-invalid]="selectedItemCategory.id && category.touched"
                   class="form-control" name="category">
            <button class="btn btn-primary fa fa-plus" (click)="openModalLarge(templateItemCategory)"
                    type="button"></button>
          </div>
        </div>
        <div class="mb-3 col-md-3">
          <label>Sub Category</label>
          <div class="input-group">
            <input [(ngModel)]="keySubCategory"
                   [typeahead]="subCategories"
                   (typeaheadLoading)="loadSubCategories()"
                   (typeaheadOnSelect)="setSelectedSubCategory($event)"
                   typeaheadOptionField="subCategoryName"
                   placeholder="Search Sub Categories"
                   autocomplete="off"
                   size="16"
                   #subCategory="ngModel" name="subCategory"
                   [class.is-invalid]="selectedSubCategory.id && subCategory.touched"
                   class="form-control">
            <button class="btn btn-primary fa fa-plus" (click)="openModalExtraLarge(templateSubCategory)"
                    type="button"></button>
          </div>
        </div>
        <div class="mb-3 col-md-6">
          <label>Description</label>
          <input type="text" #description="ngModel" [class.is-invalid]="description.invalid && description.touched"
                 class="form-control" id="Description" [(ngModel)]="item.description" name="Description"
                 placeholder="Description">
        </div>

        <div class="mb-3 col-md-3">
          <label>UOM</label>
          <div class="input-group">
            <input [(ngModel)]="keyUOM"
                   [typeahead]="unitOfMeasure"
                   (typeaheadLoading)="loadUOM()"
                   (typeaheadOnSelect)="setSelectedUOM($event)"
                   [typeaheadOptionsLimit]="7"
                   typeaheadOptionField="name"
                   placeholder="Search UOM"
                   autocomplete="off"
                   size="16"
                   #uom="ngModel" [class.is-invalid]="selectedUom.id == undefined && uom.touched"
                   class="form-control" name="uom">
            <button class="btn btn-primary fa fa-plus" (click)="openModalLarge(templateUom)"
                    type="button"></button>
          </div>
        </div>

        <div class="mb-3 col-md-3">
          <label>Common Code</label>
          <input type="text" #commonCode="ngModel"
                 class="form-control" id="commonCode" [(ngModel)]="item.commonCode" name="commonCode"
                 placeholder="Common Code">
        </div>
        <div class="mb-3 col-md-3">
          <label>Brand</label>
          <div class="input-group">
            <input [(ngModel)]="keyBrand"
                   [typeahead]="brands"
                   (typeaheadLoading)="loadBrands()"
                   (typeaheadOnSelect)="setSelectedBrand($event)"
                   [typeaheadOptionsLimit]="10"

                   typeaheadOptionField="name"
                   placeholder="Search Brand"
                   autocomplete="off"
                   id="appendedInputButtons" size="16"
                   class="form-control m-" name="brand">
            <button class="btn btn-primary fa fa-plus" (click)="openModalLarge(templateBrand)"
                    type="button"></button>
          </div>
        </div>
        <div class="mb-3 col-md-2" *ngIf="!isService">
          <label>Item Cost</label>
          <input type="number" #itemCost="ngModel" class="form-control" id="itemCost" [(ngModel)]="item.itemCost"
                 name="itemCost" (ngModelChange)="setSellingPrice()"
                 placeholder="Selling Price" [class.is-invalid]="itemCost.invalid && itemCost.touched">
        </div>
        <div class="mb-3 col-md-2" *ngIf="!isService">
          <label>Supplier Discount</label>
          <input type="number" #itemCostPercentage="ngModel"
                 class="form-control" id="itemCostPercentage" [(ngModel)]="discount" (ngModelChange)="setSellingPrice()"
                 name="itemCostPercentage">
        </div>
        <div class="mb-3 col-md-4" *ngIf="isService">
          <label>Employee Commission</label>
          <input type="number" class="form-control" id="employeeDiscount" [(ngModel)]="item.serviceCommission"
                 name="employeeCommission">
        </div>
        <div class="mb-3 col-md-2"><label>Selling Price</label>
          <input type="number" #sellingPrice="ngModel" required
                 class="form-control" id="sellingPrice" [(ngModel)]="item.sellingPrice" name="sellingPrice"
                 placeholder="Selling Price" [class.is-invalid]="sellingPrice.invalid && sellingPrice.touched">
        </div>
        <div class="mb-3 col-md-3">
          <label>Used For Model</label>
          <input type="text" #ModelNo="ngModel"
                 class="form-control" id="ModelNo" [(ngModel)]="item.model" name="ModelNo"
                 placeholder="Used For Model">
        </div>
        <div class="mb-3 col-md-3">
          <label>Rack or Location</label>
          <div class="input-group">
            <select (change)="onChangeRack(rackId)"
                    class="form-control" id="location" [(ngModel)]="rackId" name="location"
                    placeholder="Rack or Location">
              <option>-Select-</option>
              <option *ngFor="let rack of racks" [value]="rack.id">{{ rack.rackNo }}</option>
            </select>
            <button class="btn btn-primary fa fa-plus" (click)="newRack()"
                    type="button"></button>
          </div>
        </div>
        <div class="mb-3 col-md-3">
          <label>Dead Stock Level</label>
          <input type="number" #dStockeLevel="ngModel"
                 class="form-control" id="dSokeLevel" [(ngModel)]="item.deadStockLevel" name="dStockeLevel"
                 placeholder="Dead Stock Level">
        </div>
        <div class="mb-3 col-md-3">
          <label>Initial Quantity</label>
          <input type="number" #initQuantity="ngModel"
                 class="form-control" id="initQuantity" [(ngModel)]="item.quantity" name="initQuantity"
                 placeholder="Initial Quantity">
        </div>
        <div class="mb-3 col-md-6">
          <div class="form-check checkbox me-2">
            <input class="form-check-input" id="check2" name="check2" type="checkbox" value=""
                   [(ngModel)]="item.manageStock">
            <label class="form-check-label" for="check2">Manage Stock</label>
          </div>
          <div class="form-check checkbox me-2">
            <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                   [(ngModel)]="item.active">
            <label class="form-check-label" for="check3">Active</label>
          </div>
          <div class="form-check checkbox me-2">
            <input class="form-check-input" id="check4" name="check4" type="checkbox" value=""
                   [(ngModel)]="item.autoInactive">
            <label class="form-check-label" for="check3">Auto Inactive</label>
          </div>
        </div>

        <div class="col-md-6 w-100">
          <div class="float-end mt-4">
            <button class="btn btn-success me-3" [disabled]="(!createItemForm.form.valid || itemAvailable)">save
            </button>
            <button class="btn btn-success me-3" [disabled]="(!createItemForm.form.valid || itemAvailable)"
                    type="button" (click)="saveAndBarcode(createItemForm)">save and Barcode
            </button>
            <button class="btn btn-warning" type="button" (click)="clearForm()">clear</button>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

<ng-template #templateBrand>
  <div class="modal-header">
    <h4 class="modal-title float-start">Add Brand</h4>
    <button type="button" class="close float-end" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <app-brand></app-brand>
  </div>
</ng-template>

<ng-template #templateItemCategory>
  <div class="modal-header">
    <h4 class="modal-title float-start">Add Item Category</h4>
    <button type="button" class="close float-end" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <app-item-category></app-item-category>
  </div>
</ng-template>

<ng-template #templateSubCategory>
  <div class="modal-header">
    <h4 class="modal-title float-start">Add Sub Category</h4>
    <button type="button" class="close float-end" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <app-sub-category></app-sub-category>
  </div>
</ng-template>

<ng-template #templateItemType>
  <div class="modal-header">
    <h4 class="modal-title float-start">Add Item Type</h4>
    <button type="button" class="close float-end" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <app-item-type></app-item-type>
  </div>
</ng-template>

<ng-template #templateUom>
  <div class="modal-header">
    <h4 class="modal-title float-start">Add Unit Of Measure</h4>
    <button type="button" class="close float-end" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <app-uom></app-uom>
  </div>
</ng-template>

