<div>
  <div class="card">
    <div class="card-header">
      <strong>MANAGE ITEM CATEGORY</strong>
    </div>
    <div class="card-body">

      <div class="row g-3">
        <div class="col-md-6">
          <div class="form-group">
            <div class="input-group mb-2">

              <input [(ngModel)]="keyItemCategory"
                     [typeahead]="itemCategoriesSearched"
                     (typeaheadLoading)="loadItemCategories()"
                     (typeaheadOnSelect)="setSelectedItemCategory($event)"
                     [typeaheadOptionsLimit]="7"

                     typeaheadOptionField="categoryName"
                     placeholder="Search Item Categories"
                     autocomplete="off"
                     size="16"
                     class="form-control" name="category">
            </div>
          </div>

          <table class="table table-hover">
            <thead>
            <tr>
              <th>Category Name</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let itemCat of itemCategories"
                (click)="onSelect(itemCat)" [class.active]="itemCat === selectedCategory">
              <td>{{ itemCat.categoryName }}</td>
            </tr>
            </tbody>
          </table>

          <div class="row g-3">
            <div class="col-xs-12 col-12">
              <pagination class="pagination-sm justify-content-center"
                          [totalItems]="collectionSize"
                          [(ngModel)]="page"
                          [boundaryLinks]="true"
                          [maxSize]="10"
                          (pageChanged)="pageChanged($event)">
              </pagination>
            </div>
          </div>


        </div>
        <div class="col-md-6">
          <form (ngSubmit)="save()" #ManageItemCetogoryForm="ngForm">
            <label>Item Category Name</label>
            <div class="form-group">
              <input type="text" required #itemCategoryName="ngModel"
                     class="form-control" id="itemCategoryName" [(ngModel)]="itemCategory.categoryName"
                     name="categoryName"
                     placeholder=" Item Category Name">
              <div *ngIf="itemCategoryName.errors && (itemCategoryName.invalid || itemCategoryName.touched)">
                <small class="text-danger" [class.d-none]="itemCategoryName.valid || itemCategoryName.untouched">*item
                  type Name is required
                </small>
              </div>
            </div>

            <div class="form-check checkbox me-2">
              <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                     [(ngModel)]="itemCategory.active" (change)="isCheckActivate($event)">
              <label class="form-check-label" for="check3">Active</label>
            </div>


            <div class="float-end">
              <button class="btn btn-success me-3"
                      [disabled]="selectedCategory!=null||!ManageItemCetogoryForm.form.valid">
                save
              </button>
              <button type="button" class="btn btn-primary me-3"
                      [disabled]="selectedCategory===null"
                      (click)="openModal(templateUpdate)">update
              </button>
              <button type="button" (click)="clear()" class="btn btn-warning">clear</button>
            </div>
          </form>
        </div>
      </div>

    </div>
  </div>
</div>

<ng-template #templateUpdate>
  <div class="modal-body text-center ">
    <p>Do you want to confirm?</p>
    <button type="button" class="btn btn-default" (click)="confirmUpdate()">Yes</button>
    <button type="button" class="btn btn-primary" (click)="decline()">No</button>
  </div>
</ng-template>


