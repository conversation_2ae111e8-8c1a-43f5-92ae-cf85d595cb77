import {Component, OnInit} from '@angular/core';
import {UOM} from '../../model/uom';
import {NotificationService} from '../../../../core/service/notification.service';
import {UomService} from '../../service/uom.service';

@Component({
  standalone: false,
  selector: 'app-uom',
  templateUrl: './uom.component.html',
  styleUrls: ['./uom.component.css']
})
export class UOMComponent implements OnInit {
  firstRecordIndex: number = 0;
  itemsPerPage: number = 10;

  uom: UOM;
  isWholeNumber: boolean;
  units: Array<UOM> = [];
  setClickedRow: Function;
  selectedRow: number;
  page = 1;
  pageSize = 10;
  collectionSize;

  constructor(private uomService: UomService,
              private notificationService: NotificationService) {

  }

  ngOnInit() {
    this.findAll();
    this.uom = new UOM();
    this.uom.active = true;
  }

  findAll() {
    this.uomService.findAll(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.units = result.content;
      this.collectionSize = result.totalPages * 10;
    });
  }

  pageChanged(event) {
    this.page = event.page;
    this.findAll();
  }
  onPageChange(event: any) {
    const page = event.page + 1; // PrimeNG paginator is 0-indexed
    this.page = page;
    // Call your existing pagination method
    this.pageChanged({ page: page });
  }


  saveUOM() {
    this.uomService.save(this.uom).subscribe(result => {
      this.notificationService.showSuccess(result);
      this.ngOnInit();
    }, error => {
      console.log(error);
    });
  }


  uomDetail(uom,index) {
    this.uom = uom;
    this.selectedRow = index;
  }

  clearAll() {
    this.ngOnInit();
  }

}
