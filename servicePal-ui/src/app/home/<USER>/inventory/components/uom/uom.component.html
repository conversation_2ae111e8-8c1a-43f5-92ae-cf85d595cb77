<div class="card">
  <div class="card-header">
    <strong>MANAGE UNITS OF MEASUREMENT</strong>
  </div>
  <div class="card-body">
    <form #uomForm="ngForm" (ngSubmit)="saveUOM(); uomForm.reset()">
      <div class="row g-3">
        <div class="col-md-6">
          <div class="form-group">
            <label>Unit Name</label>
            <input type="text" required #name="ngModel"
                   [class.is-invalid]="name.invalid && name.touched"
                   class="form-control" id="name" [(ngModel)]="uom.name" name="name"
                   placeholder="Unit Name">
            <small class="text-danger" [class.d-none]="name.valid || name.untouched">*Name is required
            </small>
          </div>

          <div class="form-group">
            <label>Symbol</label>
            <input type="text" required #symbol="ngModel"
                   [class.is-invalid]="symbol.invalid && symbol.touched"
                   class="form-control" id="symbol" [(ngModel)]="uom.symbol" name="symbol"
                   placeholder="Unit Symbol">
            <small class="text-danger" [class.d-none]="symbol.valid || symbol.untouched">*Symbol is required
            </small>
          </div>

          <div class="form-check checkbox me-2 mb-3">
            <input class="form-check-input" id="check1" name="check1" type="checkbox" value=""
                   [(ngModel)]="uom.active">
            <label class="form-check-label" for="check1">Active</label>
          </div>

          <div class="row float-end">
            <div class="me-3">
              <button type="submit" class="btn btn-success" [disabled]="!uomForm.form.valid">{{uom.id ? 'Update' : 'Save'}}</button>
            </div>
            <div class="me-3">
              <button type="button" class="btn btn-warning" (click)="clearAll(); uomForm.reset()">Clear</button>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="table-height">
            <table class="table table-striped">
              <thead align="center">
              <tr>
                <th scope="col">Name</th>
                <th scope="col">Symbol</th>
                <th scope="col">Active</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let unit of units; let i = index"
                  (click)="uomDetail(unit, i)" [class.active]="i === selectedRow">
                <td>{{unit.name}}</td>
                <td>{{unit.symbol}}</td>
                <td>
                  <span [class.text-success]="unit.active" [class.text-danger]="!unit.active">
                    {{unit.active ? 'Yes' : 'No'}}
                  </span>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </form>
  </div>

  <div class="col-xs-12 col-12 ">
    <!-- Updated to PrimeNG paginator -->
    <p-paginator
      [(page)]="page"
      [totalRecords]="collectionSize"
      [rows]="pageSize"
      [rowsPerPageOptions]="[5,10,20,50]"
      (onPageChange)="pageChanged($event)">
    </p-paginator>
  </div>
</div>