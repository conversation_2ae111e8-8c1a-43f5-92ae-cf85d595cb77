<div>
  <div class="card">
    <div class="card-header">
      <strong>MANAGE UNIT OF MEASURE</strong>

    </div>
    <div class="card-body">
      <form (ngSubmit)="saveUOM(); UOMForm.reset()" #UOMForm="ngForm" >
        <div class="row">
          <div class="col-md-6">
            <input type="text" class="form-control" id="uom" name="uom" placeholder="Search Unit">
            <br>
            <table class="table">
              <thead>
              <tr style="text-align: center">
                <th scope="col">Unit Name</th>
                <th scope="col">Symbol</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let uom of units,let i=index" (click)="uomDetail(uom,i) "  [class.active]="i === selectedRow">
                <td>{{uom.name}}</td>
                <td>{{uom.symbol}}</td>
              </tr>
              </tbody>
            </table>
            <div class="row">
              <div class="col-xs-12 col-12 ">
                <pagination class="pagination-sm justify-content-center"
                  [totalItems]="collectionSize"
                  [(ngModel)]="page"
                  [boundaryLinks]="true"
                  [maxSize]="10"
                  (pageChanged)="pageChanged($event)"
                  [ngModelOptions]="{standalone: true}">
                </pagination>
              </div>
            </div>

          </div>
          <div class="col-md-6">
            <div class="form-group">
            <label>Unit Name</label>
            <input type="text" required #UserName="ngModel" [class.is-invalid]="UserName.invalid && UserName.touched"
                   class="form-control" id="bName" [(ngModel)]="uom.name" name="bName"
                   placeholder="Unit Name ">
            <div *ngIf="UserName.errors && (UserName.invalid || UserName.touched)">
              <small class="text-danger" [class.d-none]="UserName.valid || UserName.untouched">*Unite Name is required
              </small>
            </div>
            </div>

            <label>Symbol</label>
            <div class="form-group">
            <input type="text" required #Symbol="ngModel" [class.is-invalid]="Symbol.invalid && Symbol.touched"
                   class="form-control" id="Symbol" [(ngModel)]="uom.symbol" name="uom" placeholder="Symbol">
            <div *ngIf="Symbol.errors && (Symbol.invalid || Symbol.touched)">
              <small class="text-danger" [class.d-none]="Symbol.valid || Symbol.untouched">*Symbol is required
              </small>
            </div>
            </div>

            <div class="form-check checkbox me-2">
              <input class="form-check-input" id="check" name="check" type="checkbox" value=""
                     [(ngModel)]="uom.isWholeNumber">
              <label class="form-check-label" for="check">Is Whole Number</label>
            </div>
            <br>
            <div class="form-check checkbox me-2">
              <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                     [(ngModel)]="uom.active">
              <label class="form-check-label" for="check3">Active</label>
            </div>
            <br>

            <div class="row float-end ">
              <div class="me-3">
                <button type="submit" class="btn btn-success" [disabled]="!UOMForm.form.valid">save</button>
              </div>

              <!--<div class="col-6 col-sm-4 col-md-2 col-xl mb-3  mb-xl-0">-->
                <!--<button class="btn btn-danger" (click)="removeUOM()" [disabled]="!UOMForm.form.valid">delete</button>-->
              <!--</div>-->
              <div class="me-3">
                <button type="button" class="btn btn-warning" [disabled]="!UOMForm.form.valid" (click)="clearAll()">clear</button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
