import {Component, OnInit} from '@angular/core';
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {MetaData} from "../../../../core/model/metaData";
import {PurchaseInvoice} from "../../model/purchase-invoice";
import {PurchaseInvoiceService} from "../../service/purchase-invoice.service";
import {NotificationService} from "../../../../core/service/notification.service";
import {PaySiBalanceComponent} from "../pay-si-balance/pay-si-balance.component";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {ViewPiComponent} from "../view-pi/view-pi.component";

@Component({
  standalone: false,
  selector: 'app-manage-pi',
  templateUrl: './manage-pi.component.html',
  styleUrls: ['./manage-pi.component.css']
})
export class ManagePiComponent implements OnInit {

  pis: Array<PurchaseInvoice> = [];
  selectedPi: PurchaseInvoice;

  keySupplierId: string;
  keyInvoiceNo: string;
  keyDate: Date;
  searchFilter: string;

  page;
  pageSize;
  collectionSize;

  selectedRow: number;
  modalRef: BsModalRef;

  piStatus: MetaData;
  piStatusList: Array<MetaData>;
  paymentStatusId: string;

  constructor(private piService: PurchaseInvoiceService, private modalService: BsModalService,
              private notificationService: NotificationService, private metaDataService: MetaDataService) {
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 10;
    this.findAll();
    this.loadStatus();
  }

  findAll() {
    this.piService.findAllPages(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.pis = result.content;
      this.collectionSize = result.totalPages * 10;
    });
  }

  loadStatus(){
    this.metaDataService.findByCategory("PaymentStatus").subscribe((data: Array<MetaData>)=>{
      this.piStatusList = data;
    });
  }

  selectPi(si, index) {
    this.selectedRow = index;
    this.selectedPi = si;
  }

  searchPi() {
    console.log(this.searchFilter)
    console.log(this.keyDate)
    if (this.searchFilter === 'inv') {
      this.piService.findAllByInvoiceNo(this.keyInvoiceNo).subscribe((data: PurchaseInvoice) => {
        this.pis = [];
        this.pis.push(data);
      });
    }
    if (this.searchFilter === 'sup') {
      this.piService.findAllBySupplier(this.keySupplierId).subscribe((data: Array<PurchaseInvoice>) => {
        this.pis = [];
        this.pis = data;
      });
    }
    if (this.searchFilter === 'date'){
      this.piService.findAllByDate(this.keyDate.toLocaleDateString()).subscribe((data: Array<PurchaseInvoice>)=>{
        this.pis = [];
        this.pis = data;
      });
    }
  }

  setSearchFilter(filter) {
    this.searchFilter = filter;
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  showDetails() {

  }

  print() {
    this.modalRef = this.modalService.show(ViewPiComponent, <ModalOptions>{class: 'modal-lg'});
    this.modalRef.content.modalRef = this.modalRef;
    this.modalRef.content.findSi(this.selectedPi.invoiceNo);
  }

  payBalance() {
    this.modalRef = this.modalService.show(PaySiBalanceComponent, <ModalOptions>{class: 'modal-md'});
    this.modalRef.content.isPurchaseInvoice = true;
    this.modalRef.content.invoiceNo = this.selectedPi.purchaseInvoiceNo;
    this.modalRef.content.supplier = this.selectedPi.supplier;
    this.modalRef.content.totalAmount = this.selectedPi.totalAmount;
    this.modalRef.content.balance = this.selectedPi.balance;
    this.modalRef.content.modalRef = this.modalRef;
    this.modalRef.onHide.subscribe(result =>{
      this.findAll();
    })
  }

  findAllByStatus() {
    this.piService.findAllByStatus(this.paymentStatusId).subscribe((data: Array<PurchaseInvoice>)=>{
      this.pis = [];
      this.pis = data;
    });
  }
}
