<div class="card">
  <div class="card-header">
    <strong>Manage Purchase Invoices</strong>
  </div>
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-2">
        <div class="form-group">
          <select name="paymentStatus" id="paymentStatus"
                  [(ngModel)]="paymentStatusId" (change)="findAllByStatus()"
                  class="form-control">
            <option [value]="undefined" disabled>Payment Status</option>
            <option *ngFor="let status of piStatusList" [value]="status.id">{{status.value}}</option>
          </select>
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-group">
          <input [(ngModel)]="keyInvoiceNo"
                 placeholder="search By Invoice No"
                 autocomplete="off" size="16"
                 class="form-control" name="invNo" (change)="setSearchFilter('inv')">
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-group">
          <input [(ngModel)]="keySupplierId"
                 placeholder="search By Supplier"
                 autocomplete="off" size="16"
                 class="form-control" name="invNo" (change)="setSearchFilter('sup')">
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-group">
          <input [(ngModel)]="keyDate" size="16" [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
                 placeholder="search by date" autocomplete="off" bsDatepicker
                 type="text" class="form-control" (ngModelChange)="setSearchFilter('date')">
        </div>
      </div>
      <div class="col-md-1">
        <button class="btn btn-primary" (click)="searchPi()">Search</button>
      </div>
    </div>

    <div class="row g-3 mt-2">
      <table class="table table-striped">
        <thead align="center">
        <tr>
          <th scope="col">Invoice No</th>
          <th scope="col">Supplier Name</th>
          <th scope="col">Date</th>
          <th scope="col">Amount</th>
          <th scope="col">Balance</th>
          <th scope="col">Status</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let pi of pis,let i = index"
            (click)="selectPi(pi,i)"
            [class.active]="i === selectedRow" align="center">
          <td>{{pi.purchaseInvoiceNo}}</td>
          <td>{{pi.supplier.name}}</td>
          <td>{{pi.date}}</td>
          <td>{{pi.totalAmount | number : '1.2-2'}}</td>
          <td>{{pi.balance | number : '1.2-2'}}</td>
          <td>{{pi.status.value}}</td>
        </tr>
        </tbody>
      </table>
      <div class="row g-3">
        <div class="col-md-12">
          <pagination class="pagination-sm justify-content-center"
            [totalItems]="collectionSize"
            [(ngModel)]="page"
            (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>
    </div>
    <div class="row g-3 text-end">
      <div class="col-md-12">
        <button type="button" class="btn btn-outline-danger" [disabled]="!selectedRow" (click)="payBalance()">Pay Balance</button>
        <button type="button" class="btn btn-danger ms-2" (click)="print()">View</button>
      </div>
    </div>
  </div>
</div>
