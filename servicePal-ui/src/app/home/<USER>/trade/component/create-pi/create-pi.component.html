<div class="card">
  <div class="card-header">
    <strong>CREATE PURCHASE INVOICE</strong>
  </div>
  <div class="card-body py-2">
    <div class="row g-3">
      <div class="col-md-6">
        <form #piForm="ngForm">
          <div class="row g-3">
            <div class="mb-3 col-md-6">
              <label>Date</label>
              <input #piDate="ngModel" [(ngModel)]="purchaseInvoice.date" [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
                     [class.is-invalid]="piDate.invalid && piDate.touched" bsDatepicker
                     class="form-control" id="piDate" name="piDate"
                     placeholder="Enter Invoice Date"
                     required
                     type="text">
              <small [class.d-none]="piDate.valid || piDate.untouched" class="text-danger">*Invoice Date is required
              </small>
            </div>

            <div class="mb-3 col-md-6">
              <label>Due Date</label>
              <input #dueDate="ngModel" [(ngModel)]="purchaseInvoice.dueDate"
                     [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
                     [class.is-invalid]="dueDate.invalid && dueDate.touched" bsDatepicker
                     class="form-control" id="dueDate" name="dueDate"
                     placeholder="Enter Due Date"
                     required
                     type="text">
              <small [class.d-none]="dueDate.valid || dueDate.untouched" class="text-danger">*Due Date is required
              </small>
            </div>

            <div class="mb-3 col-md-6">
              <label>Supplier</label>
              <div class="input-group">
                <input (typeaheadLoading)="searchSuppliers()"
                       (typeaheadOnSelect)="setSelectedSupplier($event)"
                       [(ngModel)]="keySupplierSearch"
                       [typeaheadOptionsLimit]="15"
                       [typeahead]="supplierSearchList"
                       autocomplete="off"
                       class="form-control"
                       name="searchCustomer"
                       placeholder="Search Customers"
                       size="16"
                       typeaheadOptionField="name" >
                  <button (click)="showSupplierModal()" class="btn btn-primary fa fa-plus"
                          type="button"></button>
              </div>
            </div>

            <div class="col-md-6 form-group">
              <label>Invoice No</label>
              <input #invoiceNo="ngModel" [(ngModel)]="purchaseInvoice.invoiceNo"
                     [class.is-invalid]="invoiceNo.invalid && invoiceNo.touched" class="form-control"
                     id="invoiceNo"
                     name="invoiceNo" placeholder="Invoice No" required type="text">
              <small [class.d-none]="invoiceNo.valid || invoiceNo.untouched" class="text-danger">*Invoice No Required
              </small>
            </div>

            <div class="col-md-6 form-group">
              <label>Warehouse</label>
              <select class="form-control" required #selectedWh="ngModel"
                      [class.is-invalid]="selectedWh.invalid && selectedWh.touched" name="selectedWh"
                      [(ngModel)]="selectedWarehouse">
                <option [ngValue]="">Select Warehouse</option>
                <option *ngFor="let wh of warehouses" [ngValue]="wh">{{wh.name}}</option>
              </select>
            </div>

            <div class="col-md-12">
              <hr>
            </div>

            <div class="col-md-6 form-group">
              <label>Barcode</label>
              <div class="input-group">
                <input #code="ngModel"
                       (typeaheadLoading)="loadItemByCode()"
                       (typeaheadOnSelect)="setSelectedItem($event)"
                       [(ngModel)]="keyBarcodeSearch"
                       [class.is-invalid]="code.invalid && code.touched"
                       [typeaheadOptionsLimit]="15"
                       [typeahead]="itemSearchList"
                       autocomplete="off"
                       class="form-control"
                       id="code" name="code" placeholder="Select the Item" required
                       type="text" typeaheadOptionField="barcode" >
              </div>
            </div>

            <div class="col-md-6 form-group">
              <label>Item Name</label>
              <div class="input-group">
                <input #name="ngModel"
                       (typeaheadLoading)="searchItems()"
                       (typeaheadOnSelect)="setSelectedItem($event)"
                       [(ngModel)]="keyItemNameSearch"
                       [class.is-invalid]="name.invalid && name.touched"
                       [typeaheadOptionsLimit]="15"
                       [typeahead]="itemSearchList"
                       autocomplete="off"
                       class="form-control"
                       name="name"
                       placeholder="Search Item"
                       required
                       size="16"
                       typeaheadOptionField="itemName" >
                  <button (click)="showItemModal()" class="btn btn-primary fa fa-plus" type="button"></button>
              </div>
            </div>

            <div class="col-md-6 form-group">
              <label>Item Cost</label>
              <input #buyingPr="ngModel" [(ngModel)]="piRecord.itemCost"
                     [class.is-invalid]="buyingPr.invalid && buyingPr.touched"
                     class="form-control"
                     id="buyingPr" name="buyingPr" required type="number">
              <small [class.d-none]="buyingPr.valid || buyingPr.untouched" class="text-danger">*Item cost Required
              </small>
            </div>

            <div class="col-md-6 form-group">
              <label>Discount</label>
              <!--<div class="form-check form-check-inline mb-2" *ngFor="let discountType of discountMethodTypes">
                <input class="form-check-input" type="radio" name="discountMethod" [id]="discountType.id"
                       [value]="discountType.value"
                       [(ngModel)]="selectedDiscountType" checked required
                       (change)="setDiscountType(discountType.value)">
                <label class="form-check-label" for="Percentage">{{discountType.value}}</label>
              </div>-->
              <input #percentage="ngModel" (keyup)="setSellingPrice()" [(ngModel)]="percentageValue"
                     [class.is-invalid]="percentage.invalid && percentage.touched"
                     class="form-control" id="percentage" name="percentage" type="text">
              <small [class.d-none]="percentage.valid || percentage.untouched" class="text-danger">*Percentage Required
              </small>
            </div>

            <div class="col-md-6 form-group">
              <label>Selling Price</label>
              <input #sprice="ngModel" (keyup)="setPercentage()" [(ngModel)]="piRecord.sellingPrice"
                     [class.is-invalid]="sprice.invalid && sprice.touched"
                     class="form-control"
                     id="sprice" name="sprice" required type="number">
              <small [class.d-none]="sprice.valid || sprice.untouched" class="text-danger">*Selling Price is Required
              </small>
            </div>
            <div class="col-md-6 form-group">
              <label>Quantity</label>
              <input #quantity="ngModel" [(ngModel)]="piRecord.quantity"
                     [class.is-invalid]="quantity.invalid && quantity.touched"
                     class="form-control"
                     id="quantity" name="quantity" required type="number">
              <small [class.d-none]="quantity.valid || quantity.untouched" class="text-danger">*Quantity is Required
              </small>
            </div>
          </div>

          <div class="col-md-12 mt-3 pe-0 text-end">
            <button (click)="addEntry()" [disabled]="!piForm.form.valid"
                    class="btn btn-success" type="button"> Add Record
            </button>
            <button (click)="piForm.reset()" class="btn btn-warning ms-1" type="button">Clear
            </button>
          </div>
        </form>
      </div>
      <div class="col-md-6 border-left mt-2">
        <div class="row g-3 ms-1 table-height">
          <table class="table table-striped">
            <thead>
            <tr>
              <th scope="col">Name</th>
              <th scope="col">Cost</th>
              <th scope="col">Quantity</th>
              <th scope="col">Sub Total</th>
            </tr>
            </thead>
            <tbody>
            <tr (click)="selectEntry(entry,i)"
                *ngFor="let entry of piRecords,let i=index"
                [class.active]="i === selectedRow">
              <td>{{entry.itemName}}</td>
              <td>{{entry.itemCost}}</td>
              <td>{{entry.quantity}}</td>
              <td>{{entry.quantity * entry.itemCost}}</td>
            </tr>
            </tbody>
          </table>
        </div>
        <div class="row g-3">
          <div class="col-md-6">
            <label>Total Amount</label>
            <input [ngModel]="totalBuyingPrice" class="form-control" readonly type="number">
          </div>
          <div class="col-md-6">
            <label>Discount</label>
            <input (keyup)="calculateTotal()" [(ngModel)]="purchaseInvoice.discount" class="form-control"
                   type="number">
          </div>
          <div class="col-md-6">
            <label>Amount to be Paid</label>
            <input [ngModel]="amountToBePaid" class="form-control" readonly type="number">
          </div>
          <div class="col-md-6">
            <label>Paid Amount</label>
            <input #paidAmount="ngModel" [(ngModel)]="purchaseInvoice.payment"
                   [class.is-invalid]="paidAmount.invalid && paidAmount.touched"
                   class="form-control" (ngModelChange)="calculateBalance()"
                   id="paidAmount" name="paidAmount" required type="number">
            <small [class.d-none]="paidAmount.valid || paidAmount.untouched" class="text-danger">*Paid Amount is
              Required
            </small>
          </div>
          <div class="col-md-6 form-group">
            <label>Payment Method</label>
            <select class="form-control" required #selectedPm="ngModel"
                    [class.is-invalid]="selectedPm.invalid && selectedPm.touched" name="selectedPm"
                    [(ngModel)]="selectedPaymentMethod">
              <option [ngValue]="">Select Payment Method</option>
              <option *ngFor="let pm of paymentMethods" [ngValue]="pm">{{pm.name}}</option>
            </select>
          </div>
          <div class="col-md-12 mt-3 text-end">
            <button (click)="removeStockRecord()" class="btn btn-danger"
                    type="button">Remove
            </button>
            <button (click)="save(piForm)" class="btn btn-success ms-2" type="button">Save
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

