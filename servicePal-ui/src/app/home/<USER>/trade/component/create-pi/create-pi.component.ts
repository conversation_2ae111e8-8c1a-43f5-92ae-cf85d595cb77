import {Component, OnInit, TemplateRef} from '@angular/core';
import {Item} from "../../../inventory/model/item";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {PurchaseInvoice} from "../../model/purchase-invoice";
import {PurchaseInvoiceRecord} from "../../model/purchase-invoice-record";
import {ItemService} from "../../../inventory/service/item.service";
import {NotificationService} from "../../../../core/service/notification.service";
import {NgForm} from "@angular/forms";
import {CreateItemComponent} from "../../../inventory/components/Item/create-item/create-item.component";
import {SupplierComponent} from "../supplier/supplier.component";
import {PurchaseInvoiceService} from "../../service/purchase-invoice.service";
import {SupplierService} from "../../service/supplier.service";
import {Supplier} from "../../model/supplier";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {MetaData} from "../../../../core/model/metaData";
import {PiPaymentMethodComponent} from "../pi-payment-method/pi-payment-method.component";
import {Subscription} from "rxjs";
import {Warehouse} from "../../../inventory/model/warehouse";
import {WarehouseService} from "../../../inventory/service/warehouse.service";

@Component({
  standalone: false,
  selector: 'app-purchase-invoice',
  templateUrl: './create-pi.component.html',
  styleUrls: ['./create-pi.component.css']
})

export class CreatePiComponent implements OnInit {

  modalRef: BsModalRef;
  purchaseInvoice = new PurchaseInvoice();

  piRecords: Array<PurchaseInvoiceRecord> = [];
  piRecord = new PurchaseInvoiceRecord();

  keySupplierSearch: string;
  supplierSearchList: Array<Supplier> = [];

  keyBarcodeSearch: string;
  keyItemNameSearch: string;
  itemSearchList: Array<Item> = [];

  totalBuyingPrice: number;
  percentageValue: number;
  amountToBePaid: number;

  selectedWarehouse: Warehouse;
  warehouses: Array<Warehouse> = [];

  selectedRow: number;
  isPercentage: boolean;
  discountMethodTypes: Array<MetaData>;

  paymentMethods: Array<MetaData>;
  selectedPaymentMethod: MetaData;

  constructor(private modalService: BsModalService,
              private itemService: ItemService,
              private notificationService: NotificationService,
              private piService: PurchaseInvoiceService,
              private supplierService: SupplierService,
              private metaDataService: MetaDataService,
              private warehouseService: WarehouseService) {
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template);
  }

  ngOnInit() {
    this.purchaseInvoice = new PurchaseInvoice();
    this.purchaseInvoice.supplier = new Supplier();
    this.purchaseInvoice.purchaseInvoiceRecords = [];
    this.totalBuyingPrice = 0;
    this.amountToBePaid = 0;
    this.purchaseInvoice.date = new Date();
    this.selectedWarehouse = new Warehouse();
    this.loadDiscountTypes()
    this.loadPaymentMethods();
    this.loadWarehouses();
  }

  loadPaymentMethods() {
    this.metaDataService.findByCategory("PIPaymentMethod").subscribe((data: Array<MetaData>) => {
      this.paymentMethods = data;
    })
  }

  loadWarehouses() {
    this.warehouseService.findAllActive().subscribe((result: Array<Warehouse>) => {
      if (result.length === 1) {
        this.selectedWarehouse = result[0];
      }
      return this.warehouses = result;
    })
  }

  loadDiscountTypes() {
    this.metaDataService.findByCategory("DiscountType").subscribe((data: Array<MetaData>) => {
      this.discountMethodTypes = data;
    })
  }

  searchSuppliers() {
    this.supplierService.findByNameLike(this.keySupplierSearch).subscribe((result: Array<Supplier>) => {
      return this.supplierSearchList = result;
    })
  }

  setSelectedSupplier(event) {
    this.purchaseInvoice.supplier.id = event.item.id;
  }

  loadItemByCode() {
    this.itemService.findAllByBarcodeLike(this.keyBarcodeSearch).subscribe((data: Array<Item>) => {
      this.itemSearchList = data;
    });
  }

  searchItems() {
    this.itemService.findAllByNameLike(this.keyItemNameSearch).subscribe((data: Array<Item>) => {
      this.itemSearchList = data;
    });
  }

  setSellingPrice() {
    this.piRecord.sellingPrice = this.piRecord.itemCost + (this.piRecord.itemCost * this.percentageValue / 100);
  }

  setPercentage() {
    if (this.piRecord.itemCost != null) {
      this.percentageValue = ((this.piRecord.sellingPrice - this.piRecord.itemCost) * 100) / this.piRecord.itemCost;
    } else {
      this.notificationService.showError('Enter Unit Price first');
    }
  }

  save(form: NgForm) {
    if (this.selectedPaymentMethod) {
      this.purchaseInvoice.paymentMethod = this.selectedPaymentMethod;
      this.purchaseInvoice.purchaseInvoiceRecords = this.piRecords;
      this.purchaseInvoice.totalAmount = this.amountToBePaid;
      this.piService.save(this.purchaseInvoice).subscribe((data: any) => {
        if (data.code === 200) {
          this.notificationService.showSuccess(data.message);
          form.resetForm();
          this.clearTable();
          this.ngOnInit();
          this.selectedPaymentMethod = undefined;
        } else {
          this.notificationService.showError(data.data);
          this.selectedPaymentMethod = undefined;
        }
      });
    }
    this.selectedPaymentMethod = undefined;
  }

  addEntry() {
    let duplicateFound = false;
    let index;
    this.piRecord.warehouseCode = this.selectedWarehouse.code;
    if (this.piRecords.length > 0) {
      for (let idx in this.piRecords) {
        if (this.piRecords[idx].item.id === this.piRecord.item.id) {
          duplicateFound = true;
          index = idx;
        }
      }
      if (duplicateFound) {
        this.piRecords[index].quantity = this.piRecords[index].quantity + this.piRecord.quantity;
      } else {
        this.piRecords.push(this.piRecord);
      }
    } else {
      this.piRecords.push(this.piRecord);
    }
    this.calculateTotal();
    this.clearPiRecord()
  }

  calculateTotal() {
    this.totalBuyingPrice = 0;
    for (let idx in this.piRecords) {
      this.totalBuyingPrice = this.totalBuyingPrice + (this.piRecords[idx].itemCost * this.piRecords[idx].quantity);
    }
    if (null == this.purchaseInvoice.discount || 0 == this.purchaseInvoice.discount) {
      this.purchaseInvoice.discount = 0;
    }
    if (true === this.isPercentage) {
      this.amountToBePaid = this.totalBuyingPrice - ((this.totalBuyingPrice * this.purchaseInvoice.discount) / 100);
    } else {
      this.amountToBePaid = this.totalBuyingPrice - this.purchaseInvoice.discount;
    }
  }

  calculateBalance() {
    this.purchaseInvoice.balance = 0;
    this.purchaseInvoice.balance = this.amountToBePaid - this.purchaseInvoice.payment;
  }

  clearPiRecord() {
    this.piRecord = new PurchaseInvoiceRecord();
    this.keyBarcodeSearch = "";
    this.keyItemNameSearch = "";
    this.percentageValue = 0;
  }

  removeStockRecord() {
    if (this.selectedRow != null) {
      this.piRecords.splice(this.selectedRow, 1);
      this.calculateTotal();
    } else {
      this.notificationService.showError('select inventory record first');
    }
  }

  clearTable() {
    this.piRecords.length = 0;
  }

  setSelectedItem(event) {
    this.piRecord.item = new Item();
    this.piRecord.item.id = event.item.id;
    this.piRecord.barcode = event.item.barcode;
    this.piRecord.itemCode = event.item.itemCode;
    this.piRecord.itemName = event.item.itemName;
    this.piRecord.itemCost = event.item.itemCost;
    this.piRecord.sellingPrice = event.item.sellingPrice;
    this.keyBarcodeSearch = event.item.barcode;
    this.keyItemNameSearch = event.item.itemName;
  }

  selectEntry(entry, index) {
    this.selectedRow = index;
    this.piRecord = entry;
    this.keyBarcodeSearch = entry.barcode;
    this.keyItemNameSearch = entry.itemName;
  }

  showItemModal() {
    this.modalService.show(CreateItemComponent, <ModalOptions>{class: 'modal-xl'});
  }

  showSupplierModal() {
    this.modalService.show(SupplierComponent, <ModalOptions>{class: 'modal-xl'});
  }

  setDiscountType(value) {
    if (value === 'Percentage') {
      this.isPercentage = true;
    } else {
      this.isPercentage = false;
    }
  }

}
