<div class="card">
  <div class="card-header">
    <strong>ADD EXPENSE</strong>
  </div>
  <div class="card-body">
    <form #expenseForm="ngForm">
      <div class="row">
        <div class="mb-3 col-md-6">
          <label class="form-label">Date</label>
          <input required #expenseDate="ngModel" type="text" name="piDate" id="piDate"
                 [(ngModel)]="expense.date" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
                 [class.is-invalid]="expenseDate.invalid && expenseDate.touched"
                 class="form-control" placeholder="Enter Invoice Date" autocomplete="false">
          <small class="text-danger" [class.d-none]="expenseDate.valid || expenseDate.untouched">*Expense Date is
            required
          </small>
        </div>

        <div class="mb-3 col-md-6">
          <label>Expense Type</label>
          <div class="input-group">
            <input [(ngModel)]="keyExpenseType"
                   [typeahead]="expenseTypeList"
                   (typeaheadLoading)="loadExpenseTypes()"
                   (typeaheadOnSelect)="setSelectedExpType($event)"
                   [typeaheadOptionsLimit]="7"

                   typeaheadOptionField="name"
                   placeholder="Search Expense Types"
                   autocomplete="off"
                   size="16"
                   class="form-control" name="searchExpenseType">
          </div>
        </div>

        <div class="col-md-6 form-group">
          <label>Amount</label>
          <input type="number" required #amount="ngModel" placeholder="Amount"
                 [class.is-invalid]="amount.invalid && amount.touched"
                 class="form-control" name="amount" id="amount" [(ngModel)]="expense.amount">
          <small class="text-danger" [class.d-none]="amount.valid || amount.untouched">*Expense Amount Required
          </small>
        </div>

        <div class="col-md-6 form-group">
          <label>Responsible Person</label>
          <input [(ngModel)]="keyEmpSearch"
                 [typeahead]="empSearchList"
                 (typeaheadLoading)="searchEmployee()"
                 (typeaheadOnSelect)="setSelectedEmp($event)"
                 [typeaheadOptionsLimit]="7"

                 typeaheadOptionField="name"
                 placeholder="Enter Responsible Name"
                 autocomplete="off"
                 size="16"
                 class="form-control" name="searchEmp">
        </div>

        <div class="col-md-3 form-group">
          <label>Reference</label>
          <input type="text" #reference="ngModel"
                 [class.is-invalid]="reference.invalid && reference.touched"
                 class="form-control" name="reference" id="reference" [(ngModel)]="expense.reference">
        </div>

        <div class="col-md-9 form-group">
          <label>Remark</label>
          <input type="text" required #remark="ngModel" class="form-control" name="percentage" id="percentage"
                 [(ngModel)]="expense.remark">
        </div>

        <div class="col-md-12 mt-3 pe-0">
          <button type="button" (click)="save(expenseForm)"
                  [disabled]="!expenseForm.form.valid || loadDataMode" class="btn btn-success float-end ms-2"> Save
          </button>
          <button type="button" (click)="expenseForm.reset()" [disabled]="loadDataMode" class="btn btn-warning float-end">Clear
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
