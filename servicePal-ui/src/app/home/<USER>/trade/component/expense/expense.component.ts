import {Component, OnInit} from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

import {Expense} from "../../model/expense";
import {ExpenseType} from "../../model/expense-type";
import {ExpenseService} from "../../service/expense.service";
import {ExpenseTypeService} from "../../service/expense-type.service";
import {Employee} from "../../../hr/model/employee";
import {EmployeeService} from "../../../hr/service/employee.service";
import {NotificationService} from "../../../../core/service/notification.service";
import {NgForm} from "@angular/forms";

@Component({
  standalone: false,
  selector: 'app-expense',
  templateUrl: './expense.component.html',
  styleUrls: ['./expense.component.css']
})
export class ExpenseComponent implements OnInit {

  expense: Expense;

  expenseTypeList: Array<ExpenseType>;
  keyExpenseType: string;

  keyEmpSearch: string;
  empSearchList: Array<Employee>;

  loadDataMode: boolean;

  constructor(
    private expenseService: ExpenseService, private expenseTypeService: ExpenseTypeService,
              private employeeService: EmployeeService, private notificationService: NotificationService,
    public ref: DynamicDialogRef,
    public config: DynamicDialogConfig
  ) {
  }

  ngOnInit(): void {
    this.expense = new Expense();
    this.expenseTypeList = [];
    this.empSearchList = [];
    this.expense.type = new ExpenseType();
  }

  loadExpenseTypes(event?: any) {
    
    const query = event ? event.query : this.keyExpenseType;
    this.expenseTypeService.findByName(query).subscribe((data: Array<ExpenseType>) => {
      return this.expenseTypeList = data;
    });
  }

  setSelectedExpType(event) {
    this.expense.type.id = event.item.id;
  }

  searchEmployee(event?: any) {
    
    const query = event ? event.query : this.keyEmpSearch;
    this.employeeService.findByEmployeeNameLike(query).subscribe((result: Array<Employee>) => {
      return this.empSearchList = result;
    })
  }

  setSelectedEmp(event) {
    this.expense.responsiblePerson = new Employee();
    this.expense.responsiblePerson.id = event.item.id;
  }

  save(form: NgForm) {
    this.expenseService.save(this.expense).subscribe((result: any) => {
      if (result == true) {
        this.notificationService.showSuccess("Expense saved");
        form.resetForm();
        this.ngOnInit();
      } else {
        this.notificationService.showError("Expense saving failed");
        console.log(result.data);
      }
    })
  }

  loadExpense(id: string) {
    this.expenseService.findById(id).subscribe((result: Expense) => {
      this.expense = result;
      this.keyExpenseType = this.expense.type.name;
      this.keyEmpSearch = this.expense.responsiblePerson.name;
      this.loadDataMode = true;
    })
  }

}
