import {Component, OnInit} from '@angular/core';
import {Supplier} from '../../model/supplier';
import {NotificationService} from '../../../../core/service/notification.service';
import {SupplierService} from "../../service/supplier.service";

@Component({
  standalone: false,
  selector: 'app-supplier',
  templateUrl: './supplier.component.html',
  styleUrls: ['./supplier.component.css']
})
export class SupplierComponent implements OnInit {
  firstRecordIndex: number = 0;
  itemsPerPage: number = 10;

  supplier = new Supplier();
  suppliers: Array<Supplier>;
  setClickedRow: Function;
  selectedRow: number;
  keySupplier: string;
  collectionSize;
  page;
  pageSize;

  selectedSupplier = new Supplier();

  constructor(private supplierService: SupplierService,
              private notificationService: NotificationService) {
  }

  ngOnInit() {
    this.supplier = new Supplier();
    this.supplier.active = true;
    this.page = 1;
    this.pageSize = 8;
    this.suppliers = [];
    this.findAll();
  }

  saveSupplier() {
    this.supplierService.save(this.supplier).subscribe(result => {
      this.notificationService.showSuccess(result);
      this.ngOnInit();
    }, error => {
      console.log(error);
    });
  }

  loadSuppliers(event?: any) {
    
    const query = event ? event.query : this.keySupplier;
    this.supplierService.findByNameLike(query).subscribe((data: Array<Supplier>) => {
      this.suppliers = data;
    });
  }

  findAll() {
    this.supplierService.findAll(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.suppliers = data.content;
      this.collectionSize = data.totalPages * 8;
    });
  }

  pageChanged(event: any) {
    this.page = event.page + 1;
    this.findAll();
  }
  onPageChange(event: any) {
    const page = event.page + 1; // PrimeNG paginator is 0-indexed
    this.page = page;
    // Call your existing pagination method
    this.pageChanged({ page: page });
  }


  supplierDetail(supplier) {
    this.supplier = supplier;
  }

  setSelectedSupplier(event) {
    this.selectedSupplier = event.item;
    this.supplier = this.selectedSupplier;
    this.suppliers = [];
    this.suppliers.push(this.supplier);
  }

  Clear() {
    this.supplier = new Supplier();
    this.keySupplier = "";
  }

}
