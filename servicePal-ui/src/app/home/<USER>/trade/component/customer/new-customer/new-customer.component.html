<div class="card">
  <div class="card-header">
    <strong>New Customer</strong>
  </div>
  <div class="card-body">
    <form #newPersonForm="ngForm" (ngSubmit)="savePerson(newPersonForm);">
      <div class="row g-3">
        <div class="mb-3 col-md-6">
          <label>NIC</label>
          <input required #nic="ngModel" (keyup)="checkNic()" type="text" name="nic" id="nic"
                 [(ngModel)]="customer.nicBr"
                 [class.is-invalid]="nic.invalid && nic.touched"
                 [typeahead]="customerSearchList"
                 (typeaheadLoading)="loadCustomerByNic()"
                 (typeaheadOnSelect)="setSelectedCustomer($event)"
                 [typeaheadOptionsLimit]="7"

                 typeaheadOptionField="nicBr"
                 autocomplete="off"
                 placeholder="Search By NIC"
                 pattern="([0-9]{9}[x|X|v|V]|[0-9]{12})$"
                 class="form-control" placeholder="Enter NIC No" [disabled]="isEdit">
          <small class="text-danger" [class.d-none]="nic.valid || nic.untouched">* NIC No is required
          </small>

          <small *ngIf="nicAvailability" [class.is-none]="true" class="text-danger">* NIC No
            already used
          </small>
        </div>
        <div class="mb-3 col-md-2">
          <label>Salutation </label>
          <select type="text" required #salutation="ngModel"
                  [class.is-invalid]="salutation.invalid && salutation.touched"
                  class="form-control" id="salutation" [(ngModel)]="customer.salutation" name="salutation">
            <option *ngFor="let sal of salutations" [value]="sal">{{sal}}</option>
          </select>
        </div>
        <div class="mb-3 col-md-4">
          <label>Name </label>
          <input type="text" required #CuName="ngModel" [class.is-invalid]="CuName.invalid && CuName.touched"
                 class="form-control" id="CuName" [(ngModel)]="customer.name" name="CuName"
                 placeholder="Enter Name " [disabled]="isEdit">
          <small class="text-danger" [class.d-none]="CuName.valid || CuName.untouched">* Name is required
          </small>
        </div>
        <div class="mb-3 col-md-6">
          <label>Address</label>
          <input type="text" required #address1="ngModel" class="form-control" id="address1"
                 name="address1" placeholder="Enter Address"
                 [class.is-invalid]="address1.invalid && address1.touched"
                 [(ngModel)]="customer.address" [disabled]="isEdit">
          <small class="text-danger" [class.d-none]="address1.valid || address1.untouched">*Address is
            required
          </small>
        </div>

        <div class="mb-3 col-md-6">
          <label>Email </label>
          <input type="text" #email="ngModel" class="form-control" id="email" placeholder="Enter Email"
                 name="email" [(ngModel)]="customer.email"
                 pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$"
                 [class.is-invalid]="email.invalid " [disabled]="isEdit">
          <div *ngIf="email.errors && (email.invalid && email.touched)">
            <small class="text-danger" [class.d-none]="email.valid ">*Email Address is
              required
            </small>
          </div>
        </div>

        <div class="mb-3 col-md-6">
          <label>Contact Number 1</label>
          <input required #telephone1="ngModel" [class.is-invalid]="telephone1.invalid && telephone1.touched"
                 type="text" class="form-control" id="telephone1" placeholder="Enter Contact Number 1"
                 name="telephone1"
                 pattern="^\d{10}$"
                 [(ngModel)]="customer.telephone1" [disabled]="isEdit">
          <small class="text-danger" [class.d-none]="telephone1.valid || telephone1.untouched">*Contact Number 1 is
            required
          </small>
        </div>

        <div class="mb-3 col-md-6">
          <label>Contact Number 2 </label>
          <input type="text" class="form-control" id="telephone2" placeholder="Enter Contact Number 2"
                 name="telephone2"
                 pattern="^\d{10}$"
                 [(ngModel)]="customer.telephone2" [disabled]="isEdit">
        </div>
        <div class="col-md-6">
          <div class="form-check checkbox col-md-6 mt-4">
            <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                   [checked]="customer.active" [disabled]="isEdit">
            <label class="form-check-label me-2" for="check3">Is Active</label>
          </div>
        </div>
      </div>
      <div class="row g-3 float-end">
        <div class="col-md-4">
          <button type="submit" class="btn btn-success me-4"
                  [disabled]="!newPersonForm.form.valid || isEdit || nicAvailability">
            save
          </button>
        </div>
        <div class="col-md-4">
          <button type="button" class="btn btn-warning" (click)="clear();"
                  [disabled]=" isEdit ">clear
          </button>
        </div>
      </div>
    </form>
  </div>
</div>

