import {Component, OnInit, TemplateRef} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';
import {ModalOptions} from 'ngx-bootstrap/modal';
import {Customer} from '../../../model/customer';
import {NotificationService} from '../../../../../core/service/notification.service';
import {MetaDataService} from '../../../../../core/service/metaData.service';
import {NewCustomerComponent} from '../new-customer/new-customer.component';
import {CustomerService} from "../../../service/customer.service";

@Component({
  standalone: false,
  selector: 'app-customer-detail',
  templateUrl: './manage-customer.component.html',
  styleUrls: ['./manage-customer.component.css']
})
export class ManageCustomerComponent implements OnInit {
  firstRecordIndex: number = 0;
  itemsPerPage: number = 10;


  modalRef: BsModalRef;
  customers: Array<Customer> = [];
  customerSearchList: Array<Customer> = [];

  selectedRow: number;
  setClickedRow: Function;

  keyName: string;
  keyNic: string;
  keyTp: string;

  page;
  collectionSize;
  pageSize;

  customer: Customer;

  active: boolean = false;
  disableSetCustomer: boolean = true;

  constructor(private notificationService: NotificationService, private modalService: BsModalService,
              private customerService: CustomerService, public metaDataService: MetaDataService) {
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 8;
    this.findAll();
    this.keyName = null;
  }

  pageChanged(event: any) {
    this.page = event.page + 1;
    this.findAll();
  }
  onPageChange(event: any) {
    const page = event.page + 1; // PrimeNG paginator is 0-indexed
    this.page = page;
    // Call your existing pagination method
    this.pageChanged({ page: page });
  }


  findAll() {
    this.customerService.findAllPagination(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.customers = data.content;
      this.collectionSize = data.totalPages * 10;
    });
  }

  setFilteredCustomer(event) {
    this.customer = event.item;
    this.customers = [];
    this.customers.push(this.customer);
  }

  customerDetail(selectedItem: any, index) {
    this.selectedRow = index;
    this.customer = selectedItem;
  }

  loadCustomer() {
    if (this.keyName !== '') {
      this.customerService.findByNameLike(this.keyName).subscribe((data: Array<Customer>) => {
        this.customerSearchList = data;
        this.keyNic = '';
        this.keyTp = '';
      });
    } else {
      this.ngOnInit();
    }
  }

  loadCustomerByNic() {
    if (this.keyNic !== '') {
      this.customerService.findByNicLike(this.keyNic).subscribe((data: Array<Customer>) => {
        this.customerSearchList = data;
        this.keyName = '';
        this.keyTp = '';
      });
    } else {
      this.ngOnInit();
    }
  }

  loadCustomerByTp() {
    if (this.keyTp !== '') {
      this.customerService.findByTpLike(this.keyTp).subscribe((data: Array<Customer>) => {
        this.customerSearchList = data;
        this.keyNic = '';
        this.keyName = '';
      });
    } else {
      this.ngOnInit();
    }
  }

  openModal(template: TemplateRef<any>, isEdit: boolean) {
    this.modalRef = this.modalService.show(NewCustomerComponent, <ModalOptions>{class: 'modal-lg'});
    this.modalRef.content.customer = this.customer;
    this.modalRef.content.isEdit = isEdit;

    this.modalRef.content.modalRef = this.modalRef;
    this.modalService.onHide.subscribe(result => {
      this.findAll();
    });
  }

  searchActiveResult(e) {
    if (e.target.checked) {
      this.active = true;
    } else {
      this.active = false;
      this.findAll();
    }
  }

  setSelectedCustomer() {
    this.modalRef.hide();
  }

}
