import { Component, OnInit } from '@angular/core';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import {Customer} from '../../../model/customer';
import {NotificationService} from '../../../../../core/service/notification.service';
import {MetaDataService} from '../../../../../core/service/metaData.service';
import {NewCustomerComponent} from '../new-customer/new-customer.component';
import {CustomerService} from "../../../service/customer.service";

@Component({
  standalone: false,
  selector: 'app-customer-detail',
  templateUrl: './manage-customer.component.html',
  styleUrls: ['./manage-customer.component.css']
})
export class ManageCustomerComponent implements OnInit {

  ref: DynamicDialogRef;
  customers: Array<Customer> = [];
  customerSearchList: Array<Customer> = [];

  selectedRow: number;
  setClickedRow: Function;

  keyName: string;
  keyNic: string;
  keyTp: string;

  page;
  collectionSize;
  pageSize;

  customer: Customer;

  active: boolean = false;
  disableSetCustomer: boolean = true;

  constructor(
    private notificationService: NotificationService,
    public metaDataService: MetaDataService,
    private customerService: CustomerService,
    public dialogService: DialogService
  ) {}

  ngOnInit() {
    this.page = 1;
    this.pageSize = 8;
    this.findAll();
    this.keyName = null;
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  findAll() {
    this.customerService.findAllPagination(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.customers = data.content;
      this.collectionSize = data.totalPages * 10;
    });
  }

  setFilteredCustomer(event) {
    this.customer = event.item;
    this.customers = [];
    this.customers.push(this.customer);
  }

  customerDetail(selectedItem: any, index) {
    this.selectedRow = index;
    this.customer = selectedItem;
  }

  loadCustomer() {
    if (this.keyName !== '') {
      this.customerService.findByNameLike(this.keyName).subscribe((data: Array<Customer>) => {
        this.customerSearchList = data;
        this.keyNic = '';
        this.keyTp = '';
      });
    } else {
      this.ngOnInit();
    }
  }

  loadCustomerByNic() {
    if (this.keyNic !== '') {
      this.customerService.findByNicLike(this.keyNic).subscribe((data: Array<Customer>) => {
        this.customerSearchList = data;
        this.keyName = '';
        this.keyTp = '';
      });
    } else {
      this.ngOnInit();
    }
  }

  loadCustomerByTp() {
    if (this.keyTp !== '') {
      this.customerService.findByTpLike(this.keyTp).subscribe((data: Array<Customer>) => {
        this.customerSearchList = data;
        this.keyNic = '';
        this.keyName = '';
      });
    } else {
      this.ngOnInit();
    }
  }

  openModal(isEdit: boolean) {
    this.ref = this.dialogService.open(NewCustomerComponent, {
      header: isEdit ? 'Edit Customer' : 'Create Customer',
      width: '70%',
      contentStyle: { "max-height": "500px", "overflow": "auto" },
      data: {
        customer: this.customer,
        isEdit: isEdit
      }
    });

    this.ref.onClose.subscribe(() => {
      this.findAll();
    });
  }

  searchActiveResult(e) {
    if (e.target.checked) {
      this.active = true;
    } else {
      this.active = false;
      this.findAll();
    }
  }

  setSelectedCustomer() {
    // Handle setting selected customer
    this.ref.close();
  }

}
