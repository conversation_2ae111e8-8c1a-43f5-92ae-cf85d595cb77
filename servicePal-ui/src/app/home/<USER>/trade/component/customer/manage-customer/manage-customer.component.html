<div class="card">
  <div class="card-header">
    <h5 class="card-title">MANAGE CUSTOMERS</h5>
  </div>
  <div class="card-body">
    <div class="row g-3 mb-4">
      <div class="col-md-4">
        <span class="p-float-label">
          <input id="name" type="text" pInputText placeholder="Search Customer Name" 
                 [(ngModel)]="keyName" (keyup)="loadCustomer()" />
          <label for="name">Customer Name</label>
        </span>
      </div>

      <div class="col-md-3">
        <span class="p-float-label">
          <input id="nic" type="text" pInputText placeholder="NIC/BR No." 
                 [(ngModel)]="keyNic" (keyup)="loadCustomerByNic()" />
          <label for="nic">NIC/BR No.</label>
        </span>
      </div>

      <div class="col-md-3">
        <span class="p-float-label">
          <input id="tp" type="text" pInputText placeholder="TP No." 
                 [(ngModel)]="keyTp" (keyup)="loadCustomerByTp()" />
          <label for="tp">TP No.</label>
        </span>
      </div>

      <div class="col-md-2 align-self-end">
        <button pButton type="button" icon="pi pi-plus" label="New" 
                class="w-100" (click)="openModal(false)"></button>
      </div>
    </div>

    <div class="row g-3 mt-2">
      <div class="table-height">
        <p-table [value]="customers" responsiveLayout="scroll" 
                 [paginator]="true" [rows]="10" [rowsPerPageOptions]="[5,10,20]">
          <ng-template pTemplate="header">
            <tr>
              <th>Name</th>
              <th>Address</th>
              <th>NIC/BR</th>
              <th>TP</th>
              <th>Action</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-customer let-i="index">
            <tr (click)="customerDetail(customer, i)" [class.active]="i === selectedRow">
              <td>{{customer.name}}</td>
              <td>{{customer.address}}</td>
              <td>{{customer.nicBr}}</td>
              <td>{{customer.tp1}}, {{customer.tp2}}, {{customer.tp3}}</td>
              <td>
                <div class="d-flex justify-content-center">
                  <button pButton type="button" icon="pi pi-pencil" 
                          class="p-button-rounded p-button-primary me-2" 
                          (click)="openModal(true)"></button>
                  <button pButton type="button" icon="pi pi-check" 
                          class="p-button-rounded p-button-success" 
                          (click)="setSelectedCustomer()"></button>
                </div>
              </td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </div>

    <div class="row mt-3" *ngIf="selectedRow != null">
      <div class="col-md-6">
        <button pButton type="button" icon="pi pi-times" label="Clear" 
                class="w-100" (click)="clearAll()"></button>
      </div>
    </div>
  </div>
</div>