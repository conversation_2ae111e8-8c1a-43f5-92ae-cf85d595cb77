<div class="card">
  <div class="card-header">
    <strong>All Customer Details</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="mb-3 input-focus col-4">
        <input [(ngModel)]="keyName"
               [typeahead]="customerSearchList"
               (typeaheadLoading)="loadCustomer()"
               (typeaheadOnSelect)="setFilteredCustomer($event)"
               [typeaheadOptionsLimit]="7"

               typeaheadOptionField="name"
               autocomplete="off"
               placeholder="Search By Name"
               class="form-control" name="category">
      </div>
      <div class="mb-3 input-focus col-3">
        <input [(ngModel)]="keyNic"
               [typeahead]="customerSearchList"
               (typeaheadLoading)="loadCustomerByNic()"
               (typeaheadOnSelect)="setFilteredCustomer($event)"
               [typeaheadOptionsLimit]="7"

               typeaheadOptionField="nicBr"
               autocomplete="off"
               placeholder="Search By NIC"
               class="form-control" name="nic">
      </div>
      <div class="mb-3 input-focus col-3">
        <input [(ngModel)]="keyTp"
               [typeahead]="customerSearchList"
               (typeaheadLoading)="loadCustomerByTp()"
               (typeaheadOnSelect)="setFilteredCustomer($event)"
               [typeaheadOptionsLimit]="7"

               typeaheadOptionField="telephone1"
               autocomplete="off"
               placeholder="Search By TP"
               class="form-control" name="tp">
      </div>
      <div class="mb-3 col-md-2">
        <div class="form-check checkbox">
          <input class="form-check-input" id="check3" name="check3" type="checkbox"
                 (change)="searchActiveResult($event)"
                 [value]="active"
                 [(ngModel)]="active">
          <label class="form-check-label me-2" for="check3">Inactivates</label>
        </div>
      </div>
    </div>
    <table class="table table-bordered table-striped table-sm">
      <thead>
      <tr style="text-align: center">
        <th scope="col">Customer Name</th>
        <th scope="col">NIC</th>
        <th scope="col">Telephone1 / Telephone2</th>
        <th scope="col">Address</th>
      </tr>
      </thead>
      <tbody>
      <tr style="text-align: center" *ngFor="let customer of customers,let i = index"
          (click)="customerDetail(customer,i)"
          [class.active]="i === selectedRow">
        <td>{{customer.name}}</td>
        <td>{{customer.nicBr}}</td>
        <td>{{customer.telephone1 + (null != customer.telephone2 ? ',' + customer.telephone2 : '')}}</td>
        <td>{{customer.address}}</td>
      </tr>
      </tbody>
    </table>

    <div class="row float-end">
      <div class="me-3">
        <button class="btn btn-success " type="button" (click)="openModal(customerUpdateModal,true)"
                [disabled]="selectedRow===null">View
        </button>
      </div>
      <div class="me-3">
        <button class="btn btn-success " type="button" (click)="openModal(customerUpdateModal,false)"
                [disabled]="selectedRow===null">Edit
        </button>
      </div>
      <div class="me-3">
        <button class="btn btn-success " type="button" (click)="setSelectedCustomer()"
                [hidden]="disableSetCustomer">Set Selected
        </button>
      </div>
    </div>
    <pagination class="pagination-sm justify-content-center"
                [totalItems]="collectionSize"
                [(ngModel)]="page"
                [boundaryLinks]="true"
                [maxSize]="10"
                (pageChanged)="pageChanged($event)">
    </pagination>
  </div>
</div>

<ng-template #customerUpdateModal>

  <div class="modal-header">
    <button type="button" class="close float-end" aria-label="Close" (click)="modalRef.hide();ngOnInit();">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <app-new-person></app-new-person>
  </div>

</ng-template>


