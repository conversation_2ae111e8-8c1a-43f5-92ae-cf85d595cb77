import {Component, OnInit, ViewChild} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {NgForm} from '@angular/forms';
import {NotificationService} from '../../../../../core/service/notification.service';
import {Customer} from '../../../model/customer';
import {MetaDataService} from '../../../../../core/service/metaData.service';
import {ShowRoom} from "../../../../business/model/show-room";
import {CustomerService} from "../../../service/customer.service";

@Component({
  standalone: false,
  selector: 'app-new-person',
  templateUrl: './new-customer.component.html',
  styleUrls: ['./new-customer.component.css']
})

export class NewCustomerComponent implements OnInit {
  @ViewChild('photo', {static: true}) photo;

  customer: Customer;
  customerSearchList: Array<Customer> = [];
  nicAvailability = false;
  modalRef: BsModalRef;
  isEdit: boolean;
  isModal = false;
  type: string;
  salutations: Array<string>;

  constructor(public customerService: CustomerService,
              public notificationService: NotificationService,
              public metaDataService: MetaDataService) {
  }

  ngOnInit() {
    this.customer = new Customer();
    this.customer.active = true;
    this.salutations = ['Mr.', 'Mrs.', 'Ms.', 'BM.', 'AD.', 'CC.'];
  }

  gotoTop() {
    window.scroll({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });
  }

  checkNic() {
    this.customerService.checkNic(this.customer.nicBr).subscribe((res: boolean) => {
      this.nicAvailability = res;
    });
  }

  savePerson(form: NgForm) {
    this.customer.balance = 0;
    this.customerService.save(this.customer).subscribe((result: any) => {
      if (result === true) {
        this.notificationService.showSuccess('Customer Saved Successfully');
        if (this.isModal) {
          this.modalRef.hide();
        }
        form.reset();
        this.gotoTop();
        this.ngOnInit();
      } else if (result === null) {
        this.notificationService.showError('Customer Saving Failed');
        this.gotoTop();
        form.reset();
        this.ngOnInit();
        this.customer.active = true;
      }
    })
  }

  clear() {
    this.customer = new Customer();
  }

  setSelectedCustomer(event) {
    this.customer = event.item;
  }

  loadCustomerByNic() {
    if (this.customer.nicBr !== '') {
      this.customerService.findByNicLike(this.customer.nicBr).subscribe((data: Array<Customer>) => {
        this.customerSearchList = data;
      });
    } else {
      this.ngOnInit();
    }
  }
}

