<div class="card font-helvetica">
  <div class="card-header">
    <strong class="theme-color">View Purchase Invoice</strong>
  </div>
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-3 pe-0">
        <label class="theme-color">Invoice No</label>
        <label class="theme-color text-end">:</label>
      </div>
      <div class="col-md-3 ps-0">
        <p class="p-1 theme-color-border-bottom">
          {{purchaseInvoice.invoiceNo ? purchaseInvoice.invoiceNo : 'N/A'}}
        </p>
      </div>
      <div class="col-md-3 pe-0">
        <label class="theme-color">Date</label>
        <label class="theme-color text-end">:</label>
      </div>
      <div class="col-md-3 ps-0">
        <p class="p-1 theme-color-border-bottom">
<!--          {{ purchaseInvoice.date ? (purchaseInvoice.date | date : 'short' : '+530') : 'N/A' }}-->
        </p>
      </div>
      <div class="col-md-3 pe-0">
        <label class="theme-color">Customer Name</label>
        <label class="theme-color text-end">:</label>
      </div>
      <div class="col-md-3 ps-0">
        <p class="p-1 theme-color-border-bottom">
          {{purchaseInvoice.supplier ? purchaseInvoice.supplier.name : 'N/A'}}
        </p>
      </div>
      <div class="col-md-3 pe-0">
        <label class="theme-color">Due Date</label>
        <label class="theme-color text-end">:</label>
      </div>
      <div class="col-md-3 ps-0">
        <p class="p-1 theme-color-border-bottom">
<!--          {{ purchaseInvoice.dueDate ? (purchaseInvoice.dueDate | date : 'short' : '+530') : 'N/A' }}-->
        </p>
      </div>
      <div class="col-md-3 pe-0">
        <label class="theme-color">Payment Method</label>
        <label class="theme-color text-end">:</label>
      </div>
      <div class="col-md-3 ps-0">
        <p class="p-1 theme-color-border-bottom">
          {{purchaseInvoice.paymentMethod.value ? purchaseInvoice.paymentMethod.value : 'N/A'}}
        </p>
      </div>
      <div class="col-md-3 pe-0">
        <label class="theme-color">Status</label>
        <label class="theme-color text-end">:</label>
      </div>
      <div class="col-md-3 ps-0">
        <p class="p-1 theme-color-border-bottom">
          {{purchaseInvoice.status.value ? purchaseInvoice.status.value : 'N/A'}}
        </p>
      </div>
      <div class="col-md-3 pe-0">
        <label class="theme-color">Total Amount</label>
        <label class="theme-color text-end">:</label>
      </div>
      <div class="col-md-3 ps-0">
        <p class="p-1 theme-color-border-bottom">
          {{purchaseInvoice.totalAmount ? purchaseInvoice.totalAmount : 'N/A'}}
        </p>
      </div>
      <div class="col-md-3 pe-0" *ngIf="cashPayment">
        <label class="theme-color">Cash Amount</label>
        <label class="theme-color text-end">:</label>
      </div>
    <!--  <div class="col-md-3 pe-0">
        <label class="theme-color">Sub Total</label>
        <label class="theme-color float-end">:</label>
      </div>
      <div class="col-md-3 ps-0">
        <p class="p-1 theme-color-border-bottom">
          {{purchaseInvoice.subTotal ? purchaseInvoice.subTotal : 'N/A'}}
        </p>
      </div>-->
      <div class="col-md-3 pe-0">
        <label class="theme-color">Balance</label>
        <label class="theme-color text-end">:</label>
      </div>
      <div class="col-md-3 ps-0">
        <p class="p-1 theme-color-border-bottom">
          {{purchaseInvoice.balance ? purchaseInvoice.balance : 'N/A'}}
        </p>
      </div>
    </div>
    <div class="row g-3 mt-2">
      <div class="col-md-3 pe-0">
        <label class="theme-color">Sales Invoice Records</label>
        <label class="theme-color text-end">:</label>
      </div>
    </div>
    <div class="row g-3">
      <div class="col-md-12">
        <table class="w-100">
          <thead align="center">
          <th class="p-2 theme-color">Barcode</th>
          <th class="p-2 theme-color">Item Name</th>
          <th class="p-2 theme-color">Quantity</th>
          <th class="p-2 theme-color">Unit Price</th>
          <th class="p-2 theme-color">item Cost</th>
          <th class="p-2 theme-color">Price</th>
          </thead>
          <tbody align="center">
          <tr *ngFor="let invoiceRecord of purchaseInvoice.purchaseInvoiceRecords">
            <td class="border-0 pt-1 theme-color">{{invoiceRecord.item.barcode}}</td>
            <td class="border-0 pt-1 theme-color">{{invoiceRecord.itemName}}</td>
            <td class="border-0 pt-1 theme-color">{{invoiceRecord.quantity}}</td>
            <td class="border-0 pt-1 theme-color">{{invoiceRecord.sellingPrice | number : '1.2-2'}}</td>
            <td class="border-0 pt-1 theme-color">{{invoiceRecord.itemCost | number : '1.2-2'}}</td>
            <td class="border-0 pt-1 theme-color">{{invoiceRecord.totalAmount | number : '1.2-2'}}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="row g-3 mt-4 text-end">
      <div class="col-md-12">
        <button class="btn theme-color-bg text-white" (click)="close()">Close</button>
      </div>
    </div>
  </div>
</div>
