import {Component, ElementRef, Inject, OnInit, ViewChild} from '@angular/core';
import {Job} from '../../../business/model/job';
import {Item} from '../../../inventory/model/item';
import {ItemService} from '../../../inventory/service/item.service';
import {NotificationService} from '../../../../core/service/notification.service';
import {SalesInvoiceService} from '../../service/sales-invoice.service';
import {SalesInvoice} from '../../model/sales-invoice';
import {SalesInvoiceRecord} from '../../model/sales-invoice-record';
import {Customer} from '../../model/customer';
import {JobService} from '../../../business/service/job.service';
import {Machine} from '../../../business/model/machine';
import {StockService} from '../../../inventory/service/stock.service';
import {BsModalRef, BsModalService, ModalOptions} from 'ngx-bootstrap/modal';
import {InvoiceComponent} from "../invoice/invoice.component";
import {CustomerService} from "../../service/customer.service";
import {MetaData} from "../../../../core/model/metaData";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {PaymentMethodComponent} from "../payment-method/payment-method.component";
import {User} from "../../../../admin/model/user";
import {Stock} from "../../../inventory/model/stock";
import {ManageSiComponent} from "../manage-si/manage-si.component";
import {ViewStockComponent} from "../../../inventory/components/stock/view-stock/view-stock.component";
import {ViewCashierComponent} from "../cashier/view-cashier/view-cashier.component";
import {ManageCustomerComponent} from "../customer/manage-customer/manage-customer.component";
import {DOCUMENT} from "@angular/common";

@Component({
  standalone: false,
  selector: 'app-sales-invoice',
  templateUrl: './create-si.component.html',
  styleUrls: ['./create-si.component.css']
})
export class CreateSiComponent implements OnInit {

  elem;

  jobId: string;
  job: Job;
  directMode: boolean;
  si: SalesInvoice;
  salesInvRec: SalesInvoiceRecord;

  itemQty: number;
  sPrice: number;

  keyItemSearch: string;
  itemSearchList: Array<Item> = [];

  keyItemNameSearch: string;
  itemNameSearchList: Array<Item> = [];

  keyCustomerSearch: string;
  customerSearchList: Array<Customer> = [];

  selectedItem: Item = new Item();
  selectedSiRecordIndex: number;
  isProcessing: boolean;

  modalRef: BsModalRef;
  modalRefInvoice: BsModalRef;
  duplicateIndex: string;

  user: User;

  saleTypeList: Array<MetaData> = [];
  selectedSaleType: MetaData;
  negativeQty: boolean;

  discount: number;
  selectedDiscountMethod: string;
  isPercentage: boolean;

  paymentMethods: Array<MetaData> = [];
  paymentMethodId: string;
  cashId: string;

  customerModalRef: BsModalRef;

  @ViewChild('quantity') qty: ElementRef;
  @ViewChild('sellingPrice') sellingPrice: ElementRef;
  @ViewChild('barcode') barcode: ElementRef;
  @ViewChild('payment') payment: ElementRef;

  constructor(private itemService: ItemService, private salesInvoiceService: SalesInvoiceService,
              private notificationService: NotificationService, private jobService: JobService,
              private modalService: BsModalService, private stockService: StockService,
              private customerService: CustomerService, private metaDataService: MetaDataService,
              @Inject(DOCUMENT) private document: any) {
  }

  ngOnInit() {
    this.elem = document.documentElement;
    this.directMode = true;
    this.job = new Job();
    this.job.customer = new Customer();
    this.job.machine = new Machine();
    this.si = new SalesInvoice();
    this.si.salesInvoiceRecords = [];
    this.si.dueDate = new Date();
    this.si.totalDiscount = 0;
    this.si.paymentMethod = new MetaData();
    this.si.directMode = true;
    this.isProcessing = false;
    this.keyItemSearch = '';
    this.keyItemNameSearch = '';
    this.selectedDiscountMethod = 'Flat';
    this.discount = 0;

    this.findPaymentMethods();
    this.setDefaultPaymentMethod();
    this.loadSaleTypes();

    this.user = JSON.parse(localStorage.getItem('currentUser')).user;
  }

  searchItems() {
    this.itemService.findAllByBarcodeLike(this.keyItemSearch).subscribe((result: Array<Item>) => {
      return this.itemSearchList = result;
    })
  }

  searchItemsByName() {
    this.itemService.findAllByNameLike(this.keyItemNameSearch).subscribe((result: Array<Item>) => {
      return this.itemNameSearchList = result;
    })
  }

  searchCustomers() {
    this.customerService.findByNameLike(this.keyCustomerSearch).subscribe((result: Array<Customer>) => {
      return this.customerSearchList = result;
    })
  }

  calculateTotal() {
    this.si.subTotal = 0;
    this.si.totalAmount = 0;
    this.si.advancePayment = 0;
    for (let index = 0; this.si.salesInvoiceRecords.length > index; index++) {
      this.si.subTotal = this.si.subTotal + this.si.salesInvoiceRecords[index].price;
    }
    if (this.isPercentage) {
      this.si.totalDiscount = this.si.subTotal * this.discount / 100;
    } else {
      this.si.totalDiscount = this.discount;
    }
    this.si.totalAmount = this.si.subTotal - this.si.totalDiscount - this.si.advancePayment;
  }

  addToInvoice() {
    this.salesInvRec = new SalesInvoiceRecord();
    this.salesInvRec.item = new Item();
    let dup = this.checkForDuplicate();
    if (dup) {
      let response = confirm('Item already in the invoice. continue?');
      if (response) {
        this.addDuplicate();
      }
    } else {
      this.salesInvRec.barcode = this.selectedItem.barcode;
      this.salesInvRec.itemCode = this.selectedItem.itemCode;
      if (this.selectedItem.itemCode == "0") {
        this.salesInvRec.itemName = this.keyItemNameSearch;
      } else {
        this.salesInvRec.itemName = this.selectedItem.itemName;
      }
      this.salesInvRec.item.id = this.selectedItem.id;

      if (this.negativeQty === true) {
        this.itemQty = this.itemQty * -1;
        this.salesInvRec.recodeType = this.selectedSaleType;
      }

      this.salesInvRec.quantity = this.itemQty;
      this.salesInvRec.unitPrice = this.sPrice;
      this.salesInvRec.itemCost = this.selectedItem.itemCost;
      this.salesInvRec.price = this.itemQty * this.sPrice;
      this.si.salesInvoiceRecords.push(this.salesInvRec);
    }

    this.calculateTotal();
    this.clearAddToInvoice();
  }

  clearAddToInvoice() {
    this.keyItemSearch = '';
    this.keyItemNameSearch = '';
    this.sPrice = 0;
    this.itemQty = 0;
    this.selectedItem = new Item();
    this.barcode.nativeElement.focus();
  }

  checkAvailability() {
    this.stockService.findOneByBarcodeAndWarehouse(this.selectedItem.barcode, this.user.warehouseCode).subscribe((result: Stock) => {
      if (!result) {
        this.notificationService.showError('Not Stock Available');
      } else {
        if (this.selectedItem.manageStock && result.quantity < this.itemQty) {
          this.notificationService.showError('Not Enough Stock Available');
        } else {
          if (this.sPrice < this.selectedItem.sellingPrice) {
            this.notificationService.showWarning('Invalid Price');
            return;
          }
          this.addToInvoice();
        }
      }
      this.clearAddToInvoice();
    });
  }

  checkForDuplicate() {
    /*for (const index in this.si.salesInvoiceRecords) {
      if (this.si.salesInvoiceRecords[index].barcode === this.selectedItem.barcode) {
        this.duplicateIndex = index;
        return true;
      }
    }*/
    return false;
  }

  addDuplicate() {
    if (this.negativeQty === true) {
      this.itemQty = this.itemQty * -1;
    }
    this.si.salesInvoiceRecords[this.duplicateIndex].quantity = this.si.salesInvoiceRecords[this.duplicateIndex].quantity
      + this.itemQty;
    this.si.salesInvoiceRecords[this.duplicateIndex].price = this.si.salesInvoiceRecords[this.duplicateIndex].quantity *
      this.selectedItem.sellingPrice;
    this.calculateTotal();
  }

  initProcess() {
    this.jobService.findById(this.jobId).subscribe((result: Job) => {
      this.job = result;
      this.processEstimate();
    })
  }

  processEstimate() {
    this.si.directMode = false;
    for (let index in this.job.jobEstimate.jobEstimateRecordList) {
      this.salesInvRec = new SalesInvoiceRecord();
      this.salesInvRec.item = new Item();
      this.salesInvRec.barcode = this.job.jobEstimate.jobEstimateRecordList[index].barcode;
      this.salesInvRec.itemName = this.job.jobEstimate.jobEstimateRecordList[index].itemName;
      this.salesInvRec.itemCode = this.job.jobEstimate.jobEstimateRecordList[index].itemCode;
      this.salesInvRec.quantity = this.job.jobEstimate.jobEstimateRecordList[index].quantity;
      this.salesInvRec.unitPrice = this.job.jobEstimate.jobEstimateRecordList[index].unitPrice;
      this.salesInvRec.price = this.job.jobEstimate.jobEstimateRecordList[index].price;
      this.si.salesInvoiceRecords.push(this.salesInvRec);
    }

    for (let index in this.job.jobEstimate.services) {
      this.salesInvRec = new SalesInvoiceRecord();
      this.salesInvRec.item = new Item();
      this.salesInvRec.item.id = this.job.jobEstimate.services[index].id;
      this.salesInvRec.barcode = this.job.jobEstimate.services[index].barcode;
      this.salesInvRec.itemName = this.job.jobEstimate.services[index].itemName;
      this.salesInvRec.quantity = 1;
      this.salesInvRec.unitPrice = this.job.jobEstimate.services[index].sellingPrice;
      this.salesInvRec.price = this.job.jobEstimate.services[index].sellingPrice;
      this.si.salesInvoiceRecords.push(this.salesInvRec);
    }

    if (null != this.job.jobEstimate.transport) {
      this.salesInvRec = new SalesInvoiceRecord();
      this.salesInvRec.item = new Item();
      this.salesInvRec.item.id = this.job.jobEstimate.transport.id;
      this.salesInvRec.itemName = this.job.jobEstimate.transport.itemName;
      this.salesInvRec.barcode = this.job.jobEstimate.transport.barcode;
      this.salesInvRec.quantity = 1;
      this.salesInvRec.unitPrice = this.job.jobEstimate.transport.sellingPrice;
      this.salesInvRec.price = this.job.jobEstimate.transport.sellingPrice;
      this.si.salesInvoiceRecords.push(this.salesInvRec);
    }

    this.si.totalDiscount = this.job.jobEstimate.discount;
    this.si.advancePayment = this.job.advancePayment;
    this.si.totalAmount = this.job.jobEstimate.totalAmount - this.si.advancePayment;
    this.si.subTotal = this.job.jobEstimate.totalAmount + this.job.jobEstimate.discount;

    this.si.customer = this.job.customer;
    this.si.customerName = this.job.customer.name;

    this.si.jobNo = this.job.jobNo;
    this.si.services = this.job.jobEstimate.services;
    if (null != this.job.jobEstimate.transport) {
      this.si.transport = new Item();
      this.si.transport.id = this.job.jobEstimate.transport.id;
      this.si.transportCharge = this.job.jobEstimate.transportCharge;
      this.si.serviceCharge = this.job.jobEstimate.serviceCharge;
      this.si.partsCharge = this.job.jobEstimate.partsCharge;
    }
    parseFloat(this.si.totalAmount.toString()).toFixed(2);
  }

  calculateBalance() {
    this.si.cashBalance = this.si.payment - this.si.totalAmount;
    if (this.si.cashBalance < 0) {
      this.si.balance = this.si.cashBalance * -1;
    } else {
      this.si.balance = 0;
    }
    parseFloat(this.si.cashBalance.toString()).toFixed(2);
  }

  setSelectedItem(event) {
    this.selectedItem = event.item;
    this.sPrice = this.selectedItem.sellingPrice;
    if (this.keyItemNameSearch.length == 0) {
      this.keyItemNameSearch = this.selectedItem.itemName;
    }
    if (this.keyItemSearch.length == 0) {
      this.keyItemSearch = this.selectedItem.barcode;
    }
    this.qty.nativeElement.value = '';
    this.qty.nativeElement.focus();
  }

  setSelectedCustomer(event) {
    this.si.customer = new Customer();
    this.si.customer.id = event.item.id;
    this.si.customerName = event.item.name;
  }

  selectRow(index) {
    this.selectedSiRecordIndex = index;
  }

  removeRow() {
    this.si.salesInvoiceRecords.splice(this.selectedSiRecordIndex, 1);
    this.calculateTotal();
  }

  setPaymentMethod(event) {
    this.si.paymentMethod.id = event.target.value;
    if (this.si.paymentMethod != null && this.si.paymentMethod.id != this.cashId) {
      this.modalRef = this.modalService.show(PaymentMethodComponent, <ModalOptions>{
        class: 'modal-md',
        ignoreBackdropClick: true
      });
      this.modalRef.content.modalRef = this.modalRef;
      this.modalRef.content.payment = this.si.payment;
      this.modalService.onHide.subscribe(event => {
        this.si.cashlessAmount = this.modalRef.content.chequeCardAmount;
        this.si.cashAmount = this.modalRef.content.cashAmount;
        this.si.cardOrVoucherNo = this.modalRef.content.chequeCardNo;
      })
    }
  }

  save(print: boolean) {
    this.isProcessing = true;
    this.salesInvoiceService.save(this.si).subscribe((result: any) => {
      if (result.code === 200) {
        this.notificationService.showSuccess(result.message);
        if (print) {
          this.modalRefInvoice = this.modalService.show(InvoiceComponent, <ModalOptions>{class: 'modal-xl'});
          this.modalRefInvoice.content.invoiceNo = result.data;
          this.modalRefInvoice.content.findInvoice();
        }
        if (!this.si.directMode)
          this.modalRef.hide();
        this.ngOnInit();
        this.isProcessing = false;
      } else {
        this.notificationService.showError(result.message);
        console.log(result.data);
        this.isProcessing = false;
      }
    })
  }

  gotoPayment() {
    if (this.keyItemSearch.length == 0)
      this.payment.nativeElement.focus();
  }

  saveByEnter() {
    if (this.si.payment != null) {
      this.save(true);
    }
  }

  focusPrice() {
    this.sellingPrice.nativeElement.focus();
  }

  focusQty() {
    this.qty.nativeElement.focus();
  }

  loadSaleTypes() {
    this.metaDataService.findByCategory("SaleType").subscribe((data: Array<MetaData>) => {
      this.saleTypeList = data;
    })
  }

  setSaleType(id) {
    this.metaDataService.findById(id).subscribe((data: any) => {
      this.selectedSaleType = data;
      if (this.selectedSaleType.value === "Return") {
        this.negativeQty = true;
      } else {
        this.negativeQty = false;
      }
    });
  }

  setDiscountMethod() {
    if (this.selectedDiscountMethod === 'Percentage') {
      this.isPercentage = true;
    } else {
      this.isPercentage = false;
    }
  }

  findPaymentMethods() {
    this.metaDataService.findByCategory('PaymentMethod').subscribe((result: Array<MetaData>) => {
      for (let meta of result) {
        if (meta.value === 'Cash') {
          this.cashId = meta.id;
        }
      }
      return this.paymentMethods = result;
    })
  }

  setDefaultPaymentMethod() {
    this.metaDataService.findByValueAndCategory('Cash', 'PaymentMethod').subscribe((data: MetaData) => {
      this.si.paymentMethod.id = data.id;
      this.paymentMethodId = data.id;
    })
  }

  openCustomer() {
    this.customerModalRef = this.modalService.show(ManageCustomerComponent, <ModalOptions>{class: 'modal-xl'});
    this.customerModalRef.content.disableSetCustomer = false;
    this.customerModalRef.content.modalRef = this.customerModalRef;
    this.modalService.onHide.subscribe(event => {
      this.si.customer = new Customer();
      this.si.customer.id = this.customerModalRef.content.customer.id;
      this.si.customerName = this.customerModalRef.content.customer.name;
      this.keyCustomerSearch = this.customerModalRef.content.customer.name;
    })
  }

  openCashier() {
    this.modalService.show(ViewCashierComponent, <ModalOptions>{class: 'modal-lg'});
  }

  openPastInvoice() {
    this.modalService.show(ManageSiComponent, <ModalOptions>{class: 'modal-xl'});
  }

  openStock() {
    this.modalService.show(ViewStockComponent, <ModalOptions>{class: 'modal-xl'});
  }

  closeFullscreen() {
    if (document.exitFullscreen) {
      this.document.exitFullscreen();
    } else if (this.document.mozCancelFullScreen) {
      this.document.mozCancelFullScreen();
    } else if (this.document.webkitExitFullscreen) {
      this.document.webkitExitFullscreen();
    } else if (this.document.msExitFullscreen) {
      this.document.msExitFullscreen();
    }
  }

}
