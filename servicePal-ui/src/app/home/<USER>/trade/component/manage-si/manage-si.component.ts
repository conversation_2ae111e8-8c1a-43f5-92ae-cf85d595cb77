import {Component, OnInit} from '@angular/core';
import {BsModalRef, BsModalService, ModalOptions} from 'ngx-bootstrap/modal';
import {MetaData} from '../../../../core/model/metaData';
import {SalesInvoice} from '../../model/sales-invoice';
import {SalesInvoiceService} from '../../service/sales-invoice.service';
import {InvoiceComponent} from "../invoice/invoice.component";
import {PaySiBalanceComponent} from "../pay-si-balance/pay-si-balance.component";
import {ManageCustomerComponent} from "../customer/manage-customer/manage-customer.component";
import {NotificationService} from "../../../../core/service/notification.service";
import {Response} from "../../../../core/model/response";

@Component({
  standalone: false,
  selector: 'app-manage-si',
  templateUrl: './manage-si.component.html',
  styleUrls: ['./manage-si.component.css']
})
export class ManageSiComponent implements OnInit {

  sis: Array<SalesInvoice> = [];
  selectedSi: SalesInvoice;

  keyJobNo: string;
  keyInvoiceNo: string;
  keyCustomerNicBr: string;
  searchFilter: string;
  invSaleType: number;

  page;
  pageSize;
  collectionSize;
  maxSize;

  selectedRow: number;
  modalRef: BsModalRef;

  siStatus: MetaData;

  constructor(private siService: SalesInvoiceService, private modalService: BsModalService,
              private notificationService: NotificationService) {
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 10;
    this.maxSize = 10;
    this.selectedSi = new SalesInvoice();
    this.findAllSis();
  }

  findAllSis() {
    this.siService.findAll(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.sis = result.content;
      this.collectionSize = result.totalPages * 10;
    });
  }

  selectSi(si, index) {
    this.selectedRow = index;
    this.selectedSi = si;
  }

  searchSi() {
    if (this.searchFilter === 'job') {
      this.siService.findByJobNo(this.keyJobNo).subscribe((data: SalesInvoice) => {
        this.sis = [];
        this.sis.push(data);
      });
    }
    if (this.searchFilter === 'inv') {
      this.siService.findByInvoiceNo(this.keyInvoiceNo).subscribe((data: SalesInvoice) => {
        this.sis = [];
        this.sis.push(data);
      });
    }
    if (this.searchFilter === 'cust') {
      this.siService.findByCustomerNicBr(this.keyCustomerNicBr).subscribe((data: Array<SalesInvoice>) => {
        this.sis = [];
        this.sis = data;
      });
    }
    if (this.searchFilter === 'saleType') {
      this.siService.findBySaleType(this.invSaleType).subscribe((data: Array<SalesInvoice>) => {
        this.sis = [];
        this.sis = data;
      });
    }
  }

  setSearchFilter(filter) {
    this.searchFilter = filter;
    this.searchSi();
  }

  searchCustomer() {
    this.modalRef = this.modalService.show(ManageCustomerComponent, <ModalOptions>{class: 'modal-xl'});
    this.modalRef.content.disableSetCustomer = false;
    this.modalRef.content.modalRef = this.modalRef;
    this.modalService.onHide.subscribe(event => {
      this.keyCustomerNicBr = this.modalRef.content.customer.nicBr;
      this.setSearchFilter('cust');
    })
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAllSis();
  }

  showDetails() {

  }

  amend() {
    this.siService.amendInvoice(this.selectedSi.invoiceNo).subscribe((data: Response) => {
      if (data.code == 200) {
        this.notificationService.showSuccess("Successfully Amended");
      } else {
        this.notificationService.showError("Operation Failed");
      }
    });
  }

  print() {
    this.modalRef = this.modalService.show(InvoiceComponent, <ModalOptions>{class: 'modal-xl'});
    this.modalRef.content.invoiceNo = this.selectedSi.invoiceNo;
    this.modalRef.content.findInvoice();
  }

  payBalance() {
    this.modalRef = this.modalService.show(PaySiBalanceComponent, <ModalOptions>{class: 'modal-md'});
    this.modalRef.content.si = this.selectedSi;
    this.modalRef.content.modalRef = this.modalRef;
    this.modalService.onHide.subscribe(result => {
      this.findAllSis();
    })
  }

}
