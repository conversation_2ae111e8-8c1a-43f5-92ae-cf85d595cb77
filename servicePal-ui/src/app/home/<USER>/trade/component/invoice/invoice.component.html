<div id="print-section" style="padding: 20px; font-family: Verdana">

  <address style="text-align: center">
    <p style="font-weight: bold; font-size: x-large">{{company.name}}</p>
    <p>{{company.fullAddress}}</p>
    <p>{{company.telephone1 + ',' + company.telephone2 + ',' + company.telephone3}}</p>
    <p>{{company.email}}</p>
  </address>

  <div style="width: 100%">
    <hr>
  </div>

  <div style="margin-top: 10px; width: 100%" *ngIf="invoice.customerName !== 'Default Customer'">
    <address style="float: left">
      <p style="font-weight: bold">Invoice #{{invoice.invoiceNo}} <span
        *ngIf="!invoice.directMode"> / Job No - {{invoice.jobNo}}</span></p>
      <p>{{date | date:'medium': '+530'}}</p>
      <p><span>Cashier : </span> {{invoice.createdBy}}</p>
      <p><span>Payment Type: </span> {{paymentStatus}}</p>
    </address>
    <address style="float: right">
      <p>{{invoice.customerName}}</p>
      <p>{{invoice.customer.address}}</p>
      <p>{{invoice.customer.telephone1}}</p>
    </address>
  </div>

  <div style="margin-top: 10px; width: 100%;" *ngIf="invoice.customerName == 'Default Customer'">
    <span style="font-weight: bold">Invoice #{{invoice.invoiceNo}} </span>
    <span style="margin-left: 10%">{{date | date:'medium': '+530'}}</span>
    <span style="margin-left: 10%">Cashier : {{user}} </span>
    <span style="float: right">Payment Type: {{paymentStatus}} </span>
  </div>

  <div>
    <hr style="clear: both">
    <table style="width: 100%; margin: 25px;">
      <thead>
      <tr>
        <th>ID</th>
        <th>Item</th>
        <th>Quantity</th>
        <th>Unit Cost</th>
        <th>Total</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let rec of invoice.salesInvoiceRecords; index as i">
        <td style="padding: 7px">{{i + 1}}</td>
        <td style="padding: 7px">{{rec.itemCode != 0 ? rec.item.itemName : rec.itemName}}</td>
        <td style="padding: 7px">{{rec.quantity}}</td>
        <td style="padding: 7px">{{rec.unitPrice | number : '1.2-2'}}</td>
        <td style="padding: 7px">{{rec.price | number : '1.2-2'}}</td>
      </tr>
      </tbody>
    </table>
  </div>

  <div style="margin-top: 30px">
    <hr style="clear: both">
    <table style="width: 60%; margin-left: 40%;">
      <tr>
        <td style="text-align: end">
          <p style="font-weight: bold">Sub Total</p>
          <p style="font-size: larger">{{invoice.subTotal | number : '1.2-2'}}</p>
        </td>
        <td style="text-align: end" *ngIf="null != invoice.advancePayment">
          <p style="font-weight: bold">Advance</p>
          <p style="font-size: larger">{{invoice.advancePayment | number : '1.2-2'}}</p>
        </td>
        <td style="text-align: end">
          <p style="font-weight: bold">Discount</p>
          <p style="font-size: larger">{{invoice.totalDiscount | number : '1.2-2'}}</p>
        </td>
        <td style="text-align: end">
          <p style="font-weight: bold">Grand Total</p>
          <p style="font-size: larger">{{invoice.totalAmount | number : '1.2-2'}}</p>
        </td>
        <td style="text-align: end">
          <p style="font-weight: bold">Paid Amount</p>
          <p style="font-size: larger">{{invoice.payment | number : '1.2-2'}}</p>
        </td>
        <td *ngIf="invoice.balance > 0" style="text-align: end">
          <p style="font-weight: bold">Balance</p>
          <p style="font-size: larger">{{invoice.balance | number : '1.2-2'}}</p>
        </td>
        <td *ngIf="invoice.cashBalance > 0" style="text-align: end">
          <p style="font-weight: bold">Cash Balance</p>
          <p style="font-size: larger">{{invoice.cashBalance | number : '1.2-2'}}</p>
        </td>
      </tr>
    </table>
  </div>

  <div style="width: 100%; margin-top: 13px">
    <table style="width: 100%">
      <tr style="width: 100%">
        <td style="text-align: left; width: 20%">
          <p>---------------</p>
          <p>Customer Name</p>
        </td>
        <td style="text-align: left; width: 20%">
          <p>---------------</p>
          <p>Customer Signature</p>
        </td>
        <td style="text-align: right; width: 60%">
          <p>---------------</p>
          <p>Cashier</p>
        </td>
      </tr>
    </table>
  </div>
  <hr>
  <div style="width: 100%; text-align: left">
    <p *ngFor="let footerNote of footerNotes">- {{footerNote}}</p>
  </div>
</div>

<div>
  <button class="btn btn-primary float-end m-4" printSectionId="print-section" ngxPrint>
    Print
  </button>
</div>
