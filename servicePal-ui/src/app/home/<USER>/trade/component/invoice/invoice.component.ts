import {Component, OnInit} from '@angular/core';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

import {SalesInvoice} from "../../model/sales-invoice";
import {SalesInvoiceService} from "../../service/sales-invoice.service";
import {Customer} from "../../model/customer";
import {Company} from "../../../../core/model/company";
import {CompanyService} from "../../../../core/service/company.service";
import {DomSanitizer} from "@angular/platform-browser";
import {PrintSettingService} from "../../../../admin/service/print-setting.service";
import {PrintSetting} from "../../../../admin/model/print-setting";

@Component({
  standalone: false,
  selector: 'app-invoice',
  templateUrl: './invoice.component.html',
  styleUrls: ['./invoice.component.css']
})
export class InvoiceComponent implements OnInit {

  invoice: SalesInvoice;
  invoiceNo: string;
  jobNo: string;
  date: Date;
  existingCss: boolean;
  paymentStatus: string;
  company: Company;
  imageFile: any;
  user: string;
  footerNotes = [];

  constructor(
    private salesInvoiceService: SalesInvoiceService, private companyService: CompanyService,
              private printerSettings: PrintSettingService, private sanitizer: DomSanitizer,
    public ref: DynamicDialogRef,
    public config: DynamicDialogConfig
  ) {
  }

  ngOnInit(): void {
    this.invoice = new SalesInvoice();
    this.company = new Company();
    this.invoice.customer = new Customer();
    this.date = new Date(Date.now());
    this.existingCss = true;
    this.user = JSON.parse(localStorage.getItem('currentUser')).user.firstName;
    this.findFooterNote();
  }

  findCompany() {
    this.companyService.findCompany().subscribe((data: Company) => {
      this.company = data;
      this.imageFile = this.sanitizer.bypassSecurityTrustResourceUrl('data:image/jpg;base64,'
        + this.company.logo);
    });
  }

  findFooterNote() {
    this.printerSettings.findAll().subscribe((data: PrintSetting) => {
      this.footerNotes = data.salesInvoiceFooter.split(".");
    });
  }

  findInvoice() {
    this.findCompany();
    this.salesInvoiceService.findByInvoiceNo(this.invoiceNo).subscribe((result: SalesInvoice) => {
      this.invoice = result;
      this.date = new Date(this.invoice.date);
      if (this.invoice.status.value == 'Paid') {
        this.paymentStatus = 'Cash';
      } else {
        this.paymentStatus = 'Credit';
      }
    })
  }

  findInvoiceByJobId() {
    this.findCompany();
    this.salesInvoiceService.findByJobNo(this.jobNo).subscribe((result: SalesInvoice) => {
      this.invoice = result;
      this.date = new Date(this.invoice.date);
      if (this.invoice.status.value == 'Paid') {
        this.paymentStatus = 'Cash';
      } else {
        this.paymentStatus = 'Credit';
      }
    })
  }

}
