import {Component, OnInit} from '@angular/core';
import {BsModalRef} from "ngx-bootstrap/modal";
import {NotificationService} from "../../../../core/service/notification.service";
import {MetaData} from "../../../../core/model/metaData";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {Cheque} from "../../model/cheque";
import {Response} from "../../../../core/model/response";
import {ChequeService} from "../../service/cheque.service";

@Component({
  standalone: false,
  selector: 'app-expense-type',
  templateUrl: './payment-method.component.html',
  styleUrls: ['./payment-method.component.css']
})
export class PaymentMethodComponent implements OnInit {

  modalRef: BsModalRef;

  payment: number;
  chequeCardAmount: number;
  cashAmount: number;
  chequeCardNo: string;
  chequeDate: Date;
  bankList: Array<MetaData>;
  bankId: string;
  cheque: Cheque;

  constructor(private notificationService: NotificationService, private metaDataService: MetaDataService,
              private chequeService: ChequeService) {
  }

  ngOnInit() {
    this.chequeDate = new Date();
    this.loadBanks();
    this.cheque = new Cheque();
  }

  setPayment() {
    if ((this.chequeCardAmount + this.cashAmount) != this.payment) {
      this.notificationService.showError("Please Check Cash /Card Amounts Again");
      return;
    } else {
      this.chequeService.save(this.cheque).subscribe((res: Response)=>{
        if (res.code === 200){
          this.notificationService.showSuccess(res.message);
        }else {
          this.notificationService.showError(res.message);
        }
      });
      this.modalRef.hide();
    }
  }

  closeWindow() {
    this.modalRef.hide();
    this.payment = 0;
    this.chequeCardAmount = 0;
    this.cashAmount = 0;
    this.chequeCardNo = '';
  }

  setBank(event) {
    this.cheque.bank = new MetaData();
    this.cheque.bank.id = this.bankId;
  }

  loadBanks() {
    this.metaDataService.findByCategory("Bank").subscribe((res: Array<MetaData>)=>{
      this.bankList = res;
    })
  }

  setChequeNo(){
    this.chequeCardNo = this.cheque.chequeNo;
  }
}
