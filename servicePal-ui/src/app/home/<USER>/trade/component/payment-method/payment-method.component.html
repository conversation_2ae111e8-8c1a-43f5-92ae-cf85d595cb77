<div class="card">
  <div class="card-header">
    <strong>SALES INVOICE</strong>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="input-group col-md-12">
        <div class="row">
          <div class="mb-3 col-md-6">
            <label>Cheque/Card No</label>
            <input type="text" [(ngModel)]="cheque.chequeNo" (change)="setChequeNo()" required class="form-control" name="chequeCardNo">
          </div>
          <div class="mb-3 col-md-6">
            <label>Bank</label>
            <select name="bankSelect" (change)="setBank($event)" [(ngModel)]="bankId" required
                    #bankSelect="ngModel" class="form-control">
              <option disabled>-select-</option>
              <option *ngFor="let bank of bankList" [value]="bank.id">
                {{bank.value}}
              </option>
            </select>
          </div>
          <div class="mb-3 col-md-6">
            <label>Cheque/Card Date</label>
            <input type="text" [(ngModel)]="cheque.chequeDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }" required class="form-control" name="chequeCardDate">
          </div>
          <div class="mb-3 col-md-6">
            <label>Cheque/Card Amount</label>
            <input type="number" [(ngModel)]="chequeCardAmount" required class="form-control" name="chequeCardAmount">
          </div>
          <div class="mb-3 col-md-12">
            <label>Cash Amount</label>
            <input type="number" [(ngModel)]="cashAmount" required class="form-control" name="chequeCardAmount">
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <button type="button" class="btn btn-success float-start" (click)="setPayment()">set & save</button>
            <button type="button" class="btn btn-danger float-end ms-1" (click)="closeWindow()">Close</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
