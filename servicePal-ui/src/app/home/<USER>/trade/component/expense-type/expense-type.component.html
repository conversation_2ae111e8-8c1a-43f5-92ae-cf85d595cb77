<div class="card">
  <div class="card-header">
    <strong>MANAGE EXPENSE TYPES</strong>
  </div>
  <div class="card-body">
    <div class="row g-3">
      <div class="input-group col-md-6">
        <div class="row g-3">
          <div class="col-md-12">
            <input [(ngModel)]="keyExpType"
                   [typeahead]="expenseTypes"
                   (typeaheadLoading)="loadExpenseTypes()"
                   (typeaheadOnSelect)="setSelectedExpenseType($event)"
                   [typeaheadOptionsLimit]="7"

                   typeaheadOptionField="name"
                   placeholder="Search Expense Type"
                   autocomplete="off"
                   size="16"
                   required
                   class="form-control" name="expenseType">
          </div>

          <table class="table table-striped">
            <thead>
            <th>Expense Type Category</th>
            <th>Expense Type Name</th>
            </thead>
            <tbody>
            <tr *ngFor="let exp of expenseTypes,let i=index"
                (click)="selectExpense(exp,i)"
                [class.active]="i === selectedRow">
              <td>{{exp.category.name}}</td>
              <td>{{exp.name}}</td>
            </tr>
            </tbody>
          </table>
          <div class="col-xs-12 col-12">
            <pagination class="pagination-sm justify-content-center"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page"
                        [boundaryLinks]="true"

                        (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <form #manageExpenseTypeForm="ngForm" (ngSubmit)="saveExpenseType(); manageExpenseTypeForm.reset()">
          <div class="form-group">
            <label>Expense Category</label>
            <select class="form-control" [(ngModel)]="expenseType.category.id"
                    name="expCat" #expCat="ngModel" id="expCat" required>
              <option></option>
              <option *ngFor="let cat of expCategories" [ngValue]="cat.id">{{cat.name}}</option>
            </select>
            <div *ngIf="expCat.errors && (expCat.invalid || expCat.touched)">
              <small class="text-danger" [class.d-none]="expCat.valid || expCat.untouched">*Expense Category is required
              </small>
            </div>
          </div>

          <div class="form-group">
            <label>Expense Name</label>
            <input required #name="ngModel" [class.is-invalid]="name.invalid && name.touched"
                   class="form-control" id="name" [(ngModel)]="expenseType.name" name="name"
                   placeholder="Expense Name">
            <div *ngIf="name.errors && (name.invalid || name.touched)">
              <small class="text-danger" [class.d-none]="name.valid || name.untouched">*Description is
                required
              </small>
            </div>
          </div>

          <div class="form-check checkbox me-2">
            <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                   [(ngModel)]="expenseType.active">
            <label class="form-check-label" for="check3">Active</label>
          </div>

          <div class="row g-3 float-end">
            <div class="me-3">
              <button type="submit" class="btn btn-success" [disabled]="!manageExpenseTypeForm.form.valid">save
              </button>
            </div>

            <div class="me-3">
              <button type="button" class="btn btn-primary active" [disabled]="!manageExpenseTypeForm.form.valid"
                      (click)="updateExpenseType()">update
              </button>
            </div>

            <div class="me-3">
              <button type="button" class="btn btn-warning" (click)="clear()">clear</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

