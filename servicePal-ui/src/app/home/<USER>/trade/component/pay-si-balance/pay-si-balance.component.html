<div class="card">
  <div class="card-header">
    <strong>Pay Balance</strong>
  </div>
  <div class="card-body">
    <form #payBalanceForm="ngForm">
      <div class="row">
        <div class="col-md-12 form-group">
          <label>Invoice No</label>
          <input type="text" class="form-control" id="invoiceNo" name="invoiceNo" #invoiceNo="ngModel" disabled
                 [ngModel]="si.invoiceNo"
                 [class.is-invalid]="invoiceNo.invalid && invoiceNo.touched">
        </div>

        <div class="col-md-12 form-group">
          <label>Customer</label>
          <input type="text" class="form-control" id="customerName" name="customerName" #customerName="ngModel" disabled
                 [ngModel]="si.customer.name" [class.is-invalid]="customerName.invalid && customerName.touched">
        </div>

        <div class="col-md-12 form-group">
          <label>Total Amount</label>
          <input type="text" class="form-control" id="total" name="total" #total="ngModel" disabled
                 [ngModel]="si.totalAmount" [class.is-invalid]="total.invalid && total.touched">
        </div>

        <div class="col-md-12 form-group">
          <label>Balance</label>
          <input type="text" class="form-control" id="balance" name="balance" #balance="ngModel" disabled
                 [ngModel]="si.balance" [class.is-invalid]="balance.invalid && balance.touched">
        </div>

        <div class="col-md-12 form-group">
          <label>Payment</label>
          <input type="number" class="form-control" id="payment" name="payment" #payment="ngModel"
                 [(ngModel)]="payingAmount" [class.is-invalid]="payment.invalid && payment.touched">
        </div>

        <div class="col-md-12">
          <button type="button" class="btn btn-success float-end" mwlConfirmationPopover
                  (confirm)="payBalance()">Pay
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
