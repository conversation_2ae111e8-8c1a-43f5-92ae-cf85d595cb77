import {Component, OnInit} from '@angular/core';
import {SalesInvoice} from "../../model/sales-invoice";
import {SalesInvoiceService} from "../../service/sales-invoice.service";
import {Response} from "../../../../core/model/response";
import {NotificationService} from "../../../../core/service/notification.service";
import {Customer} from "../../model/customer";
import {BsModalRef} from "ngx-bootstrap/modal";

@Component({
  standalone: false,
  selector: 'app-pay-si-balance',
  templateUrl: './pay-si-balance.component.html',
  styleUrls: ['./pay-si-balance.component.css']
})
export class PaySiBalanceComponent implements OnInit {

  si: SalesInvoice;
  payingAmount: number;
  modalRef: BsModalRef;

  constructor(private siService: SalesInvoiceService, private notificationService: NotificationService) {
  }

  ngOnInit(): void {
    this.si = new SalesInvoice();
    this.si.customer = new Customer();
  }

  payBalance() {
    this.siService.payBalance(this.si.invoiceNo, this.payingAmount).subscribe((result: Response) => {
      if (result.code === 200) {
        this.notificationService.showSuccess(result.message);
        this.modalRef.hide();
      } else {
        this.notificationService.showError(result.message);
      }
    })
  }
}
