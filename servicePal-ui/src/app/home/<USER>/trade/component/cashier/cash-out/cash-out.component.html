<div class="card">
  <div class="card-header">
    Cash Out
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-6">
        <label>Counter</label>
        <input class="form-control" [ngModel]="cashier.counter.counterNo" readonly>
      </div>
      <div class="col-md-6">
        <label>Available Amount</label>
        <input type="number" class="form-control" [ngModel]="cashier.currentBalance" readonly>
      </div>
      <div class="col-md-6">
        <label>Withdraw Amount</label>
        <input type="number" class="form-control" name="withdrawAmount" [(ngModel)]="withdrawingAmount">
      </div>
      <div class="col-md-6">
        <label>Purpose</label>
        <div class="input-group">
          <select class="form-control" (change)="setSelectedPurpose()" name="withdrawPurposeSelect"
                  [(ngModel)]="purposeId" required #withdrawPurposeSelect="ngModel"
                  [class.is-invalid]="withdrawPurposeSelect.invalid && withdrawPurposeSelect.touched">
            <option [value]="undefined">-Select Purpose-</option>
            <option *ngFor="let typ of purposes" [value]="typ.id">
              {{typ.value}}
            </option>
          </select>
        </div>
      </div>

    </div>

    <div class="row mt-3">
      <div class="col-md-12">
        <button type="button" class="btn btn-warning float-end ms-2" (click)="clear()">
          Clear
        </button>
        <button type="button" class="btn btn-danger float-end ms-2" (click)="withdraw()" [disabled]="disableWithdraw">
          Withdraw
        </button>
      </div>
    </div>

  </div>
</div>

