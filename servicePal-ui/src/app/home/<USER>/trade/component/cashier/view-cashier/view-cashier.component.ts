import {Component, OnInit} from '@angular/core';
import {Cashier} from "../../../model/cashier";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {DayStartComponent} from "../day-start/day-start.component";
import {CashierService} from "../../../service/cashier.service";
import {User} from "../../../../../admin/model/user";
import {Counter} from "../../../../../admin/model/counter";
import {CashInComponent} from "../cash-in/cash-in.component";
import {CashOutComponent} from "../cash-out/cash-out.component";
import {DayCloseComponent} from "../day-close/day-close.component";

@Component({
  standalone: false,
  selector: 'app-view-cashier',
  templateUrl: './view-cashier.component.html',
  styleUrls: ['./view-cashier.component.css']
})
export class ViewCashierComponent implements OnInit {

  cashier: Cashier;
  modalRefDayStart: BsModalRef;
  modalRefAddCash: BsModalRef;
  modalWithdrawCash: BsModalRef;
  modalDayEnd: BsModalRef;
  user: User;
  date: Date;
  disableDayStart: boolean;
  disableDayClose: boolean;
  disableAddCash: boolean;
  disableWithdrawCash: boolean;

  constructor(private modalService: BsModalService,
              private cashierService: CashierService) {
  }

  ngOnInit(): void {
    this.disableDayStart = true;
    this.disableAddCash = true;
    this.disableWithdrawCash = true;
    this.disableDayClose = true;
    this.cashier = new Cashier();
    this.cashier.counter = new Counter();
    this.user = new User();
    this.user = JSON.parse(localStorage.getItem('currentUser')).user;
    this.date = new Date();
    this.findCashier();
  }

  findCashier() {
    this.cashierService.findCashierByCounterNo(this.user.counter).subscribe((data: Cashier) => {
      this.cashier = data;
      this.handleButtons();
    });
  }

  handleButtons() {
    if ((this.cashier.active) && (new Date(this.cashier.lastStartedDate).toLocaleDateString() === new Date().toLocaleDateString())) {
      this.disableWithdrawCash = false;
      this.disableAddCash = false;
    } else {
      this.disableWithdrawCash = true;
      this.disableAddCash = true;
    }
    if ((this.cashier.active) && (new Date(this.cashier.lastClosedDate) < new Date())) {
      this.disableDayClose = false;
    } else {
      this.disableDayClose = true;
    }
    if ((!this.cashier.active) && (new Date(this.cashier.lastClosedDate) < new Date())) {
      this.disableDayStart = false;
    } else {
      this.disableDayStart = true;
    }
  }

  openDayStart() {
    this.modalRefDayStart = this.modalService.show(DayStartComponent, <ModalOptions>{class: 'modal-md'});
    this.modalRefDayStart.content.modalRef = this.modalRefDayStart;
    this.modalRefDayStart.content.findCashier(this.cashier.counter.id);
    this.modalService.onHide.subscribe(val => {
      this.findCashier();
    })
  }

  addCash() {
    this.modalRefAddCash = this.modalService.show(CashInComponent, <ModalOptions>{class: 'modal-md'});
    this.modalRefAddCash.content.modalRef = this.modalRefAddCash;
    this.modalRefAddCash.content.findCashier(this.cashier.counter.id);
    this.modalService.onHide.subscribe(val => {
      this.findCashier();
    })
  }

  withdrawCash() {
    this.modalWithdrawCash = this.modalService.show(CashOutComponent, <ModalOptions>{class: 'modal-md'});
    this.modalWithdrawCash.content.modalRef = this.modalWithdrawCash;
    this.modalWithdrawCash.content.findCashier(this.cashier.counter.id);
    this.modalService.onHide.subscribe(val => {
      this.findCashier();
    })
  }

  dayEnd() {
    this.modalDayEnd = this.modalService.show(DayCloseComponent, <ModalOptions>{class: 'modal-md'});
    this.modalDayEnd.content.modalRef = this.modalDayEnd;
    this.modalDayEnd.content.findCashier(this.cashier.counter.id);
    this.modalService.onHide.subscribe(val => {
      this.findCashier();
    })
  }

}
