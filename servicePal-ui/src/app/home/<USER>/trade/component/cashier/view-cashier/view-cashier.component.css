.btn-outline-customize {
  width: 100%;
  color: rgb(89, 12, 48);
  border-color: rgb(89, 12, 48);
}

.btn-outline-customize:hover {
  color: #fff;
  background-color: rgb(89, 12, 48);
  border-color: rgb(89, 12, 48);
}

.btn-outline-customize:focus, .btn-outline-customize.focus {
  box-shadow: 0 0 0 0.2rem rgba(89, 12, 48, 0.5);
}

.btn-outline-customize.disabled, .btn-outline-customize:disabled {
  color: rgb(89, 12, 48);
  background-color: transparent;
}

.btn-outline-customize:not(:disabled):not(.disabled):active, .btn-outline-customize:not(:disabled):not(.disabled).active,
.show > .btn-outline-customize.dropdown-toggle {
  color: #fff;
  background-color: rgb(89, 12, 48);
  border-color: rgb(89, 12, 48);
}

.btn-outline-customize:not(:disabled):not(.disabled):active:focus, .btn-outline-customize:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-customize.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(89, 12, 48, 0.5);
}

.example-viewport {
  height: 200px;
  width: 100%;
}

.example-item {
  height: 50px;
}

cdk-virtual-scroll-viewport::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #F5F5F5;
}

cdk-virtual-scroll-viewport::-webkit-scrollbar {
  width: 12px;
  background-color: #F5F5F5;
}

cdk-virtual-scroll-viewport::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
  background-color: #555;
}

.btn-customize {
  color: #fff;
  background-color: rgb(89, 12, 48);
  border-color: rgb(89, 12, 48);
}

.btn-customize:hover {
  color: #fff;
  background-color: rgb(31, 3, 17);
  border-color: rgb(89, 12, 48);
}

.btn-customize:focus, .btn-customize.focus {
  color: #fff;
  background-color: rgb(31, 3, 17);
  border-color: rgb(31, 3, 17);
  box-shadow: 0 0 0 0.2rem rgba(95, 1, 48, 0.5);
}

.btn-customize.disabled, .btn-customize:disabled {
  color: #fff;
  background-color: rgb(89, 12, 48);
  border-color: rgb(89, 12, 48);
}

.btn-customize:not(:disabled):not(.disabled):active, .btn-customize:not(:disabled):not(.disabled).active,
.show > .btn-customize.dropdown-toggle {
  color: #fff;
  background-color: rgb(89, 12, 48);
  border-color: rgb(89, 12, 48);
}

.btn-customize:not(:disabled):not(.disabled):active:focus, .btn-customize:not(:disabled):not(.disabled).active:focus,
.show > .btn-customize.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(95, 1, 48, 0.5);
}
