<div class="card">
  <div class="card-header">
    Cashier
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-6">
        <label>Date</label>
        <input class="form-control" name="date" [ngModel]="date| date" readonly>
      </div>
      <div class="col-md-6">
        <label>Counter</label>
        <input class="form-control" [ngModel]="cashier.counter.counterNo" readonly>
      </div>
      <div class="col-md-6">
        <label>User</label>
        <input class="form-control" [ngModel]="user.username" readonly>
      </div>
      <div class="col-md-6">
        <label>Opening Balance</label>
        <input class="form-control" [ngModel]="cashier.openingBalance | number" readonly>
      </div>
      <div class="col-md-6">
        <label>Current Balance</label>
        <input class="form-control" [ngModel]="cashier.currentBalance | number" readonly>
      </div>
    </div>

    <div class="row mt-3">
      <div class="col-md-12">
        <button type="button" class="btn btn-customize float-end ms-2" (click)="dayEnd()" [disabled]="disableDayClose">
          Day Close
        </button>
        <button type="button" class="btn btn-customize float-end ms-2" (click)="withdrawCash()" [disabled]="disableWithdrawCash">
          Withdrawal
        </button>
        <button type="button" class="btn btn-customize float-end ms-2" (click)="addCash()" [disabled]="disableAddCash">
          Add Cash
        </button>
        <button type="button" class="btn btn-customize float-end ms-2" (click)="openDayStart()" [disabled]="disableDayStart">
          Day Start
        </button>
      </div>
    </div>
  </div>
</div>
