<div class="card">
  <div class="card-header">
    Cash In
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-6">
        <label>Counter</label>
        <input class="form-control" [ngModel]="cashier.counter.counterNo" readonly>
      </div>
      <div class="col-md-6">
        <label>Available Amount</label>
        <input type="number" class="form-control" [ngModel]="cashier.currentBalance " readonly>
      </div>
      <div class="col-md-6">
        <label>Amount to Be Add</label>
        <input type="number" class="form-control" name="addingAmount" [(ngModel)]="addingAmount" (keyup)="calculateTotal()">
      </div>
      <div class="col-md-6">
        <label>Total Amount</label>
        <input type="number" class="form-control" [ngModel]="totalAmount" readonly>
      </div>
    </div>

    <div class="row mt-3">
      <div class="col-md-12">
        <button type="button" class="btn btn-warning float-end ms-2">
          Clear
        </button>
        <button type="button" class="btn btn-success float-end ms-2" (click)="addCash()"[disabled]="disableAddCash">
          Add Cash
        </button>
      </div>
    </div>

  </div>
</div>
