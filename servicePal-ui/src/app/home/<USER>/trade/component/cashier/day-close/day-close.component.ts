import {Component, OnInit} from '@angular/core';
import {Cashier} from "../../../model/cashier";
import {CashierService} from "../../../service/cashier.service";
import {BsModalRef, BsModalService} from "ngx-bootstrap/modal";
import {CashRecordService} from "../../../service/cash-record.service";
import {NotificationService} from "../../../../../core/service/notification.service";
import {CashierHistoryService} from "../../../service/cashier-history.service";

@Component({
  standalone: false,
  selector: 'app-day-close',
  templateUrl: './day-close.component.html',
  styleUrls: ['./day-close.component.css']
})
export class DayCloseComponent implements OnInit {

  date: string;
  cashier: Cashier;
  actualAmount: number;
  withdrawalAmount: number;
  balance: number;
  modalRef: BsModalRef;

  constructor(private cashierService: CashierService,
              private modalService: BsModalService,
              private cashRecordService: CashRecordService,
              private notificationService: NotificationService,
              private cashierHistoryService: CashierHistoryService) {
  }

  //these balances need to set to cashier.

  ngOnInit(): void {
    this.date = new Date().toLocaleDateString();
    this.cashier = new Cashier();
    this.withdrawalAmount = 0;
  }

  findCashier(counterNo: string) {
    this.cashierService.findCashierByCounterNo(counterNo).subscribe((data: Cashier) => {
      this.cashier = data;
    });
  }

  calculateBalance() {
    this.balance = this.actualAmount - this.withdrawalAmount;
  }

  save() {
    if (this.withdrawalAmount > this.actualAmount) {
      this.notificationService.showError("You cant withdraw more than available amount");
    } else {
      this.cashierHistoryService.save(this.actualAmount, this.withdrawalAmount, this.balance)
        .subscribe((val) => {
          if (val){
            this.notificationService.showSuccess("Cashier Closed Successfully");
            this.modalRef.hide();
          }
        })
    }

  }

  clear(){
    this.modalRef.hide();
  }
}
