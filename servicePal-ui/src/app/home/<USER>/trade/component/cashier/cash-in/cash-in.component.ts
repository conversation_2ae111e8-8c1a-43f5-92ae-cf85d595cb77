import {Component, OnInit} from '@angular/core';
import {Cashier} from "../../../model/cashier";
import {CashRecord} from "../../../model/cash-record";
import {Counter} from "../../../../../admin/model/counter";
import {CashierService} from "../../../service/cashier.service";
import {BsModalRef, BsModalService} from "ngx-bootstrap/modal";
import {CashRecordService} from "../../../service/cash-record.service";
import {NotificationService} from "../../../../../core/service/notification.service";

@Component({
  standalone: false,
  selector: 'app-cash-in',
  templateUrl: './cash-in.component.html',
  styleUrls: ['./cash-in.component.css']
})
export class CashInComponent implements OnInit {

  cashier: Cashier; //need to add cash record
  cashRecord: CashRecord;
  availableAmount: number;
  totalAmount: number;
  addingAmount: number;
  modalRef: BsModalRef;
  disableAddCash: boolean;

  constructor(private cashierService: CashierService,
              private modalService: BsModalService,
              private cashRecordService: CashRecordService,
              private notificationService: NotificationService) {
  }

  ngOnInit(): void {
    this.cashRecord = new CashRecord();
    this.cashier = new Cashier();
    this.cashier.counter = new Counter();
  }

  findCashier(counterNo: string) {
    this.cashierService.findCashierByCounterNo(counterNo).subscribe((data: Cashier) => {
      this.cashier = data;
    });
    this.disableAddCash = true;
  }

  calculateTotal() {
    this.totalAmount = this.cashier.currentBalance + this.addingAmount;
    if(this.addingAmount > 0 && this.addingAmount){
      this.disableAddCash = false;
    }
  }

  addCash() {
    this.cashRecordService.addCash(this.addingAmount).subscribe(val => {
      if (val) {
        this.notificationService.showSuccess("Cash Added Successfully");
        this.modalRef.hide();
      }
    })
  }
}

