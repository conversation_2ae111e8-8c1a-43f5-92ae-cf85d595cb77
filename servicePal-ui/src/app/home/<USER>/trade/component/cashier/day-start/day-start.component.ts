import {Component, OnInit} from '@angular/core';
import {User} from "../../../../../admin/model/user";
import {CashierService} from "../../../service/cashier.service";
import {CashRecordService} from "../../../service/cash-record.service";
import {NotificationService} from "../../../../../core/service/notification.service";
import {BsModalRef} from "ngx-bootstrap/modal";
import {Cashier} from "../../../model/cashier";

@Component({
  standalone: false,
  selector: 'app-day-start',
  templateUrl: './day-start.component.html',
  styleUrls: ['./day-start.component.css']
})
export class DayStartComponent implements OnInit {

  cashier: Cashier;
  counterNo: string;
  date: Date;
  user: User;
  addingAmount: number;
  totalAmount: number;
  modalRef: BsModalRef;
  disableStartButton: boolean;


  constructor(private cashierService: CashierService,
              private cashRecordService: CashRecordService,
              private notificationService: NotificationService,
              private modalService: BsModalRef) {
  }

  ngOnInit(): void {
    this.cashier = new Cashier();
    this.date = new Date();
    this.user = JSON.parse(localStorage.getItem('currentUser')).user;
    this.disableStartButton = true;
  }

  findCashier(counterNo: string) {
    this.cashierService.findCashierByCounterNo(counterNo).subscribe((data: Cashier) => {
      this.cashier = data;
    });
  }

  calculateTotalAmount() {
    if(this.addingAmount >= 0 && this.addingAmount !== null){
      this.disableStartButton = false;
      console.log(this.disableStartButton)
    }
    this.totalAmount = this.cashier.currentBalance + this.addingAmount;
  }

  save() {
    this.cashRecordService.dayStart(this.addingAmount).subscribe(val => {
      if (val) {
        this.notificationService.showSuccess("Day started, Have a Great One..!");
        this.modalRef.hide();
      } else {
        this.notificationService.showError("Something Went Wrong");
      }
    })
  }

  clear() {
    this.modalRef.hide()
  }
}
