<div class="card">
  <div class="card-header">
    Day Start
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-6">
        <label>Date</label>
        <input class="form-control" name="date" [(ngModel)]="date">
      </div>
      <div class="col-md-6">
        <label>Current Amount</label>
        <input type="number" class="form-control" [ngModel]="cashier.currentBalance" readonly>
      </div>
      <div class="col-md-6">
        <label>Adding Amount</label>
        <input type="number" class="form-control" name="addingAmount" [(ngModel)]="addingAmount"
               (keyup)="calculateTotalAmount()">
      </div>
      <div class="col-md-6">
        <label>Total Amount</label>
        <input type="number" class="form-control" name="totalAmount" [(ngModel)]="totalAmount">
      </div>
    </div>
    <div class="row mt-3">
      <div class="col-md-12">
        <button type="button" class="btn btn-success float-end ms-2" (click)="clear()">
          Clear
        </button>
        <button type="button" class="btn btn-warning float-end ms-2" [disabled]="disableStartButton" (click)="save()">
          Start
        </button>
      </div>
    </div>

  </div>
</div>

