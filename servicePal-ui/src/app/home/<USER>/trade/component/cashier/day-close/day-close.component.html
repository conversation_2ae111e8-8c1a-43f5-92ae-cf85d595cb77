<div class="card">
  <div class="card-header">
    Day Close
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-6">
        <label>Date</label>
        <input class="form-control" name="date" [(ngModel)]="date" >
      </div>
      <div class="col-md-6">
        <label>Opening Amount</label>
        <input class="form-control" [ngModel]="cashier.openingBalance" readonly>
      </div>
      <div class="col-md-6">
        <label>Current Amount</label>
        <input class="form-control" [ngModel]="cashier.currentBalance" readonly>
      </div>
      <div class="col-md-6">
        <label>Actual Amount</label>
        <input type="number" class="form-control" name="actualAmount" [(ngModel)]="actualAmount" (keyup)="calculateBalance()" >
      </div>
      <div class="col-md-6">
        <label>Withdrawal Amount</label>
        <input type="number" class="form-control" name="withdrawalAmount" [(ngModel)]="withdrawalAmount"
               (keyup)="calculateBalance()">
      </div>
      <div class="col-md-6">
        <label>Balance</label>
        <input type="number" class="form-control" [ngModel]="balance" readonly>
      </div>
    </div>

    <div class="row mt-3">
      <div class="col-md-12">
        <button type="button" class="btn btn-danger float-end ms-2">
          Clear
        </button>
        <button type="button" class="btn btn-success float-end ms-2" mwlConfirmationPopover (confirm)="save()">
          Save
        </button>
      </div>
    </div>

  </div>
</div>
