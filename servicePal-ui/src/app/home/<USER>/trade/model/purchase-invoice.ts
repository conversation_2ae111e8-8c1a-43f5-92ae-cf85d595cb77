import {Supplier} from './supplier';
import {PurchaseInvoiceRecord} from './purchase-invoice-record';
import {MetaData} from '../../../core/model/metaData';

export class PurchaseInvoice {

  id: string;

  purchaseInvoiceNo: string;

  invoiceNo: string;

  date: Date;

  supplier: Supplier;

  purchaseInvoiceRecords: Array<PurchaseInvoiceRecord>;

  totalAmount: number;

  payment: number;

  balance: number;

  dueDate: Date;

  status: MetaData;

  paymentMethod: MetaData;

  discount: number;

}
