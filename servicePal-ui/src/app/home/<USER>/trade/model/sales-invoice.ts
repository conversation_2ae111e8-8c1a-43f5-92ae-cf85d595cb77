import {SalesInvoiceRecord} from './sales-invoice-record';
import {MetaData} from '../../../core/model/metaData';
import {Customer} from './customer';
import {Item} from '../../inventory/model/item';

export class SalesInvoice {

  id: string;

  invoiceNo: string;

  jobNo: string;

  date: string;

  services: Array<Item>;

  transport: Item;

  advancePayment: number;

  partsCharge: number;

  serviceCharge: number;

  transportCharge: number;

  subTotal: number;

  totalAmount: number;

  balance: number;

  totalDiscount: number;

  payment: number;

  cashBalance: number;

  paymentMethod: MetaData;

  dueDate: Date;

  paidDate: Date;

  status: MetaData;

  salesInvoiceRecords: Array<SalesInvoiceRecord>;

  customerName: string;

  customer: Customer;

  cashlessAmount: number;

  cashAmount: number;

  cardOrVoucherNo: number;

  directMode: boolean;

  createdBy: string;

}
