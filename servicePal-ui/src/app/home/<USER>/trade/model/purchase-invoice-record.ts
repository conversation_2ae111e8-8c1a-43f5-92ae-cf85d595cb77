import {Item} from '../../inventory/model/item';
import {MetaData} from '../../../core/model/metaData';

export class PurchaseInvoiceRecord {

  public id: string;

  quantity: number;

  itemCost: number;

  itemName: string;

  sellingPrice: number;

  itemCode: string;

  barcode: string;

  discount: number;

  totalAmount: number;

  subTotal: number;

  item: Item;

  date;

  warehouseCode: number;

}
