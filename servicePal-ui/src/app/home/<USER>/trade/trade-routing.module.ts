import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {CreatePiComponent} from './component/create-pi/create-pi.component';
import {SupplierComponent} from './component/supplier/supplier.component';
import {CreateSiComponent} from './component/create-si/create-si.component';
import {ManageSiComponent} from './component/manage-si/manage-si.component';
import {ManagePiComponent} from './component/manage-pi/manage-pi.component';
import {InvoiceComponent} from "./component/invoice/invoice.component";
import {PaySiBalanceComponent} from "./component/pay-si-balance/pay-si-balance.component";
import {NewCustomerComponent} from "./component/customer/new-customer/new-customer.component";
import {ManageCustomerComponent} from "./component/customer/manage-customer/manage-customer.component";
import {ExpenseTypeComponent} from "./component/expense-type/expense-type.component";
import {ExpenseComponent} from "./component/expense/expense.component";
import {ViewCashierComponent} from "./component/cashier/view-cashier/view-cashier.component";
import {CashInComponent} from "./component/cashier/cash-in/cash-in.component";
import {CashOutComponent} from "./component/cashier/cash-out/cash-out.component";
import {DayCloseComponent} from "./component/cashier/day-close/day-close.component";
import {DayStartComponent} from "./component/cashier/day-start/day-start.component";
import {PaymentHistoryComponent} from "./component/payment-history/payment-history.component";
import {PaymentMethodComponent} from "./component/payment-method/payment-method.component";

const routes: Routes = [
  {
    path: 'new_purchase_invoice',
    component: CreatePiComponent
  },
  {
    path: 'manage_purchase_invoices',
    component: ManagePiComponent
  },
  {
    path: 'new_sales_invoice',
    component: CreateSiComponent
  },
  {
    path: 'manage_sales_invoices',
    component: ManageSiComponent
  },
  {
    path: 'invoice',
    component: InvoiceComponent
  },
  {
    path: 'payBalance',
    component: PaySiBalanceComponent
  },
  {
    path: 'new_customer',
    component: NewCustomerComponent
  },
  {
    path: 'manage_customer',
    component: ManageCustomerComponent
  },
  {
    path: 'manage_supplier',
    component: SupplierComponent
  },
  {
    path: 'expense_type',
    component: ExpenseTypeComponent
  },
  {
    path: 'new_expense',
    component: ExpenseComponent
  },
  {
    path: 'cashier',
    component: ViewCashierComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class TradeRoutingModule {
}

export const tradeRouteParams = [CreatePiComponent, SupplierComponent, PaySiBalanceComponent, InvoiceComponent,
  CreatePiComponent, ManagePiComponent, CreateSiComponent, ManageSiComponent, ExpenseComponent,
  ExpenseTypeComponent, ViewCashierComponent, CashInComponent, CashOutComponent, DayCloseComponent,
  DayStartComponent, PaymentHistoryComponent, ExpenseComponent, PaymentMethodComponent];
