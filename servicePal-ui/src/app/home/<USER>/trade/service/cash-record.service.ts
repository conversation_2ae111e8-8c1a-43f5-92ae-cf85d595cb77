import {Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {TradeConstants} from "../trade-constants";

@Injectable({
  providedIn: 'root'
})
export class CashRecordService {

  constructor(private http: HttpClient) {
  }

  dayStart(addingAmount) {
    return this.http.get(TradeConstants.DAY_START, {params: {addingAmount: addingAmount}});
  }

  addCash(addingAmount) {
    return this.http.get(TradeConstants.ADD_CASH, {params: {addingAmount: addingAmount}});
  }

  withdraw(withdrawingAmount, purpose) {
    return this.http.get(TradeConstants.WITHDRAW_CASH,
      {
        params: {
          withdrawingAmount: withdrawingAmount,
          purpose: purpose
        }
      });
  }

  findByCounterAndDataBetween(counterId, sDate, eDate) {
    return this.http.get(TradeConstants.FIND_BY_COUNTER_AND_DATES_BETWEEN,
      {
        params: {
          counterId: counterId,
          sDate: sDate,
          eDate: eDate
        }
      });
  }

  findByTypeAndDataBetween(typeId, sDate, eDate) {
    return this.http.get(TradeConstants.FIND_BY_TYPE_AND_DATES_BETWEEN,
      {
        params: {
          typeId: typeId,
          sDate: sDate,
          eDate: eDate
        }
      });
  }

  findByDataBetween(sDate, eDate) {
    return this.http.get(TradeConstants.FIND_BY_DATES_BETWEEN,
      {
        params: {
          sDate: sDate,
          eDate: eDate
        }
      });
  }

  findByCounterAndTypeAndDataBetween(counterId, typeId, sDate, eDate) {
    return this.http.get(TradeConstants.FIND_BY_COUNTER_AND_TYPE_AND_DATES_BETWEEN,
      {
        params: {
          counterId: counterId,
          typeId: typeId,
          sDate: sDate,
          eDate: eDate
        }
      });
  }

  calculateCashOut(counterId, date){
    return this.http.get(TradeConstants.GET_CALCULATED_CASH_OUT,
      {params: {counterId: counterId, sDate: date}});
  }

  getDayEndWithdrawalAmount(counterId, date){
    return this.http.get(TradeConstants.GET_DAY_END_WITHDRAWAL_AMOUNT,
      {params: {counterId: counterId, sDate: date}});
  }

  calculateTotalCashOut(counterId, date){
    return this.http.get(TradeConstants.GET_CALCULATED_TOTAL_CASH_OUT,
      {params: {counterId: counterId, sDate: date}});
  }

  calculateTotalCashIn(counterId, date){
    return this.http.get(TradeConstants.GET_CALCULATED_TOTAL_CASH_IN,
      {params: {counterId: counterId, sDate: date}});
  }

  calculateOtherCashIn(counterId, date){
    return this.http.get(TradeConstants.GET_CALCULATED_OTHER_CASH_IN,
      {params: {counterId: counterId, sDate: date}});
  }





}
