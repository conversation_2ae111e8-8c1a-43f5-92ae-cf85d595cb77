import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {TradeConstants} from "../trade-constants";
import {ExpenseType} from "../model/expense-type";

@Injectable({
  providedIn: 'root'
})

export class ExpenseTypeService {

  constructor(private http: HttpClient) {
  }

  save(brand) {
    return this.http.post<any>(TradeConstants.SAVE_EXPENSE_TYPE, brand);
  }

  public findAll(page, pageSize) {
    return this.http.get(TradeConstants.GET_EXPENSE_TYPE, {params: {page: page, pageSize: pageSize}});
  }

  public findByName(name) {
    return this.http.get<ExpenseType[]>(TradeConstants.SEARCH_EXPENSE_TYPE, {params: {name: name}});
  }

}
