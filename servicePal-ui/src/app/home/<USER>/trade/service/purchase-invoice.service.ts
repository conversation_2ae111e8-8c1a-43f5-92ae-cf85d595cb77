
import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {TradeConstants} from '../trade-constants';
import {PurchaseInvoice} from "../model/purchase-invoice";


@Injectable({
  providedIn: 'root'
})
export class PurchaseInvoiceService {

  constructor(private http: HttpClient) {
  }


  save(purchaseInvoiceModel) {
    return this.http.post(TradeConstants.SAVE_PURCHASE_INVOICE, purchaseInvoiceModel);
  }

  findAllPages(page, pageSize) {
    return this.http.get(TradeConstants.GET_PURCHASE_INVOICE_PAGINATION, {params: {page: page, pageSize: pageSize}});
  }

  findAllByName(keyPurchaseInvoice: string) {
    return this.http.get(TradeConstants.FIND_ALL_BY_INVOICE_LIKE, {params: {name: keyPurchaseInvoice}});
  }

  findAllBySupplier(id: string) {
    return this.http.get(TradeConstants.FIND_ALL_BY_SUPPLIER, {params: {id: id}});
  }

  findAllByInvoiceNo(invoiceNo: string) {
    return this.http.get(TradeConstants.FIND_ALL_BY_PURCHASE_INVOICE_NO, {params: {invoiceNo: invoiceNo}});
  }

  findById(id: string) {
    return this.http.get(TradeConstants.FIND_ALL_BY_INVOICE_ID, {params: {id: id}});
  }

  findAllByDate(date) {
    return this.http.get(TradeConstants.FIND_ALL_PIS_BY_DATE, {params: {date: date}});
  }

  payPIBalance(purchaseInvoiceNo, amount) {
    return this.http.get(TradeConstants.PAY_PI_BALANCE, {
      params: {
        purchaseInvoiceNo: purchaseInvoiceNo,
        amount: amount
      }});
  }

  findAllByStatus(StatusId) {
    return this.http.get(TradeConstants.FIND_ALL_PIS_BY_STATUS_ID, {
      params: {statusId: StatusId}
    });
  }
}
