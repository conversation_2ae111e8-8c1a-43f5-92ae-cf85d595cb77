import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {TradeConstants} from "../trade-constants";

@Injectable({
  providedIn: 'root'
})

export class ExpenseService {

  constructor(private http: HttpClient) {
  }

  save(expense) {
    return this.http.post<any>(TradeConstants.SAVE_EXPENSE, expense);
  }

  findById(id: string){
    return this.http.get(TradeConstants.FIND_EXPENSE_BY_ID,{params:{id:id}})
  }

}
