import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {TradeConstants} from "../trade-constants";

@Injectable({
  providedIn: 'root'
})
export class CashierService {

  constructor(private http: HttpClient) {
  }

  findCashierByCounterNo(counterNo){
    return this.http.get(TradeConstants.FIND_CASHIER_BY_COUNTER, {params: {counterNo: counterNo},
      headers:{
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache'
        }});
  }
}
