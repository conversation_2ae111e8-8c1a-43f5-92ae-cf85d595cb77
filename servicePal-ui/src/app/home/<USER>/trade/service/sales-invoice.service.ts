import {Injectable} from '@angular/core';
import {TradeConstants} from '../trade-constants';
import {HttpClient} from "@angular/common/http";
import {SalesInvoice} from "../model/sales-invoice";


@Injectable({
  providedIn: 'root'
})
export class SalesInvoiceService {
  constructor(private http: HttpClient) {
  }

  public save(salesInvoice) {
    return this.http.post<any>(TradeConstants.SAVE_SALES_INVOICE, salesInvoice);
  }

  public findAll(page, pageSize) {
    return this.http.get(TradeConstants.GET_SALES_INVOICE, {params: {page: page, pageSize: pageSize}});
  }

  public findAllPendingPages(page, pageSize) {
    return this.http.get(TradeConstants.GET_PENDING_SALES_INVOICE,
      {params: {page: page, pageSize: pageSize}});
  }

  public findAllPendingSiForMonth() {
    return this.http.get(TradeConstants.GET_PENDING_SALES_INVOICE_THIS_MONTH);
  }

  public findAllPendingByRange(rangeId) {
    return this.http.get(TradeConstants.GET_PENDING_SALES_INVOICE_BY_RANGE, {
      params: {
        rangeId: rangeId
      }
    });
  }

  public findAllPendingBetween(sDate, eDate) {
    return this.http.get(TradeConstants.GET_PENDING_SALES_INVOICE_BETWEEN, {
      params: {sDate: sDate, eDate: eDate}
    });
  }

  public findByInvoiceNo(invNo: string) {
    return this.http.get<SalesInvoice>(TradeConstants.FIND_SI_BY_INV_NO, {params: {invNo: invNo}});
  }

  public findByCustomerNicBr(nicBr) {
    return this.http.get(TradeConstants.FIND_SI_BY_CUST_NIC_BR, {params: {nicBr: nicBr}});
  }

  public findByCustomerAndPending(nicBr) {
    return this.http.get(TradeConstants.FIND_SI_BY_CUST_NIC_BR_PENDING, {params: {nicBr: nicBr}});
  }

  public findBySaleType(invSaleType) {
    return this.http.get(TradeConstants.FIND_SI_BY_CUST_NIC_BR, {params: {saleType: invSaleType}});
  }

  public amendInvoice(invNo) {
    return this.http.get(TradeConstants.AMEND_SI, {params: {invNo: invNo}});
  }

  public findByJobNo(jobNo) {
    return this.http.get(TradeConstants.FIND_SI_BY_JOB_NO, {params: {jobNo: jobNo}});
  }

  public cancelSalesInvoice(salesInvoice: string, status: string) {
    return this.http.get<SalesInvoice[]>(TradeConstants.CANCEL_SALES_INVOICE, {
      params: {
        invoiceId: salesInvoice,
        statusId: status
      }
    });
  }

  public payBalance(siNo, amount) {
    return this.http.get(TradeConstants.PAY_SI_BALANCE, {params: {siNo: siNo, amount: amount}});
  }


}
