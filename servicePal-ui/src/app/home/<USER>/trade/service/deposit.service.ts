
import { Injectable } from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Cheque} from '../model/cheque';
import {Deposit} from '../model/deposit';
import {TradeConstants} from '../trade-constants';

@Injectable({
  providedIn: 'root'
})
export class DepositService {

public  deposit: Deposit;
  constructor(private http:HttpClient) {

  }

  save(deposit) {
    return this.http.post<any>(TradeConstants.SAVE_DEPOSIT, deposit);
  }

 public saveAsAngularObject(deposit){
   this.deposit = deposit;

   return  this.deposit;
  }

  public findByCheque(cheque) {
    return this.http.post<any>(TradeConstants.FIND_BY_CHEQUE, cheque);
  }
}
