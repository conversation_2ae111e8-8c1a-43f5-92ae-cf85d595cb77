import {Component, OnInit} from '@angular/core';
import {PermissionService} from '../../service/permission.service';
import {Permission} from "../../../admin/model/permission";

@Component({
  standalone: false,
  selector: 'app-all-permissions',
  templateUrl: './all-permissions.component.html',
  styleUrls: ['./all-permissions.component.css']
})
export class AllPermissionsComponent implements OnInit {

  user: any;
  permissions: Array<Permission> = [];
  permListMod: Array<any> = [];

  constructor(private permissionService: PermissionService) {
    this.user = JSON.parse(localStorage.getItem('currentUser')).user;
    this.findUserPermissions();
  }

  ngOnInit() {
  }

  findUserPermissions() {
    this.permissionService.findAvailablePermissions(this.user.username).subscribe((result: Array<Permission>) => {
      this.permissions = result;
      this.permissions.forEach(obj => {
        let duplicate = false;
        for (let idx in this.permListMod) {
          if (this.permListMod[idx].name == obj.module.name) {
            duplicate = true;
            this.permListMod[idx].perms.push({
              'name': obj.name, 'route': obj.route
            })
          }
        }
        if (!duplicate) {
          this.permListMod.push({
            'name': obj.module.name, 'perms': [{
              'name': obj.name, 'route': obj.route
            }]
          })
        }
      });
    })
  }

}
