import {Component, OnInit} from '@angular/core';
import {User} from '../../model/user';
import {Role} from '../../model/role';
import {Permission} from '../../model/permission';
import {Module} from '../../model/module';
import {UserService} from '../../service/user.service';
import {NotificationService} from '../../../core/service/notification.service';
import {RoleService} from '../../service/role.service';
import {PermissionService} from '../../service/permission.service';
import {Warehouse} from "../../../custom/inventory/model/warehouse";
import {WarehouseService} from "../../../custom/inventory/service/warehouse.service";
import {DialogService, DynamicDialogRef} from 'primeng/dynamicdialog';

@Component({
  standalone: false,
  selector: 'app-create-user',
  templateUrl: './create-user.component.html',
  styleUrls: ['./create-user.component.css'],
  providers: [DialogService]
})
export class CreateUserComponent implements OnInit {
  search: any;
  user = new User();
  userRole = new Role();
  userRoles: Array<Role> = [];
  warehouses: Array<Warehouse> = [];
  confirmPassword: string;
  selectedRow: number;
  setClickedRow: Function;
  ref: DynamicDialogRef;
  modules: Array<Module> = [];
  selectedModule: Module;
  selectedPermission: Permission;
  selectedPermissions: Array<Permission> = [];
  userAvailability: boolean;
  permissions: Array<Permission> = [];
  isFromModal: boolean;
  isPasswordMatch: boolean;
  isPermAdded: boolean;
  warehouse = new Warehouse();

  //only for display purposes
  displaySec = [];
  displayWh = [];

  userName: string;

  ngOnInit() {
    this.permissions = [];
    this.userAvailability = false;
    this.user = new User();
    this.user.userRoles = [];
    this.selectedPermission = new Permission();
    this.selectedPermissions = [];
    this.warehouses = [];
    this.selectedModule = new Module();
    this.user.desktopPermissions = [];
    this.user.active = true;
    this.isFromModal = false;
    this.isPasswordMatch = false;
    this.isPermAdded = false;
    this.findAllRole();
    this.getAllModulesForUser();
    this.getWarehouse();
  }

  constructor(
    private userService: UserService,
    private notificationService: NotificationService,
    private roleService: RoleService,
    private permService: PermissionService,
    public dialogService: DialogService,
    private warehouseService: WarehouseService
  ) {}
  }

  saveUser() {
    this.user.permissions = this.permissions;
    if (this.user.permissions.length === 0) {
      this.notificationService.showError('Add Permissions first');
    } else if (this.user.permissions.length !== 0) {
      this.userService.save(this.user).subscribe(result => {
        if (result === true) {
          this.notificationService.showSuccess("User Created");
          this.user.permissions.length = 0;
          this.clearForm();
          this.ngOnInit();
        } else {
          this.notificationService.showWarning("Creating User Failed");
        }
      });
    }
  }

  selectRole(item: Role) {
    let available = false;
    for (const i of this.user.userRoles) {
      if (i.id === item.id) {
        available = true;
      }
    }
    if (!available) {
      this.user.userRoles.push(item);
    }
  }

  findAllRole() {
    this.roleService.findAll().subscribe((data: Array<Role>) => {
      this.userRoles = data;
    });
  }

  clearForm() {
    this.user.userRoles = [];
    this.user.firstName = "";
    this.user.lastName = '';
    this.user.password = '';
    this.confirmPassword = '';
    this.ngOnInit();
  }

  getAllModulesForUser() {
    this.permService.getEnabledModules(this.user.username).subscribe((result: Array<Module>) => {
      this.modules = result;
    })
  }

  addPermissions(selectedPermission) {
    let available = false;
    for (const perm of this.permissions) {
      if (perm.id === selectedPermission.id) {
        available = true;
        this.notificationService.showWarning("This permission already added");
      }
    }
    if (!available) {
      this.permissions.push(this.selectedPermission);
      this.isPermAdded = true;
    }
  }

  findPermsForModule() {
    this.selectedPermissions = [];
    this.permService.findPermsByModule(this.selectedModule.id).subscribe((result: Array<Permission>) => {
      this.selectedPermissions = result;
    })
  }

  selectWarehouse() {
    this.user.warehouseCode = this.warehouse.code;
    this.displayWh = [this.warehouse];
  }

  getWarehouse() {
    this.warehouseService.findAll().subscribe((data: Array<Warehouse>) => {
      this.warehouses = data;
    })
  }

  /*removeFromUserRoles(event: Role | TagModelClass) {
    for (let i = 0; i < this.user.userRoles.length; i++) {
      if (this.user.userRoles[i].name === event.name) {
        this.user.userRoles.splice(i);
      }
    }
  }*/
  removeFromUserRoles(role: Role) {
    this.user.userRoles = this.user.userRoles.filter(t => t !== role);
  }

  checkUserName() {
    this.userService.checkBike(this.user.username).subscribe((res: boolean) => {
      this.userAvailability = res;
    });
  }

  checkPassword() {
    if (this.user.password === this.confirmPassword) {
      this.isPasswordMatch = true;
    }
  }

  loadUser() {
    this.confirmPassword = 'NOCHNG';
    this.isPasswordMatch = true;

    this.userService.findByName(this.userName).subscribe((user: User) => {
      this.user = user;
      this.user.password = 'NOCHNG';

      this.displayWh = [this.warehouse];
      this.permissions = this.user.permissions;
    })

    this.warehouseService.findByCode(this.user.warehouseCode).subscribe((wh: Warehouse) => {
      this.displayWh = [wh];
    })
  }


}

