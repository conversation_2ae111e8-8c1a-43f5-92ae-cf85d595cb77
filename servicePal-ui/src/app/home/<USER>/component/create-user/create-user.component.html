<div class="card">
  <div class="card-header">
    <strong>User </strong>
    <small>Registration</small>
  </div>
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-12">
        <form (ngSubmit)="saveUser();userForm.reset()" #userForm="ngForm">
          <div class="row g-3">
            <div class="col-md-6">
              <div class="mb-3">
                <label>User Role</label>
                <select class="form-select" name="userRole" [(ngModel)]="userRole" (change)="selectRole(userRole)">
                  <option *ngFor="let ur of userRoles" [ngValue]="ur">{{ur.name}}</option>
                </select>
                <!--<tag-input [(ngModel)]="user.userRoles"
                           [identifyBy]="'id'" [displayBy]="'name'"
                           name="userRoles" [hideForm]="true"
                           [placeholder]="'Select a Role'" (onRemove)="removeFromUserRoles($event)"></tag-input>-->
                <div class="tag-input">
                  <div *ngFor="let tag of user.userRoles" class="tag">
                    {{ tag.name }} <button (click)="removeFromUserRoles(tag)">x</button>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label>First Name</label>
                <input type="text" required #firstName="ngModel"
                       [class.is-invalid]="firstName.invalid && firstName.touched"
                       class="form-control" id="firstName"
                       placeholder="Enter First Name" name="firstName" [(ngModel)]="user.firstName">
                <small class="text-danger" [class.d-none]="firstName.valid || firstName.untouched">*First Name is
                  Required
                </small>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>Last Name</label>
                <input type="text" required #lastName="ngModel"
                       [class.is-invalid]="lastName.invalid && lastName.touched"
                       class="form-control" id="lastName"
                       placeholder="Enter Last Name" name="lastName" [(ngModel)]="user.lastName">
                <small class="text-danger" [class.d-none]="lastName.valid || lastName.untouched">*Last Name is Required
                </small>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>User Name</label>
                <input type="text" required #userName="ngModel"
                       [class.is-invalid]="userName.invalid && userName.touched"
                       class="form-control" id="userName" placeholder="Enter User Name" name="userName"
                       [(ngModel)]="user.username" (keyup)="checkUserName()">
                <small class="text-danger" [class.d-none]="userName.valid || userName.untouched">*Username is Required
                </small>
                <small *ngIf="userAvailability" [class.is-none]="true" class="text-danger">* This User Is Already Added
                </small>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>Email</label>
                <input type="text" required #email="ngModel" [class.is-invalid]="email.invalid && email.touched"
                       class="form-control" id="email" placeholder="Enter Email" name="email"
                       [(ngModel)]="user.email" pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$">
                <small class="text-danger" [class.d-none]="email.valid || email.untouched">*Email is Required</small>
              </div>
            </div>

            <div class="col-md-6 mb-3">
              <label>Warehouse</label>
              <select class="form-select"
                      #warehouseSelect="ngModel"
                      name="warehouseSelect"
                      [(ngModel)]="warehouse"
                      (change)="selectWarehouse()"
                      [class.is-invalid]="warehouseSelect.invalid && warehouseSelect.touched">
                <option [value]="undefined" disabled>-Select-</option>
                <option *ngFor="let warehouse of warehouses" [ngValue]="warehouse">{{warehouse.name}}</option>
              </select>
              <tag-input [(ngModel)]="displayWh"
                         [identifyBy]="'id'" [displayBy]="'name'" [removable]='false'
                         name="whTag" [hideForm]="true"></tag-input>
            </div>
          </div>
          <div class="row g-3 mt-3">
            <div class="col-md-6">
              <div class="form-group">
                <label>Password</label>
                <input type="password" #password="ngModel" [class.is-invalid]="password.invalid && password.touched"
                       class="form-control" id="password" placeholder="Enter Password" name="password"
                       [(ngModel)]="user.password" required>
                <small class="text-danger" [class.d-none]="password.valid || password.untouched">
                  *Password required
                </small>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>Confirm Password</label>
                <input type="password" class="form-control" id="confirmPassword" placeholder="Re Enter Password"
                       name="confirmPassword"
                       [(ngModel)]="confirmPassword" required (ngModelChange)="checkPassword()">
                <small class="text-danger" [class.d-none]="user.password === confirmPassword">*Password does not match
                </small>
              </div>
            </div>
          </div>

          <div class="row g-3">
            <div class="col-md-6">
              <div class="form-group">
                <label>Modules</label>
                <select class="form-control" name="moduleList" [(ngModel)]="selectedModule"
                        (change)="findPermsForModule()">
                  <option *ngFor="let mod of modules" [ngValue]="mod">{{mod.name}}</option>
                </select>
              </div>
            </div>
            <div class="col-md-5">
              <div class="form-group">
                <label>Permissions</label>
                <select class="form-control" name="moduleList" [(ngModel)]="selectedPermission">
                  <option *ngFor="let perm of selectedPermissions" [ngValue]="perm">{{perm.name}}</option>
                </select>
              </div>
            </div>
            <div class="col-md-1 text-end">
              <button type="button" class="btn btn-success btn-md mt-4"
                      (click)="addPermissions(selectedPermission)">Add
              </button>
            </div>
          </div>

          <div class="col-md-12" required>
            <tag-input [(ngModel)]="permissions"
                       [identifyBy]="'id'" [displayBy]="'name'"
                       name="modules" [hideForm]="true"
                       [placeholder]="'Select a Module'"></tag-input>
          </div>
          <div class="col-md-9 col-form-label mt-2">
            <div class="form-check">
              <input name="active" class="form-check-input"
                     [(ngModel)]="user.active" id="active" type="checkbox">
              <label class="form-check-label" for="active">Active</label>
            </div>
          </div>
          <div class="row g-3">
            <div class="col-md-12 text-end">
              <button type="submit" class="btn btn-success btn-md me-2"
                      [disabled]="!userForm.form.valid || !isPasswordMatch">
                Save
              </button>
              <button type="button" class="btn btn-warning btn-md text-end me-2"
                      (click)="clearForm()">Clear
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

