import {Component, OnInit, ViewChild} from '@angular/core';
import {DomSanitizer} from "@angular/platform-browser";
import {Company} from "../../../core/model/company";
import {CompanyService} from "../../../core/service/company.service";
import {NotificationService} from "../../../core/service/notification.service";
import {PrintSetting} from "../../model/print-setting";
import {PrintSettingService} from "../../service/print-setting.service";

@Component({
  standalone: false,
  selector: 'app-company',
  templateUrl: './print-setting.component.html'
})

export class PrintSettingComponent implements OnInit {

  printSetting: PrintSetting;

  constructor(private printSettingService: PrintSettingService, private notificationService: NotificationService) {
  }

  ngOnInit() {
    this.printSetting = new PrintSetting();
    this.findPrintSetting();
  }

  savePrintSettings() {
    this.printSettingService.save(this.printSetting).subscribe(result => {
      if (result === 'success') {
        this.notificationService.showSuccess(result);
        this.findPrintSetting();
      }
    });
  }

  findPrintSetting() {
    this.printSettingService.findAll().subscribe((data: PrintSetting) => {
      if (data != null) {
        this.printSetting = data;
      }
    });
  }

}

