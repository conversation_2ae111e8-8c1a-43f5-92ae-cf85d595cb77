<div class="card">
  <div class="card-header">
    <div class="row g-3">
      <div class="col-md-6">
        <label><b>Print Settings</b></label>
      </div>
    </div>
  </div>

  <div class="card-body">

    <form #settingsForm=ngForm (ngSubmit)="savePrintSettings();settingsForm.reset()">
      <div class="row g-3">
        <div class="mb-3  col-md-6">
          <label>Sales Invoice Footer</label>
          <input #salesInvoice="ngModel" type="text" name="company_name" id="company_name"
                 [class.is-invalid]="salesInvoice.invalid && salesInvoice.touched"
                 [(ngModel)]="printSetting.salesInvoiceFooter"
                 class="form-control" placeholder="Enter Sales Invoice Footer">
        </div>
        <div class="mb-3  col-md-6">
          <label>Job Card Footer</label>
          <input #jobCard="ngModel" type="text" name="jobCardFooter" id="jobCardFooter"
                 [class.is-invalid]="jobCard.invalid && jobCard.touched"
                 [(ngModel)]="printSetting.jobCardFooter"
                 class="form-control" placeholder="Enter Job Card Footer">
        </div>

        <div class="mb-3  col-md-6">
          <label>Customer Job Receipt Footer</label>
          <input #customerJobReceipt="ngModel" type="text" name="customerJobReceiptFooter"
                 id="customerJobReceiptFooter"
                 [class.is-invalid]="customerJobReceipt.invalid && customerJobReceipt.touched"
                 [(ngModel)]="printSetting.customerJobReceiptFooter"
                 class="form-control" placeholder="Enter Customer Job Receipt Footer">
        </div>

        <div class="mb-3  col-md-6">
          <label>Estimate Footer</label>
          <input #estimate="ngModel" type="text" name="estimateFooter" id="estimateFooter"
                 [class.is-invalid]="estimate.invalid && estimate.touched"
                 [(ngModel)]="printSetting.estimateFooter"
                 class="form-control" placeholder="Enter Estimate Footer">
        </div>
      </div>
      <div class="row g-3">
        <div class="mb-3 col-md-12">
          <button type="submit" [disabled]="!settingsForm.form.valid" class="btn  btn-success me-1 float-end"><i
            class="fa fa-dot-circle-o"></i> Save
          </button>
        </div>
      </div>
    </form>

  </div>
</div>

