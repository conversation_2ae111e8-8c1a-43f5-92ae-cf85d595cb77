import {Component, OnInit} from '@angular/core';
import {User} from '../../model/user';
import {Role} from '../../model/role';
import {NotificationService} from '../../../core/service/notification.service';
import {UserService} from '../../service/user.service';
import {RoleService} from '../../service/role.service';
import {Permission} from '../../model/permission';
import {PermissionService} from '../../service/permission.service';
import {Module} from '../../model/module';
import {CreateUserComponent} from "../create-user/create-user.component";
import {DialogService, DynamicDialogRef} from 'primeng/dynamicdialog';

@Component({
  standalone: false,
  selector: 'app-user',
  templateUrl: './user-management.component.html',
  styleUrls: ['./user-management.component.css'],
  providers: [DialogService]
})
export class UserManagementComponent implements OnInit {

  search: any;
  user = new User();
  users: Array<User> = [];
  userRoles: Array<Role> = [];
  confirmPassword: string;
  selectedRow: number;
  setClickedRow: Function;
  ref: DynamicDialogRef;
  modules: Array<Module> = [];
  selectedUser: User;
  isUserSelected: boolean;

  ngOnInit() {
    this.user = new User();
    this.selectedUser = new User();
    this.user.userRoles = [];
    this.isUserSelected = false;
    this.findAllUsers();
    this.findAllRole();
    this.getEnabledModulesForUser();
  }

  constructor(private userService: UserService, private notificationService: NotificationService,
              private roleService: RoleService, private permService: PermissionService,
              public dialogService: DialogService) {}
  }

  getEnabledModulesForUser() {
    this.permService.getEnabledModules(this.user.username).subscribe((result: Array<Module>) => {
      this.modules = result;
    });
  }

  findAllUsers() {
    this.userService.findAll().subscribe((data: Array<User>) => {
      this.users = data;
    });
  }

  findAllRole() {
    this.roleService.findAll().subscribe((data: Array<Role>) => {
      this.userRoles = data;
    });
  }

  selectRole(item: Role) {
    let available = false;
    for (const i of this.user.userRoles) {
      if (i.id === item.id) {
        available = true;
      }
    }
    if (!available) {
      this.user.userRoles.push(item);
    }
  }

  userDetail(selectedItem: User, index) {
    this.selectedRow = index;
    this.selectedUser = selectedItem;
    this.isUserSelected = true;
    this.user.password = 'NOCHNG';
    this.confirmPassword = 'NOCHNG';
  }

  searchUser() {
    this.users = [];
    this.userService.searchByName(this.search).subscribe((data: User) => {
      this.users.push(data);
    });
  }

  editUser() {
    this.ref = this.dialogService.open(CreateUserComponent, {
      data: {
        user: this.selectedUser,
        isEdit: true,
        password: 'NOCHNG',
        confirmPassword: 'NOCHNG',
        permissions: this.selectedUser.permissions
      },
      header: 'Edit User',
      width: '70%',
      contentStyle: {"max-height": "500px", "overflow": "auto"},
      baseZIndex: 10000
    });

    this.ref.onClose.subscribe(() => {
      this.ngOnInit();
    });
  }
}

