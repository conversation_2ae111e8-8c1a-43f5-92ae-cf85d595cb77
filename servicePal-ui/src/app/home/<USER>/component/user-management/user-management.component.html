<div class="card">
  <div class="card-header">
    <strong>Manage User </strong>
    <small>Registration</small>
  </div>
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-12">
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-6">
              <input type="text" [(ngModel)]="search" class="form-control " id="search" placeholder="Enter username"
                     name="search">
            </div>
            <div class="col-md-1">
              <button type="button" class="btn btn-primary " (click)="searchUser()">Search</button>
            </div>
          </div>
          <br>
          <div class="col-md-12 m-0 p-0">
            <table class="table  table-striped table-bordered">
              <thead>
              <tr>
                <th>Full Name</th>
                <th>User Name</th>
                <th>Email</th>
                <th>User Role</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let user of users ,let i = index" (click)="userDetail(user,i)"
                  [class.active]="i === selectedRow">
                <td>{{user.firstName + ' ' + user.lastName}}</td>
                <td>{{user.username}}</td>
                <td>{{user.email}}</td>
                <td><p class="p-0 m-0" *ngFor="let userRole of user.userRoles">{{userRole.name}}</p></td>
              </tr>
              </tbody>
            </table>
            <div class="float-end">
              <button class="btn btn-danger" (click)="editUser()" [disabled]="!isUserSelected"> Edit
                User
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
