<div class="card">
  <div class="card-header">
    <div class="row g-3">
      <div class="col-md-6">
        <label><b>Company Detail</b></label>
      </div>
    </div>
  </div>

  <div class="card-body">
    <form #companyForm=ngForm (ngSubmit)="saveCompany();companyForm.reset()">
      <div class="row g-3">
        <div class="mb-3  col-md-6">
          <label>Company Name</label>
          <input required #company_name="ngModel" type="text" name="company_name" id="company_name"
                 [class.is-invalid]="company_name.invalid && company_name.touched" [(ngModel)]="company.name"
                 class="form-control" placeholder="Enter Company Name">
          <small class="text-danger" [class.d-none]="company_name.valid || company_name.untouched">*Company Name is
            required
          </small>
        </div>

        <div class="mb-3 col-md-6">
          <label>Company Slogan (Optional)</label>
          <input type="text" name="company_slogan" id="company_slogan"
                 [(ngModel)]="company.slogan" class="form-control" placeholder="Enter Company Slogan">
        </div>
      </div>

      <div class="row g-3">
        <div class="mb-3 col-md-4">
          <label>Contact 1</label>
          <input required #company_contact_1="ngModel" type="text" name="company_contact_1" id="company_contact_1"
                 [class.is-invalid]="company_contact_1.invalid && company_contact_1.touched"
                 [(ngModel)]="company.telephone1" class="form-control" pattern="^\d{10}$"
                 placeholder="Enter Contact 1">
          <small class="text-danger" [class.d-none]="company_contact_1.valid || company_contact_1.untouched">*Contact 1
            is Required</small>
        </div>

        <div class="mb-3 col-md-4">
          <label>Contact 2</label>
          <input type="text" name="company_contact_2" #company_contact_2="ngModel"
                 id="company_contact_2"
                 [(ngModel)]="company.telephone2" class="form-control" pattern="^\d{10}$"
                 placeholder="Enter Contact 2">
        </div>
        <div class="mb-3 col-md-4">
          <label>Contact 3</label>
          <input type="text" name="company_contact_3" #company_contact_3="ngModel"
                 id="company_contact_3"
                 [(ngModel)]="company.telephone3" class="form-control" pattern="^\d{10}$"
                 placeholder="Enter Contact 3">
        </div>
      </div>

      <div class="row g-3">
        <div class="col-md-12">
          <label>Address</label>
          <div class="form-group">
            <input type="text" #address2="ngModel" class="form-control" id="address2"
                   name="address2" placeholder="Enter Address line 2"
                   [class.is-invalid]="address2.invalid && address2.touched"
                   [(ngModel)]="company.fullAddress">
            <small class="text-danger" [class.d-none]="address2.valid || address2.untouched">*Address is
              required
            </small>
          </div>
        </div>
      </div>

      <div class="row g-3">
        <div class="mb-3 col-md-6">
          <label>Company Email</label>
          <input #company_email="ngModel" type="text" name="company_email" id="company_email"
                 pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$"
                 [class.is-invalid]="company_email.invalid && company_email.touched" [(ngModel)]="company.email"
                 class="form-control" placeholder="Enter Company Email">
          <small class="text-danger" [class.d-none]="company_email.valid || company_email.untouched">*Company Email is
            required
          </small>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label>Company Register No</label>
            <input #regNo="ngModel" type="text" class="form-control" id="regNo"
                   name="regNo" placeholder="Enter Register No"
                   [class.is-invalid]="regNo.invalid && regNo.touched"
                   [(ngModel)]="company.regNo">
            <small class="text-danger" [class.d-none]="regNo.valid || regNo.untouched">*Register no is
              required
            </small>
          </div>
        </div>
      </div>

      <div class="row g-3">
        <div class="mb-3 col-md-6">
          <label>Company Logo</label>
          <div class="mb-3 col-md-12">
            <input class="form-control-file" type="file" #logo placeholder="upload your image"
                   (change)="getFiles($event)"
                   accept="image/*" multiple>
            <img [(src)]="imageFile" width="250px" height="250px">
          </div>
        </div>
      </div>

      <div class="row g-3">
        <div class="mb-3 col-md-12">
          <button type="submit" [disabled]="!companyForm.form.valid" class="btn  btn-success me-1 float-end"><i
            class="fa fa-dot-circle-o"></i> Save
          </button>
        </div>
      </div>
    </form>

  </div>
</div>

