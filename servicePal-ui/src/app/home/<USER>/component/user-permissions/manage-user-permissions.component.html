<div class="card">
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-4 form-group">
        <label for="enableModules">Enabled Modules</label>
        <select class="form-control" id="enableModules" name="module" [(ngModel)]= "module"
                (change)="getPermsForModule(module)">
          <option></option>
          <option *ngFor="let module of modules" [ngValue]="module">{{module.name}}</option>
        </select>
      </div>
      <div class="col-md-4 form-group">
        <label for="addedPerms">Enabled Permissions</label>
        <select class="form-control" id="addedPerms" [(ngModel)]="selectedPerm" >
          <option></option>
          <option *ngFor="let perm of availablePerms" [ngValue]="perm">{{perm.name}}</option>
        </select>
      </div>
      <div class="col-md-4 form-group">
        <label>&nbsp;</label>
        <button class="btn btn-success float-end form-control" (click)="addToDesktop(selectedPerm)"> Add
        </button>
      </div>
    </div>
    <div class="row g-3">
      <div class="col-md-12">
        <h5>Current Desktop Permissions</h5>
        <tag-input [(ngModel)]='desktopPerms' [identifyBy]="'id'" [displayBy]="'name'" theme='dark'
                   [editable]='false' [allowDupes]='false'></tag-input>
      </div>
    </div>
    <div class="row g-3 mt-3">
      <div class="col-md-12">
        <button class="btn btn-success float-end" (click)="close()">Done</button>
      </div>
    </div>
  </div>
</div>
