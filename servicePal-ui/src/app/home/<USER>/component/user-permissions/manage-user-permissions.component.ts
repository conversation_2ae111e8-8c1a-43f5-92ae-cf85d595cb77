import {Component, EventEmitter} from '@angular/core';
import {NotificationService} from '../../../core/service/notification.service';
import {PermissionService} from '../../service/permission.service';
import {Module} from '../../model/module';
import {Permission} from '../../model/permission';
import {User} from "../../model/user";
import {DynamicDialogConfig, DynamicDialogRef} from 'primeng/dynamicdialog';

@Component({
  standalone: false,
  selector: 'app-manage-user-permissions',
  templateUrl: './manage-user-permissions.component.html',
  styleUrls: ['./manage-user-permissions.component.css']
})
export class ManageUserPermissionsComponent {

  modules: Array<Module> = [];
  availablePerms: Array<Permission> = [];
  desktopPerms: Array<Permission> = [];
  selectedPerm: Permission = new Permission();
  module = new Module();
  user: User;

  public event: EventEmitter<any> = new EventEmitter();

  constructor(
    private permService: PermissionService,
    private notificationService: NotificationService,
    public ref: DynamicDialogRef,
    public config: DynamicDialogConfig
  ) {}

  ngOnInit() {
    this.user = this.config.data?.user || JSON.parse(localStorage.getItem('currentUser'));
    this.modules = this.config.data?.modules || [];
    this.desktopPerms = this.config.data?.desktopPerms || [];
  }

  getAllModulesForUser() {
    this.permService.getEnabledModules(this.user.username).subscribe((result: Array<Module>) => {
      this.modules = result;
    })
  }

  getPermsForModule(module: Module) {
    this.permService.findPermsByModule(this.module.id).subscribe((result: Array<Permission>) => {
      this.availablePerms = result;
    })
  }

  addToDesktop(perm) {
    if (null != perm.id) {
      this.desktopPerms.push(perm);
    } else {
      this.notificationService.showError('Please Select a Permission')
    }
  }

  close() {
    this.ref.close(this.desktopPerms);
  }

}
