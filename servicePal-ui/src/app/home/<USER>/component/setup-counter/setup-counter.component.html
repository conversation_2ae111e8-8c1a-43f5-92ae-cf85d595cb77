<div class="card">
  <div class="card-header">
    Setup counter To User
  </div>
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-6">
        <label>User</label>
          <select class="form-select" (change)="setSelectedUser($event)" name="userSelect"
                  [(ngModel)]="userId" required #userSelect="ngModel"
                  [class.is-invalid]="userSelect.invalid && userSelect.touched">
            <option [value]="undefined">-Select User-</option>
            <option *ngFor="let user of users" [value]="user.id">
              {{user.username}}
            </option>
          </select>
      </div>
      <div class="col-md-6">
        <label>Counter</label>
          <select class="form-control" (change)="setSelectedCounter($event)" name="counterSelect"
                  [(ngModel)]="counterId" required #counterSelect="ngModel"
                  [class.is-invalid]="counterSelect.invalid && counterSelect.touched">
            <option [value]="undefined">-Select Counter-</option>
            <option *ngFor="let counter of counters" [value]="counter.id">
              {{counter.counterNo}}
            </option>
          </select>
      </div>
    </div>

    <div class="row g-3 mt-3">
      <div class="col-md-12">
        <button type="button" class="btn btn-customize float-end ms-2" (click)="setupCounter()" [disabled]="!isCounterSelected">
          save
        </button>
      </div>
    </div>

  </div>
</div>
