import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {P404Component, P500Component} from './error';
import {LoginComponent} from './home/<USER>/login/login.component';
import {HomeComponent} from './home/<USER>';
import {AuthGuard} from './guard/auth.guard';
import {HeaderComponent} from './home/<USER>/layout/header/header.component';
import {FooterComponent} from './home/<USER>/layout/footer/footer.component';
import {SideBarComponent} from './home/<USER>/layout/side-bar/side-bar.component';
import {P401Component} from './error/401.component';
import {StarterComponent} from './home/<USER>/starter/starter.component';

// Need to Move these to Core Module
import {AllPermissionsComponent} from './home/<USER>/component/all-permissions/all-permissions.component';
import {UserPermissionsComponent} from './home/<USER>/component/user-permissions/user-permissions.component';
import {DesktopManagerComponent} from './home/<USER>/component/desktop-manager/desktop-manager.component';

export const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'home/starter'
  },
  {
    path: '404',
    component: P404Component
  },
  {
    path: '500',
    component: P500Component
  },
  {
    path: '401',
    component: P401Component
  },
  {
    path: 'login',
    component: LoginComponent
  },
  {
    path: 'home',
    component: HomeComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: 'starter',
        component: StarterComponent,
      },
      {
        path: 'all_availablePerms',
        component: AllPermissionsComponent,
      },
      {
        path: 'manage_desktop',
        component: DesktopManagerComponent,
      },
      {
        path: 'user_perms',
        component: UserPermissionsComponent,
      },
      {
        path: 'admin',
        loadChildren: () => import('./home/<USER>/admin.module').then(a => a.AdminModule)
      },
      {
        path: 'inventory',
        loadChildren: () => import('./home/<USER>/inventory/inventory.module').then(m => m.InventoryModule)
      },
      {
        path: 'hr',
        loadChildren: () => import('./home/<USER>/hr/hr.module').then(h => h.HrModule)
      },
      {
        path: 'job',
        loadChildren: () => import('./home/<USER>/business/business.module').then(b => b.BusinessModule)
      },
      {
        path: 'customer',
        loadChildren: () => import('./home/<USER>/business/business.module').then(b => b.BusinessModule)
      },
      {
        path: 'machine',
        loadChildren: () => import('./home/<USER>/business/business.module').then(b => b.BusinessModule)
      },
      {
        path: 'trade',
        loadChildren: () => import('./home/<USER>/trade/trade.module').then(b => b.TradeModule)
      },
      {
        path: 'report',
        loadChildren: () => import('./home/<USER>/report/report.module').then(b => b.ReportModule)
      }
    ]
  }];

export const routeParams = [HeaderComponent, FooterComponent, SideBarComponent, P401Component, P404Component, P500Component,
  HomeComponent, UserPermissionsComponent, DesktopManagerComponent, LoginComponent, StarterComponent, AllPermissionsComponent];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})

export class AppRoutingModule {
}

