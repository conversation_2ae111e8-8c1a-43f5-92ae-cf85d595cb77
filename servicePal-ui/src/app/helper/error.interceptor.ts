import {Injectable} from '@angular/core';
import {HttpRequest, <PERSON>ttpHandler, HttpEvent, HttpInterceptor} from '@angular/common/http';
import {Observable, throwError} from 'rxjs';
import {catchError} from 'rxjs/operators';
import {AuthenticationService} from '../home/<USER>/login/authentication.service';
import {Router} from '@angular/router';

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  constructor(private authenticationService: AuthenticationService, private router: Router) {
  }

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(catchError(err => {
      if (err.status === 404) {
        this.router.navigateByUrl('/404');
      }
      if (err.status === 500) {
        this.router.navigateByUrl('/500');
      }
      if (err.status === 401) {
        this.authenticationService.logout();
        this.router.navigateByUrl('/401');
      }
      if (err.status === 0) {
        this.authenticationService.logout();
        this.router.navigateByUrl('/login');
      }
      const error = err.error.message || err.statusText;
      return throwError(error);
    }))
  }
}
