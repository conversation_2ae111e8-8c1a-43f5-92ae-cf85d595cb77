/* You can add global styles to this file, and also import other style files */

/* Multi-tenant CSS variables */
:root {
  --tenant-primary-color: #590c30;
  --tenant-primary-rgb: 89, 12, 48;
}

.theme-color {
  color: var(--tenant-primary-color);
}

.theme-color-bg {
  background-color: var(--tenant-primary-color);
}

.theme-color-border {
  border: var(--tenant-primary-color) solid 2px;
}

.cursor-pointer {
  cursor: pointer;
}

.border-right {
  border-right: var(--tenant-primary-color) solid 1px;
}

.table tr.active td {
  background-color: #46dad3 !important;
  color: white;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.ng-valid[required], .ng-valid.required  {
  border-left: 5px solid #42A948; /* green */
}

.ng-invalid:not(form).form-control, .ng-invalid:not(form).form-select {
  border-left: 5px solid #a94442; /* red */
}

.form-control, .form-select {
  border: 1px solid #adb3b9; /* Bootstrap default */
  outline: none;
}