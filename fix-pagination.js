const fs = require('fs');
const path = require('path');

function fixPaginationMethods(content) {
  let modified = false;
  
  // Fix pageChanged method to handle PrimeNG paginator events
  const pageChangedPattern = /pageChanged\s*\(\s*event\s*:\s*any\s*\)\s*{([^}]*)}/g;
  
  content = content.replace(pageChangedPattern, (match, methodBody) => {
    // Check if already converted
    if (methodBody.includes('event.page + 1') || methodBody.includes('event.first')) {
      return match;
    }
    
    // Convert ngx-bootstrap pagination to PrimeNG
    if (methodBody.includes('event.page')) {
      const newMethodBody = methodBody.replace(/event\.page/g, 'event.page + 1');
      modified = true;
      return `pageChanged(event: any) {${newMethodBody}}`;
    }
    
    return match;
  });
  
  // Add onPageChange method if pageChanged exists but onPageChange doesn't
  if (content.includes('pageChanged(') && !content.includes('onPageChange(')) {
    const pageChangedMethodPattern = /(pageChanged\s*\([^)]*\)\s*{[^}]*})/;
    const match = content.match(pageChangedMethodPattern);
    
    if (match) {
      const onPageChangeMethod = `
  onPageChange(event: any) {
    const page = event.page + 1; // PrimeNG paginator is 0-indexed
    this.page = page;
    // Call your existing pagination method
    this.pageChanged({ page: page });
  }
`;
      
      content = content.replace(match[0], match[0] + onPageChangeMethod);
      modified = true;
    }
  }
  
  // Add firstRecordIndex property if pagination exists
  if (content.includes('pageChanged') && !content.includes('firstRecordIndex')) {
    const classPattern = /export\s+class\s+\w+.*?{/;
    const classMatch = content.match(classPattern);
    
    if (classMatch) {
      const paginationProperties = `
  firstRecordIndex: number = 0;
  itemsPerPage: number = 10;
`;
      
      const insertIndex = content.indexOf('{', classMatch.index) + 1;
      content = content.slice(0, insertIndex) + paginationProperties + content.slice(insertIndex);
      modified = true;
    }
  }
  
  return { content, modified };
}

function processFile(filePath) {
  try {
    const originalContent = fs.readFileSync(filePath, 'utf8');
    
    // Only process files that have pagination
    if (!originalContent.includes('pageChanged') && !originalContent.includes('pagination')) {
      return;
    }
    
    const { content, modified } = fixPaginationMethods(originalContent);
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Updated pagination in: ${filePath}`);
    }
    
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

function findTypeScriptFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && item.endsWith('.component.ts')) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Main execution
console.log('🔧 Fixing pagination methods for PrimeNG...');

const srcDir = path.join(__dirname, 'servicePal-ui', 'src');
const tsFiles = findTypeScriptFiles(srcDir);

console.log(`Checking ${tsFiles.length} TypeScript component files for pagination...`);

tsFiles.forEach(processFile);

console.log('✅ Pagination fixes completed!');
