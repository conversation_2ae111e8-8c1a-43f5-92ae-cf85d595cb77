const fs = require('fs');
const path = require('path');

// Methods that need to be updated for PrimeNG AutoComplete
const methodsToFix = [
  'searchEmployee',
  'searchEmployeeForAssign', 
  'loadBrands',
  'loadBrandsByCode',
  'loadSuppliers',
  'searchSuppliers',
  'loadItemByCode',
  'searchItems',
  'loadWarehouses',
  'loadExpenseTypes',
  'searchService',
  'loadServices',
  'loadCustomers',
  'searchCustomers',
  'loadVehicles',
  'searchVehicles',
  'loadEmployees',
  'findEmployees',
  'loadCategories',
  'searchCategories'
];

function fixAutoCompleteMethod(content, methodName) {
  // Pattern to match method definition
  const methodPattern = new RegExp(`(\\s+${methodName}\\s*\\(\\s*)\\)\\s*{`, 'g');
  
  // Replace method signature to include optional event parameter
  content = content.replace(methodPattern, `$1event?: any) {`);
  
  // Pattern to find the method body and add query extraction
  const methodBodyPattern = new RegExp(
    `(\\s+${methodName}\\s*\\(\\s*event\\?:\\s*any\\)\\s*{\\s*)(.*?)(\\.subscribe)`,
    'gs'
  );
  
  content = content.replace(methodBodyPattern, (match, methodStart, methodBody, subscribe) => {
    // Check if query extraction already exists
    if (methodBody.includes('const query = event ? event.query')) {
      return match; // Already fixed
    }
    
    // Find the variable being used in the service call
    const serviceCallMatch = methodBody.match(/this\.\w+\.\w+\(([^)]+)\)/);
    if (serviceCallMatch) {
      const originalParam = serviceCallMatch[1].trim();
      
      // Add query extraction logic
      const queryExtraction = `\n    const query = event ? event.query : ${originalParam};\n    `;
      
      // Replace the original parameter with 'query'
      const updatedMethodBody = methodBody.replace(originalParam, 'query');
      
      return methodStart + queryExtraction + updatedMethodBody + subscribe;
    }
    
    return match;
  });
  
  return content;
}

function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Check if file contains any of the methods we need to fix
    for (const methodName of methodsToFix) {
      const methodExists = content.includes(`${methodName}(`);
      if (methodExists) {
        const originalContent = content;
        content = fixAutoCompleteMethod(content, methodName);
        if (content !== originalContent) {
          modified = true;
          console.log(`Fixed method ${methodName} in ${filePath}`);
        }
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Updated: ${filePath}`);
    }
    
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

function findTypeScriptFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && item.endsWith('.component.ts')) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Main execution
console.log('🔧 Fixing AutoComplete method signatures for PrimeNG...');

const srcDir = path.join(__dirname, 'servicePal-ui', 'src');
const tsFiles = findTypeScriptFiles(srcDir);

console.log(`Found ${tsFiles.length} TypeScript component files`);

tsFiles.forEach(processFile);

console.log('✅ AutoComplete method signature fixes completed!');
