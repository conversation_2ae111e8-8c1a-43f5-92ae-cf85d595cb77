package lk.sout.servicePal.inventory.repository;

import lk.sout.servicePal.inventory.entity.ItemCategory;
import lk.sout.servicePal.inventory.entity.SubCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface ItemSubCategoryRepository extends MongoRepository<SubCategory, String> {

    List<SubCategory> findSubCategoryBySubCategoryNameLikeIgnoreCase(String any);

    List<SubCategory> findSubCategoryByItemCategoryAndSubCategoryNameLikeIgnoreCaseAndActive(
            ItemCategory itemCategory, String subCategoryName, boolean b);

    Page<SubCategory> findAll(Pageable pageable);
}
