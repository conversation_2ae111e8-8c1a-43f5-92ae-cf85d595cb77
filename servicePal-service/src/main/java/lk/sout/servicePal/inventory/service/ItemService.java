package lk.sout.servicePal.inventory.service;

import lk.sout.core.entity.Response;
import lk.sout.servicePal.inventory.entity.Brand;
import lk.sout.servicePal.inventory.entity.Item;
import lk.sout.servicePal.inventory.entity.ItemCategory;
import lk.sout.servicePal.trade.entity.PurchaseInvoiceRecord;
import lk.sout.servicePal.trade.entity.SalesInvoice;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ItemService {

    Response save(Item item);

    boolean remove(String id);

    Iterable<Item> findAll(Pageable pageable);

    List<Item> getReorderList();

    Iterable<Item> findAllStock(Pageable pageable);

    Item findOne(String id);

    List<Item> findAllByBarcodeLike(String code);

    List<Item> findByCategory(ItemCategory category);

    List<Item> findByBrand(Brand brand);

    List<Item> findActiveByNameLike(String name);

    List<Item> findActiveServiceByNameLike(String name);

    List<Item> findActiveServiceByCodeLike(String name);

    List<Item> findActiveTransportByNameLike(String name);

    List<Item> findActiveTransportByCodeLike(String name);

    List<Item> findAllByNameLike(String name);

    List<Item> findByAny(String name);

    Item findOneByItemCode(String s);

    Item findOneByBarcode(String s);

}
