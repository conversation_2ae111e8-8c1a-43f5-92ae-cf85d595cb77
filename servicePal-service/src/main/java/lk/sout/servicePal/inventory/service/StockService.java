package lk.sout.servicePal.inventory.service;

import lk.sout.core.entity.Response;
import lk.sout.servicePal.inventory.entity.*;
import lk.sout.servicePal.trade.entity.PurchaseInvoiceRecord;
import lk.sout.servicePal.trade.entity.SalesInvoice;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by Mad<PERSON>a Weerasinghe on 2/4/2020
 */

public interface StockService {

    Response save(Stock stock, String type, String itemCode);

    boolean basicSave(Stock stock);

    Response createByPurchaseInvRecs(List<PurchaseInvoiceRecord> piRecords, boolean manual);

    Iterable<Stock> findAllByWarehouse(Pageable pageable, int warehouseCode);

    Stock findOneByBarcodeAndWarehouse(String barcode, int warehouseCode);

    List<Stock> findByUserWarehouseAndBarcodeLike(String barcode);

    List<Stock> findByUserWarehouseAndBarcodeLike(int warehouseCode, String barcode);

    List<Stock> findByUserWarehouseAndItemNameLike(String itemName);

    List<Stock> findByUserWarehouseAndItemNameLike(int warehouseCode, String itemName);

    List<Stock> findByItemCode(String itemCode);

    Stock findByBarcodeAndUserWarehouse(String barcode);

    Stock findByItemCodeAndWarehouse(String itemCode, int warehouseCode);

    boolean deductFromStock(SalesInvoice salesInvoice, int warehouseCode);

    boolean deductFromStock(String itemCode, int warehouseCode, double quantity);

    Response adjust(String stockId, Double actualValue, String remark);

    Response transferStock(TransferStock transferStock);

    Response batchStockTransfer(BatchStockTransfer batchStockTransfer);

    List<Stock> getDeadStockList();

    Page<Stock> findAllStocks(Pageable pageable);

    List<Stock> findAllByBarcodeLike(String code);

    List<Stock> findAllByNameLike(String name);

    List<Stock> findAllByItemCategory(String id);

    List<StockSummary> findStockSummary();

    List<Stock> findReorderListByWarehouse(int warehouseCode, Double threshold);

    List<Stock> findReorderList(Double threshold, String catCode, String brandCode);

    boolean topUpStock(String itemCode, int warehouseCode, Double qty, boolean b);

    boolean addStockMovement(String type, String itemCode, Stock stock);

    List<StockMovement> findStockMovementByItemAndDateBetween(String barcode, int whCode,
                                                              LocalDate fromDate, LocalDate toDate);

    List<Stock> findStockByItemCategoryAndWh(String id, int whCode);

    List<Stock> findStockByBrandAndWh(String id, int whCode);

    Response bulkUpdateStocks(List<Stock> stocks);

}
