package lk.sout.servicePal.inventory.repository;


import lk.sout.servicePal.inventory.entity.Warehouse;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasing<PERSON> on 4/24/2018
 */
@Repository
public interface WarehouseRepository extends MongoRepository<Warehouse, String> {

    List<Warehouse> findByNameLikeIgnoreCaseAndActive(String name, Boolean b);

    Warehouse findByName(String name);

    Warehouse findWarehouseById(String id);

    Warehouse findWarehouseByCode(int code);

    List<Warehouse> findByCode(int code);

    List<Warehouse> findAllByActive(boolean b);

}
