package lk.sout.servicePal.inventory.service.impl;

import lk.sout.core.entity.Sequence;
import lk.sout.core.service.SequenceService;
import lk.sout.core.service.UserService;
import lk.sout.servicePal.inventory.entity.Warehouse;
import lk.sout.servicePal.inventory.repository.WarehouseRepository;
import lk.sout.servicePal.inventory.service.WarehouseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by Madhawa Weerasinghe on 4/24/2018
 */
@Service
public class WarehouseServiceImpl implements WarehouseService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WarehouseServiceImpl.class);

    @Autowired
    WarehouseRepository warehouseRepository;

    @Autowired
    List<Warehouse> warehouses;

    @Autowired
    UserService userService;

    @Autowired
    Sequence sequence;

    @Autowired
    SequenceService sequenceService;

    @Override
    public boolean save(Warehouse warehouse) {
        try {
            sequence = sequenceService.findSequenceByName("WarehouseNo");
            sequence.setCounter(sequence.getCounter() + 1);
            sequence = null;
            warehouse.setCode(sequence.getCounter());
            warehouseRepository.save(warehouse);
            sequenceService.save(sequence);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Saving warehouse Failed. " + warehouse.getName());
            return false;
        }
    }

    @Override
    public List<Warehouse> findAll() {
        try {
            return warehouseRepository.findAll();
        } catch (Exception ex) {
            LOGGER.error("Retrieving warehouse Failed");
            return null;
        }
    }

    @Override
    public List<Warehouse> findByNameLike(String name) {
        try {
            return warehouseRepository.findByNameLikeIgnoreCaseAndActive(name, true);
        } catch (Exception ex) {
            LOGGER.error("Retrieving warehouse Failed");
            return null;
        }
    }

    @Override
    public Warehouse findByName(String name) {
        try {
            return warehouseRepository.findByName(name);
        } catch (Exception ex) {
            LOGGER.error("Retrieving warehouse Failed");
            return null;
        }
    }

    @Override
    public Warehouse findById(String id) {
        try {
            return warehouseRepository.findWarehouseById(id);
        } catch (Exception ex) {
            LOGGER.error("Retrieving warehouse Failed");
            return null;
        }
    }

    @Override
    public Warehouse findByCode(int code) {
        try {
            return warehouseRepository.findWarehouseByCode(code);
        } catch (Exception ex) {
            LOGGER.error("Retrieving warehouse Failed");
            return null;
        }
    }

    @Override
    public String delete(String id) {
        try {
            warehouseRepository.deleteById(id);
            return "success";
        } catch (Exception ex) {
            LOGGER.error("Removing Warehouse failed " + id + ". " + ex.getMessage());
            return "failed";
        }
    }

    @Override
    public List<Warehouse> findAllByName(String name) {
        try {
            return warehouseRepository.findByNameLikeIgnoreCaseAndActive(name, true);
        } catch (Exception ex) {
            LOGGER.error("Retrieving warehouse Failed");
            return null;
        }
    }

    @Override
    public Iterable<Warehouse> findAll(Pageable pageable) {
        try {
            return warehouseRepository.findAll(pageable);
        } catch (Exception e) {
            return null;
        }
    }

}
