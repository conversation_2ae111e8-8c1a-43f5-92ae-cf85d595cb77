package lk.sout.servicePal.inventory.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON><PERSON> Weera<PERSON> on 4/20/2018
 */
@Document
@Component
public class UOM {

    @Id
    private String id;

    private String name;

    private boolean isWholeNumber;

    private String symbol;

    private boolean active;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public boolean getIsWholeNumber() {
        return isWholeNumber;
    }

    public void setIsWholeNumber(boolean wholeNumber) {
        isWholeNumber = wholeNumber;
    }


    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }
}
