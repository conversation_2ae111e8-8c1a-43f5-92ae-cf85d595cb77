package lk.sout.servicePal.inventory.repository;

import lk.sout.servicePal.inventory.entity.Item;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;

/**
 * Created by <PERSON><PERSON><PERSON> on 4/22/2020
 */
@Repository
public class CustomItemRepository {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomItemRepository.class);

    @Autowired
    MongoTemplate mongoTemplate;

    public List<Item> searchBarcodeLikeForSuggestions(String param) {
        Query query = new Query();
        query.limit(10);
        param = param.replace("(", "\\(");
        param = param.replace(")", "\\)");
        param = param.replace("-", "\\-");
        query.addCriteria(Criteria.where("active").is(true));
        query.addCriteria(Criteria.where("barcode").regex("(?i)\\b" + param + ".*?\\b"));
        query.fields().include("barcode").include("itemName").include("itemCode").
                include("sellingPrice").include("quantity").include("manageStock");
        return mongoTemplate.find(query, Item.class);
    }

    public List<Item> searchItemNameLikeForSuggestions(String param) {
        Query query = new Query();
        query.limit(20);
        param = param.replace("+", "\\+");
        param = param.replace("(", "\\(");
        param = param.replace(")", "\\)");
        query.addCriteria(Criteria.where("active").is(true));
        query.addCriteria(Criteria.where("itemName").regex("(?i)\\b" + param + ".*?\\b"));
        query.fields().include("itemName").include("barcode").include("itemCode").
                include("sellingPrice").include("quantity").include("manageStock");
        return mongoTemplate.find(query, Item.class);
    }

    public Page<Item> findAllStockForTable(Pageable pageable) {
        Query query = new Query();
        query.fields().include("id").include("barcode").include("itemName").include("rack").include("quantity").
                include("itemCost").include("sellingPrice").include("deadStockLevel");
        long count = mongoTemplate.count(query, Item.class);
        List<Item> filteredItems = mongoTemplate.find(query.with(pageable), Item.class, "item");
        Page<Item> items = PageableExecutionUtils.getPage(
                filteredItems,
                pageable,
                () -> count);
        return items;
    }

    public Page<Item> findAllItemForTable(Pageable pageable) {
        Query query = new Query();
        query.fields().include("barcode").include("itemCode").include("itemName").include("category").include("brand").
                include("model").include("sellingPrice");
        long count = mongoTemplate.count(query, Item.class);
        List<Item> filteredItems = mongoTemplate.find(query.with(pageable), Item.class, "item");
        Page<Item> items = PageableExecutionUtils.getPage(
                filteredItems,
                pageable,
                () -> count);
        return items;
    }

    public List<Item> getReorderList() {
        try {
            TypedAggregation<Item> agg = newAggregation(Item.class,
                    project("barcode", "quantity", "itemName", "sellingPrice", "deadStockLevel", "rack","active").
                            andExpression("deadStockLevel - quantity").as("diff"),
                    match(Criteria.where("diff").gt(0)),
                    match(Criteria.where("active").is(true)),
                    project("barcode", "quantity", "itemName", "sellingPrice", "deadStockLevel", "rack"));
            AggregationResults<Item> groupResults = mongoTemplate.aggregate(agg, Item.class);
            List<Item> result = groupResults.getMappedResults();
            return result;
        } catch (Exception e) {
            LOGGER.error("Retrieving All Items failed");
            return Collections.emptyList();
        }
    }


}
