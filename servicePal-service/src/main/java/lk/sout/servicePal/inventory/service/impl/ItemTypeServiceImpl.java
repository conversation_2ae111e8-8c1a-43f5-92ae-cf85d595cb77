package lk.sout.servicePal.inventory.service.impl;


import lk.sout.servicePal.inventory.entity.ItemType;
import lk.sout.servicePal.inventory.repository.ItemTypeRepository;
import lk.sout.servicePal.inventory.service.ItemTypeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Created by <PERSON><PERSON>a Weerasinghe on 4/18/2018
 */

@Service
public class ItemTypeServiceImpl implements ItemTypeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ItemTypeServiceImpl.class);

    @Autowired
    ItemTypeRepository itemTypeRepository;

    @Override
    public boolean save(ItemType itemType) {
        try {
            itemTypeRepository.save(itemType);
            LOGGER.info("Item Type saved. " + itemType.getName());
            return true;
        } catch (Exception ex) {
            LOGGER.error("Saving Item Type Failed : " + ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean remove(String id) {
        try {
            itemTypeRepository.deleteById(id);
            LOGGER.info("Item Type removed. ");
            return true;
        } catch (Exception ex) {
            LOGGER.error("Removing Item Type Failed :" + ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<ItemType> findAll(Pageable pageable) {
        try {
            return itemTypeRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Retrieving All Item Types failed");
            return null;
        }
    }

    @Override
    public Iterable<ItemType> findAll() {
        try {
            return itemTypeRepository.findAll();
        } catch (Exception ex) {
            LOGGER.error("Retrieving All Item Types failed");
            return null;
        }
    }


    @Override
    public ItemType findOne(String id) {
        try {
            Optional<ItemType> itemType = itemTypeRepository.findById(id);
            return itemType.get();
        } catch (Exception ex) {
            LOGGER.error("Retrieving a Item Type failed :" + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<ItemType> findByName(String name) {
        try {
            return itemTypeRepository.findItemTypeByNameLikeIgnoreCaseAndActive(name,true);

        } catch (Exception e) {
            LOGGER.error("ItemType Searching failed :" + e.getMessage());
            return null;
        }
    }

}
