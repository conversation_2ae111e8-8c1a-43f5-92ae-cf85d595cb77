package lk.sout.servicePal.inventory.entity;

import org.springframework.data.annotation.*;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 4/20/2018
 */
@Document
@Component
public class Item {

    @Id
    private String id;

    @Indexed(unique = true)
    private String barcode;

    @Indexed(unique = true)
    private String itemCode;

    private String itemName;

    private Double oldSellingPrice;

    @DBRef
    private Brand brand;

    @DBRef
    private ItemType itemType;

    @DBRef
    private Item sparePartFor;

    @DBRef
    private ItemCategory itemCategory;

    @DBRef
    private SubCategory subCategory;

    private Double sellingPrice;

    private Double itemCost;

    private String description;

    private Double serviceCommission;

    @Transient
    private boolean isUpdate;

    @DBRef
    private UOM uom;

    @DBRef
    private Rack rack;

    private boolean manageStock;

    private boolean active;

    private boolean autoInactive;

    private String commonCode;

    private Double deadStockLevel;

    @CreatedDate
    private Date createdDate;

    @CreatedBy
    private String createdBy;

    @LastModifiedDate
    private Date lastModifiedDate;

    @LastModifiedBy
    private String lastModifiedBy;

    @Transient
    private double quantity;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public Brand getBrand() {
        return brand;
    }

    public void setBrand(Brand brand) {
        this.brand = brand;
    }

    public ItemType getItemType() {
        return itemType;
    }

    public void setItemType(ItemType itemType) {
        this.itemType = itemType;
    }

    public Item getSparePartFor() {
        return sparePartFor;
    }

    public void setSparePartFor(Item sparePartFor) {
        this.sparePartFor = sparePartFor;
    }

    public ItemCategory getItemCategory() {
        return itemCategory;
    }

    public void setItemCategory(ItemCategory itemCategory) {
        this.itemCategory = itemCategory;
    }

    public SubCategory getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(SubCategory subCategory) {
        this.subCategory = subCategory;
    }

    public Double getSellingPrice() {
        return sellingPrice;
    }

    public void setSellingPrice(Double sellingPrice) {
        this.sellingPrice = sellingPrice;
    }

    public Double getItemCost() {
        return itemCost;
    }

    public void setItemCost(Double itemCost) {
        this.itemCost = itemCost;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public UOM getUom() {
        return uom;
    }

    public void setUom(UOM uom) {
        this.uom = uom;
    }

    public Rack getRack() {
        return rack;
    }

    public void setRack(Rack rack) {
        this.rack = rack;
    }

    public boolean isManageStock() {
        return manageStock;
    }

    public void setManageStock(boolean manageStock) {
        this.manageStock = manageStock;
    }

    public boolean isActive() {
        return active;
    }

    public boolean isAutoInactive() {
        return autoInactive;
    }

    public void setAutoInactive(boolean autoInactive) {
        this.autoInactive = autoInactive;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public String getCommonCode() {
        return commonCode;
    }

    public void setCommonCode(String commonCode) {
        this.commonCode = commonCode;
    }

    public Double getDeadStockLevel() {
        return deadStockLevel;
    }

    public void setDeadStockLevel(Double deadStockLevel) {
        this.deadStockLevel = deadStockLevel;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Double getServiceCommission() {
        return serviceCommission;
    }

    public void setServiceCommission(Double serviceCommission) {
        this.serviceCommission = serviceCommission;
    }

    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public boolean isUpdate() {
        return isUpdate;
    }

    public void setUpdate(boolean update) {
        isUpdate = update;
    }

    public Double getOldSellingPrice() {
        return oldSellingPrice;
    }

    public void setOldSellingPrice(Double oldSellingPrice) {
        this.oldSellingPrice = oldSellingPrice;
    }

    public double getQuantity() {
        return quantity;
    }

    public void setQuantity(double quantity) {
        this.quantity = quantity;
    }
}

