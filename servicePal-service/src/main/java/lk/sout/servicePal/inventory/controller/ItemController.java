package lk.sout.servicePal.inventory.controller;

import lk.sout.servicePal.inventory.entity.Brand;
import lk.sout.servicePal.inventory.entity.Item;
import lk.sout.servicePal.inventory.entity.ItemCategory;
import lk.sout.servicePal.inventory.service.ItemService;
import lk.sout.servicePal.trade.entity.PurchaseInvoice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/item")
public class ItemController {

    @Autowired
    ItemService itemService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody Item item) {
        try {
            return ResponseEntity.ok(itemService.save(item));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    private ResponseEntity<?> findAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(itemService.findAll(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/getReorderList", method = RequestMethod.GET)
    private ResponseEntity<?> getReorderList() {
        try {
            return ResponseEntity.ok(itemService.getReorderList());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllStock", method = RequestMethod.GET)
    private ResponseEntity<?> findAllStock(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(itemService.findAllStock(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findActiveByNameLike", method = RequestMethod.GET)
    private ResponseEntity<?> findActiveByNameLike(@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(itemService.findActiveByNameLike(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findActiveServiceByNameLike", method = RequestMethod.GET)
    private ResponseEntity<?> findActiveServiceByNameLike(@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(itemService.findActiveServiceByNameLike(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findActiveServiceByBarcodeLike", method = RequestMethod.GET)
    private ResponseEntity<?> findActiveServiceByCodeLike(@RequestParam("barcode") String barcode) {
        try {
            return ResponseEntity.ok(itemService.findActiveServiceByCodeLike(barcode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findActiveTransportByBarcodeLike", method = RequestMethod.GET)
    private ResponseEntity<?> findActiveTransportByCodeLike(@RequestParam("barcode") String barcode) {
        try {
            return ResponseEntity.ok(itemService.findActiveTransportByCodeLike(barcode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findActiveTransportByNameLike", method = RequestMethod.GET)
    private ResponseEntity<?> findActiveTransportByNameLike(@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(itemService.findActiveTransportByNameLike(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByNameLike", method = RequestMethod.GET)
    private ResponseEntity<?> findAllByNameLike(@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(itemService.findAllByNameLike(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByAny", method = RequestMethod.GET)
    private ResponseEntity<?> findByAny(@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(itemService.findByAny(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByBarcodeLike", method = RequestMethod.GET)
    private ResponseEntity<?> searchByCode(@RequestParam("barcode") String code) {
        try {
            return ResponseEntity.ok(itemService.findAllByBarcodeLike(code));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findOneByItemCode", method = RequestMethod.GET)
    private ResponseEntity<?> findOneByItemCode(@RequestParam("itemCode") String code) {
        try {
            return ResponseEntity.ok(itemService.findOneByItemCode(code));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findOneByBarcode", method = RequestMethod.GET)
    private ResponseEntity<?> findOneByBarcode(@RequestParam("barcode") String code) {
        try {
            return ResponseEntity.ok(itemService.findOneByBarcode(code));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findOneById", method = RequestMethod.GET)
    private ResponseEntity<?> findOneById(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(itemService.findOne(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByCategory", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByCategory(@RequestBody ItemCategory category) {
        try {
            return ResponseEntity.ok(itemService.findByCategory(category));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByBrand", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByBrand(@RequestBody Brand brand) {
        try {
            return ResponseEntity.ok(itemService.findByBrand(brand));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

}
