package lk.sout.servicePal.inventory.service.impl;

import lk.sout.config.Constant;
import lk.sout.core.entity.Action;
import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import lk.sout.core.entity.User;
import lk.sout.core.service.ActionService;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.UserService;
import lk.sout.servicePal.inventory.entity.*;
import lk.sout.servicePal.inventory.repository.CustomItemRepository;
import lk.sout.servicePal.inventory.repository.ItemRepository;
import lk.sout.servicePal.inventory.repository.ItemTypeRepository;
import lk.sout.servicePal.inventory.service.ItemCategoryService;
import lk.sout.servicePal.inventory.service.ItemService;
import lk.sout.servicePal.inventory.service.StockService;
import lk.sout.servicePal.inventory.service.WarehouseService;
import lk.sout.servicePal.trade.entity.PurchaseInvoiceRecord;
import lk.sout.servicePal.trade.entity.SalesInvoice;
import lk.sout.servicePal.trade.entity.SalesInvoiceRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Created by Madhawa Weerasinghe on 4/21/2018
 */

@Service
public class ItemServiceImpl implements ItemService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ItemServiceImpl.class);

    @Autowired
    ItemRepository itemRepository;

    @Autowired
    Response response;

    @Autowired
    ItemTypeRepository itemTypeRepository;

    @Autowired
    PurchaseInvoiceRecord purchaseInvoiceRecord;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    ActionService actionService;

    @Autowired
    Action action;

    @Autowired
    Item item;

    @Autowired
    CustomItemRepository customItemRepository;

    @Autowired
    Stock stock;

    @Autowired
    StockService stockService;

    @Autowired
    ItemCategoryService itemCategoryService;

    @Autowired
    WarehouseService warehouseService;

    @Autowired
    UserService userService;

    @Override
    @Transactional
    public Response save(Item item) {
        try {
            User user = userService.findLoggedInUser();
            Warehouse warehouse = warehouseService.findByCode(user.getWarehouseCode());
            if (null == item.getId()) {
                if (itemRepository.findByBarcode(item.getBarcode()) != null) {
                    response.setCode(503);
                    response.setMessage("Item Already Available");
                    response.setData("Item Already Available");
                    return response;
                }
                if (item.isManageStock()) {
                    stock.setId(null);
                    stock.setQuantity(Optional.ofNullable(item.getQuantity()).orElse(0.0));
                    stock.setDeadStockLevel(item.getDeadStockLevel());
                    stock.setItemCode(item.getItemCode());
                    stock.setBarcode(item.getBarcode());
                    stock.setItemName(item.getItemName());
                    stock.setSellingPrice(item.getSellingPrice());
                    stock.setItemCost(item.getItemCost());
                    stock.setItemName(item.getItemName());
                    if (null != item.getItemCategory()) {
                        stock.setCategoryCode(item.getItemCategory().getCode());
                    }
                    if (null != item.getBrand()) {
                        stock.setBrandCode(item.getBrand().getCode());
                    }
                    stock.setWarehouseCode(warehouse.getCode());
                    stock.setWarehouseName(warehouse.getName());
                    stock.setActive(true);
                    stockService.save(stock, "Creating Stock by Item Creation", item.getItemCode());
                }
            }
            if (null != item.getId() && item.isManageStock()) {
                Item avlItem = itemRepository.findByItemCode(item.getItemCode());
                List<Stock> stocks = stockService.findByItemCode(item.getItemCode());

                if (!avlItem.getBarcode().equals(item.getBarcode())) {
                    for (Stock stock : stocks) {
                        stock.setBarcode(item.getBarcode());
                        stockService.basicSave(stock);
                    }
                }
                if (!avlItem.getItemName().equals(item.getItemName())) {
                    for (Stock stock : stocks) {
                        stock.setItemName(item.getItemName());
                        stockService.basicSave(stock);
                    }
                }
                if (avlItem.getSellingPrice().compareTo(item.getSellingPrice()) != 0) {
                    for (Stock stock : stocks) {
                        stock.setSellingPrice(item.getSellingPrice());
                        stockService.basicSave(stock);
                    }
                }
                if (avlItem.getItemCost().compareTo(item.getItemCost()) != 0) {
                    for (Stock stock : stocks) {
                        stock.setItemCost(item.getItemCost());
                        stockService.basicSave(stock);
                    }
                }
                if (null != avlItem.getItemCategory() && !avlItem.getItemCategory().getCode().equals(item.getItemCategory().getCode())) {
                    for (Stock stock : stocks) {
                        stock.setCategoryCode(item.getItemCategory().getCode());
                        stockService.basicSave(stock);
                    }
                }
                if (null != avlItem.getBrand() && !avlItem.getBrand().getCode().equals(item.getBrand().getCode())) {
                    for (Stock stock : stocks) {
                        stock.setBrandCode(item.getBrand().getCode());
                        stockService.basicSave(stock);
                    }
                }
            }
            item.setItemCode(String.valueOf(System.currentTimeMillis()));
            itemRepository.save(item);
            response.setCode(200);
            response.setMessage("Item Created Successfully");
            return response;
        } catch (Exception ex) {
            ex.printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Creating Item Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Creating Item Failed");
            response.setData(ex.getMessage());
            return response;
        }
    }

    @Override
    public boolean remove(String id) {
        try {
            itemRepository.deleteById(id);
            LOGGER.info("Item removed. ");
            return true;
        } catch (Exception ex) {
            LOGGER.error("Removing Item Failed : " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<Item> findAll(Pageable pageable) {
        try {
            return customItemRepository.findAllItemForTable(pageable);
        } catch (Exception ex) {
            LOGGER.error("Retrieving All Items failed");
            return null;
        }
    }

    @Override
    public List<Item> getReorderList() {
        try {
            return customItemRepository.getReorderList();
        } catch (Exception ex) {
            LOGGER.error("Retrieving All Items failed");
            return null;
        }
    }

    @Override
    public Iterable<Item> findAllStock(Pageable pageable) {
        try {
            return customItemRepository.findAllStockForTable(pageable);
        } catch (Exception ex) {
            LOGGER.error("Retrieving Dead Stock failed");
            return null;
        }
    }

    @Override
    public Item findOne(String id) {
        try {
            Optional<Item> item = itemRepository.findById(id);
            return item.get();
        } catch (Exception ex) {
            LOGGER.error("Retrieving an Item failed : " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findAllByBarcodeLike(String code) {
        if (code.equals(Constant.NO_STOCK_BARCODE)) {
            List<Item> items = new ArrayList<>();
            items.add(itemRepository.findByItemCode("0"));
            return items;
        }
        return customItemRepository.searchBarcodeLikeForSuggestions(code);
    }

    @Override
    public List<Item> findByCategory(ItemCategory category) {
        try {
            return itemRepository.findAllByItemCategory(category);
        } catch (Exception e) {
            LOGGER.error("Search item Failed: " + e.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findByBrand(Brand brand) {
        try {
            return itemRepository.findAllByBrand(brand);
        } catch (Exception e) {
            LOGGER.error("Search item Failed: " + e.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findActiveByNameLike(String name) {
        try {
            return customItemRepository.searchItemNameLikeForSuggestions(name);
        } catch (Exception ex) {
            LOGGER.error("Search item Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findActiveServiceByNameLike(String name) {
        try {
            ItemType service = itemTypeRepository.findByName("Service");
            List<Item> items = itemRepository.findAllByItemTypeAndItemNameLikeIgnoreCaseAndActive(service, name, true);
            return items;
        } catch (Exception ex) {
            LOGGER.error("Search item Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findActiveServiceByCodeLike(String name) {
        try {
            ItemType service = itemTypeRepository.findByName("Service");
            return itemRepository.findAllByItemTypeAndBarcodeLikeIgnoreCaseAndActive(service, name, true);
        } catch (Exception ex) {
            LOGGER.error("Search item Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findActiveTransportByNameLike(String name) {
        try {
            ItemType transport = itemTypeRepository.findByName("Transport");
            List<Item> items = itemRepository.findAllByItemTypeAndItemNameLikeIgnoreCaseAndActive(transport, name, true);
            return items;
        } catch (Exception ex) {
            LOGGER.error("Search item Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findActiveTransportByCodeLike(String name) {
        try {
            ItemType transport = itemTypeRepository.findByName("Transport");
            List<Item> items = itemRepository.findAllByItemTypeAndBarcodeLikeIgnoreCaseAndActive(transport, name, true);
            return items;
        } catch (Exception ex) {
            LOGGER.error("Search item Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findAllByNameLike(String name) {
        try {
            List<Item> items = itemRepository.findAllByItemNameLikeIgnoreCase(name);
            return items;
        } catch (Exception ex) {
            LOGGER.error("Search item Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Item> findByAny(String any) {
        List<Item> itemList = new ArrayList<>();
        try {
            if (itemRepository.findAllByItemNameLikeIgnoreCaseAndActive(any, true).size() > 0) {
                List<Item> items = itemRepository.findAllByItemNameLikeIgnoreCaseAndActive(any, true);
                itemList = items;
            } else if (itemRepository.findTop10ByBarcodeLikeIgnoreCaseAndActive(any, true).size() > 0) {
                List<Item> items = itemRepository.findTop10ByBarcodeLikeIgnoreCaseAndActive(any, true);
                itemList = items;
            }
            return itemList;
        } catch (Exception ex) {
            LOGGER.error("Search item Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Item findOneByItemCode(String s) {
        try {
            return itemRepository.findByItemCode(s);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public Item findOneByBarcode(String s) {
        try {
            return itemRepository.findByBarcodeIgnoreCase(s);
        } catch (Exception e) {
            return null;
        }
    }

}
