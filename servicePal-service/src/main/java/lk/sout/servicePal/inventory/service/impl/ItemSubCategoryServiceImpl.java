package lk.sout.servicePal.inventory.service.impl;

import lk.sout.servicePal.inventory.entity.SubCategory;
import lk.sout.servicePal.inventory.repository.ItemCategoryRepository;
import lk.sout.servicePal.inventory.repository.ItemSubCategoryRepository;
import lk.sout.servicePal.inventory.service.ItemSubCategoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ItemSubCategoryServiceImpl implements ItemSubCategoryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ItemSubCategoryServiceImpl.class);

    @Autowired
    ItemSubCategoryRepository subCategoryRepository;

    @Autowired
    ItemCategoryRepository itemCategoryRepository;

    @Override
    public boolean save(SubCategory subCategory) {
        try {
            subCategory.setCode(String.valueOf(System.currentTimeMillis()));
            subCategoryRepository.save(subCategory);
            LOGGER.info("Sub Item Category saved. " + subCategory.getSubCategoryName());
            return true;
        } catch (Exception ex) {
            LOGGER.error("Saving Sub Item Category Failed : " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<SubCategory> findAll(Pageable pageable) {
        return subCategoryRepository.findAll(pageable);
    }

    @Override
    public List<SubCategory> findAllByCategory(String s) {
        return null;
    }

    @Override
    public SubCategory findAllById(String s) {
        return null;
    }

    @Override
    public String delete(String id) {
        try {
            subCategoryRepository.deleteById(id);
            LOGGER.info("item Category removed. ");
            return "success";
        } catch (Exception ex) {
            LOGGER.error("Removing item Category Failed :" + ex.getMessage());
            return ex.getMessage();
        }
    }

    @Override
    public List<SubCategory> findByName(String any) {
        try {
            return subCategoryRepository.findSubCategoryBySubCategoryNameLikeIgnoreCase(any);

        } catch (Exception e) {
            LOGGER.error("Sub Item Category Searching failed :" + e.getMessage());
            return null;
        }
    }

    @Override
    public List<SubCategory> findByParent(String key, String catId) {
        try {
            List<SubCategory> subCategories = subCategoryRepository.
                    findSubCategoryByItemCategoryAndSubCategoryNameLikeIgnoreCaseAndActive(itemCategoryRepository.findById(catId).get(),
                            key, true);
            return subCategories;
        } catch (Exception e) {
            LOGGER.error("Sub Item Category Searching failed :" + e.getMessage());
            return null;
        }
    }

    @Override
    public List<SubCategory> findAll() {
        try {
            return subCategoryRepository.findAll();
        } catch (Exception ex) {
            LOGGER.error("Retrieving All Sub Categories failed: " + ex.getMessage());
            return null;
        }
    }
}
