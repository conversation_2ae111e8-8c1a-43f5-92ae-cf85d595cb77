package lk.sout.servicePal.inventory.controller;

import lk.sout.servicePal.inventory.entity.SubCategory;
import lk.sout.servicePal.inventory.service.ItemCategoryService;
import lk.sout.servicePal.inventory.service.ItemSubCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/subItemCategory")
public class ItemSubCategoryController {

    @Autowired
    ItemSubCategoryService subItemCategoryService;

    @Autowired
    ItemCategoryService itemCategoryService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody SubCategory subCategory) {
        try {
            return ResponseEntity.ok(subItemCategoryService.save(subCategory));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/get", method = RequestMethod.GET)
    private ResponseEntity<?> findAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(subItemCategoryService.findAll(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    private ResponseEntity<?> findAll() {
        try {
            return ResponseEntity.ok(itemCategoryService.findAllCategories());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/search", method = RequestMethod.GET)
    private ResponseEntity<?> search(@RequestParam("any") String name) {
        try {
            return ResponseEntity.ok(subItemCategoryService.findByName(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByParent", method = RequestMethod.GET)
    private ResponseEntity<?> findByParent(@RequestParam("key") String key, @RequestParam("catName") String catName) {
        try {
            return ResponseEntity.ok(subItemCategoryService.findByParent(key,catName));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/delete", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> delete(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(subItemCategoryService.delete(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}
