package lk.sout.servicePal.inventory.service;

import lk.sout.servicePal.inventory.entity.SubCategory;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ItemSubCategoryService {

    boolean save(SubCategory subCategory);

    Iterable<SubCategory> findAll(Pageable pageable);

    List<SubCategory> findAllByCategory(String s);

    SubCategory findAllById(String s);

    String delete(String id);

    List<SubCategory> findByName(String name);

    List<SubCategory> findByParent(String key, String catId);

    List<SubCategory> findAll();
}


