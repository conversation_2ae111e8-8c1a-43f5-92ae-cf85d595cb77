package lk.sout.servicePal.inventory.repository;

import lk.sout.servicePal.inventory.entity.ItemCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ItemCategoryRepository extends MongoRepository<ItemCategory,String> {

    List<ItemCategory> findItemCategoryByCategoryNameLikeIgnoreCaseAndActive(String name, Boolean b);

    Page<ItemCategory> findAll(Pageable pageable);

    List<ItemCategory> findAll();

}
