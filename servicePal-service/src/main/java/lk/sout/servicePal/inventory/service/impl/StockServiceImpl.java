/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package lk.sout.servicePal.inventory.service.impl;

import lk.sout.core.entity.*;
import lk.sout.core.service.ActionService;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.UserService;
import lk.sout.servicePal.inventory.entity.*;
import lk.sout.servicePal.inventory.repository.*;
import lk.sout.servicePal.inventory.service.ItemService;
import lk.sout.servicePal.inventory.service.StockService;
import lk.sout.servicePal.inventory.service.WarehouseService;
import lk.sout.servicePal.trade.entity.PurchaseInvoiceRecord;
import lk.sout.servicePal.trade.entity.SalesInvoice;
import lk.sout.servicePal.trade.entity.SalesInvoiceRecord;
import lk.sout.servicePal.trade.repository.PurchaseInvoiceRecordRepository;
import lk.sout.servicePal.trade.repository.SalesInvoiceRecordRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static lk.sout.config.Constant.STOCK_UPDATE_REASON;

/**
 * <AUTHOR>
 */
@Service
public class StockServiceImpl implements StockService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockServiceImpl.class);

    @Autowired
    Response response;

    @Autowired
    StockRepository stockRepository;

    @Autowired
    CustomStockRepository customStockRepository;

    @Autowired
    StockMovementRepository stockMovementRepository;

    @Autowired
    UserService userService;

    @Autowired
    TransferStockRepository transferStockRepository;

    @Autowired
    ItemRepository itemRepository;

    @Autowired
    ItemService itemService;

    @Autowired
    Stock stock;

    @Autowired
    StockMovement stockMovement;

    @Autowired
    Item item;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    ActionService actionService;

    @Autowired
    Action action;

    @Autowired
    WarehouseService warehouseService;

    @Autowired
    CustomItemRepository customItemRepository;

    @Autowired
    PurchaseInvoiceRecordRepository purchaseInvoiceRecordRepository;

    @Autowired
    SalesInvoiceRecordRepository salesInvoiceRecordRepository;

    @Autowired
    StockService stockService;

    @Override
    public Response save(Stock stock, String type, String itemCode) {
        try {
            addStockMovement(type, itemCode, stock);
            stockRepository.save(stock);
            response.setCode(200);
            response.setMessage("Successfully Saved");
            return response;
        } catch (Exception ex) {
            LOGGER.error("Saving stock failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Saving Failed");
            ex.printStackTrace();
            return response;
        }
    }

    @Override
    //Quantity doesn't involved, so no need of updating stock movement, just saving
    public boolean basicSave(Stock stock) {
        try {
            stockRepository.save(stock);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Basic Save Failed " + ex.getMessage());
            ex.printStackTrace();
            return false;
        }
    }

    @Transactional
    public Response createByPurchaseInvRecs(List<PurchaseInvoiceRecord> piRecords, boolean manual) {
        try {
            if (manual == true && !userService.isAdminOrManagerLoggedIn()) {
                response.setCode(403);
                response.setMessage("No Permission");
                return response;
            }
            stock.setId(null);
            User user = userService.findLoggedInUser();
            for (PurchaseInvoiceRecord pir : piRecords) {
                String barcode = pir.getBarcode();
                stock = stockRepository.findByBarcodeAndWarehouseCode(barcode, user.getWarehouseCode());
                Item item = itemService.findOneByBarcode(barcode);

                if (null != item.getSellingPrice() && pir.getSellingPrice().compareTo(item.getSellingPrice()) != 0) {
                    item.setSellingPrice(pir.getSellingPrice());  // selling price of item is updating when adding stock
                    itemRepository.save(item);
                }

                if (null != item.getItemCost() && pir.getItemCost().compareTo(item.getItemCost()) != 0) {
                    item.setItemCost(pir.getItemCost());  // Item cost is updating when adding stock
                    itemRepository.save(item);
                }

                //update existing
                if (null != stock) {
                    stock.setQuantity(stock.getQuantity() + pir.getQuantity());
                    Double oldSellingPrice = item.getSellingPrice();
                    if (oldSellingPrice.compareTo(pir.getSellingPrice()) != 0) {
                        item.setSellingPrice(pir.getSellingPrice());
                    }
                    if (null == item.getItemCost() || item.getItemCost().compareTo(pir.getItemCost()) != 0) {
                        item.setItemCost(pir.getItemCost());
                        item.setSellingPrice(pir.getSellingPrice());
                    }
                    if (null != stock.getDeadStockLevel() && stock.getDeadStockLevel().compareTo(stock.getDeadStockLevel()) != 0) {
                        stock.setDeadStockLevel(stock.getDeadStockLevel());
                    }
                    itemRepository.save(item);
                    save(stock, "Updating Stock By Purchasing", barcode);
                } else {
                    stock = new Stock();
                    stock.setQuantity(pir.getQuantity());
                    stock.setDeadStockLevel(item.getDeadStockLevel());
                    stock.setItemCode(item.getItemCode());
                    stock.setBarcode(item.getBarcode());
                    stock.setItemName(item.getItemName());
                    stock.setSellingPrice(item.getSellingPrice());
                    stock.setItemCost(item.getItemCost());
                    stock.setItemName(item.getItemName());
                    if (null != item.getItemCategory()) {
                        stock.setCategoryCode(item.getItemCategory().getCode());
                    }
                    if (null != item.getBrand()) {
                        stock.setBrandCode(item.getBrand().getCode());
                    }
                    stock.setWarehouseCode(user.getWarehouseCode());
                    stock.setActive(true);
                    save(stock, "Creating Stock By Purchasing", barcode);
                }

                List<Stock> stocks = stockService.findByItemCode(pir.getItemCode());
                for (Stock stock : stocks) {
                    stock.setSellingPrice(item.getSellingPrice());
                    stock.setItemCost(item.getItemCost());
                    basicSave(stock);
                }
            }
            response.setCode(200);
            response.setMessage("Item Added Successfully");
            return response;
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            ex.printStackTrace();
            LOGGER.error("Adding Item Failed " + ex.getMessage() + " on line " + ex.getStackTrace()[0].getLineNumber());
            response.setCode(501);
            response.setMessage("Adding Item Failed");
            response.setData(ex.getMessage());
            return response;
        }
    }

    @Override
    public Iterable<Stock> findAllByWarehouse(Pageable pageable, int warehouseCode) {
        try {
            Iterable<Stock> subStocks = stockRepository.findAllByWarehouseCode(pageable, warehouseCode);
            return subStocks;
        } catch (Exception ex) {
            LOGGER.error("findAllByWarehouse failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Stock findOneByBarcodeAndWarehouse(String barcode, int warehouseCode) {
        return stockRepository.findByBarcodeAndWarehouseCode(barcode, warehouseCode);
    }

    @Override
    public List<Stock> findByUserWarehouseAndBarcodeLike(String barcode) {
        return stockRepository.findByWarehouseCodeAndBarcodeLikeIgnoreCaseAndActiveOrderBySellingPrice
                (userService.findLoggedInUser().getWarehouseCode(), barcode, true);
    }

    @Override
    public List<Stock> findByUserWarehouseAndBarcodeLike(int warehouseCode, String barcode) {
        return stockRepository.findByWarehouseCodeAndBarcodeLikeIgnoreCaseAndActiveOrderBySellingPrice(warehouseCode,
                barcode, true);
    }

    @Override
    public List<Stock> findByUserWarehouseAndItemNameLike(String itemName) {
        return stockRepository.findTop15ByWarehouseCodeAndItemNameLikeIgnoreCaseAndActiveOrderBySellingPrice
                (userService.findLoggedInUser().getWarehouseCode(), itemName, true);
    }

    @Override
    public List<Stock> findByUserWarehouseAndItemNameLike(int warehouseCode, String itemName) {
        return stockRepository.findByWarehouseCodeAndItemNameLikeIgnoreCaseAndActiveOrderBySellingPrice(warehouseCode,
                itemName, true);
    }

    @Override
    public Stock findByBarcodeAndUserWarehouse(String barcode){
        return stockRepository.findByBarcodeAndWarehouseCode(barcode,userService.findLoggedInUser().getWarehouseCode());
    }

    @Override
    public Stock findByItemCodeAndWarehouse(String itemCode, int warehouseCode) {
        try {
            Stock stock = stockRepository.findByItemCodeAndWarehouseCode(itemCode, warehouseCode);
            return stock;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Stock> findByItemCode(String itemCode) {
        return stockRepository.findByItemCode(itemCode);
    }

    public boolean topUpStock(String itemCode, int warehouseCode, Double qty, boolean returnRecord) {
        boolean response = false;
        Double quantity = 0.0;
        Stock stock1 = findByItemCodeAndWarehouse(itemCode, warehouseCode);

        if (stock1 != null) {
            quantity = stock1.getQuantity();
        } else {
            quantity = 0.0;
            qty = 0.0;
        }

        if (qty >= 0 || returnRecord) {
            stock1.setQuantity(quantity + qty);
            stockRepository.save(stock1);
            response = true;
        } else {
            response = false;
        }
        return response;
    }

    @Override
    public boolean deductFromStock(SalesInvoice salesInvoice, int warehouseCode) {
        try {
            for (SalesInvoiceRecord salesInvoiceRecord : salesInvoice.getSalesInvoiceRecords()) {
                Item item = itemRepository.findByItemCode(salesInvoiceRecord.getItemCode());
                salesInvoiceRecord.setDate(LocalDateTime.now());
                salesInvoiceRecord.setInvoiceNo(salesInvoice.getInvoiceNo());
                salesInvoiceRecord.setCounter(salesInvoice.getCounter());
                salesInvoiceRecord.setBarcode(item.getBarcode());
                salesInvoiceRecord.setWarehouseCode(salesInvoice.getWarehouseCode());
                if (item.isManageStock()) {
                    Stock stock = stockService.findOneByBarcodeAndWarehouse(item.getBarcode(),
                            warehouseCode);
                    salesInvoiceRecord.setStockCount(stock.getQuantity());
                    if (stock.getQuantity().compareTo(salesInvoiceRecord.getQuantity()) >= 0) {
                        stock.setQuantity(stock.getQuantity() - salesInvoiceRecord.getQuantity());
                        save(stock, "Updating Stock by Sales Invoice Deduction", item.getItemCode());
                    } else {
                        return false;
                    }
                }
                salesInvoiceRecordRepository.save(salesInvoiceRecord);
            }
            return true;
        } catch (Exception ex) {
            return false;
        }
    }

    @Override
    public boolean deductFromStock(String itemCode, int warehouseCode, double quantity) {
        try {
            Item item = itemService.findOneByItemCode(itemCode);
            if (item == null || !item.isManageStock()) {
                return false;
            }

            Stock stock = stockService.findOneByBarcodeAndWarehouse(item.getBarcode(), warehouseCode);
            if (stock == null || stock.getQuantity() == null || stock.getQuantity() < quantity) {
                return false;
            }
            // Deduct stock safely
            stock.setQuantity(stock.getQuantity() - quantity);
            save(stock, STOCK_UPDATE_REASON, item.getItemCode());
            return true;
        } catch (
                Exception ex) {
            return false;
        }
    }

    @Transactional
    public Response adjust(String stockId, Double actualValue, String remark) {
        try {
            if (userService.isAdminOrManagerLoggedIn()) {
                Double systemValue;

                Stock stock = stockRepository.findById(stockId).get();
                systemValue = stock.getQuantity();
                stock.setQuantity(actualValue);

                MetaData stockAdjustment = metaDataService.searchMetaData("Adjust Stock", "Action");
                action = new Action();
                action.setType(stockAdjustment);
                action.setReference(stock.getBarcode());
                action.setReference2(stock.getItemName());
                if (actualValue.compareTo(systemValue) > 0) {
                    action.setOperator("+");
                } else {
                    action.setOperator("-");
                }
                action.setChange(String.valueOf(actualValue - systemValue));
                action.setRemark(remark);

                actionService.save(action);
                save(stock, "Updating Stock by Count Adjustment", stock.getItemCode());
                response.setCode(200);
                response.setMessage("Successfully Saved");
            } else {
                response.setCode(403);
                response.setMessage("No Permission");
            }
            return response;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            response.setCode(501);
            response.setMessage("Saving Failed");
            return response;
        }
    }

    @Override
    @Transactional
    public Response transferStock(TransferStock transferStock) {
        try {
            if (userService.isAdminOrManagerLoggedIn()) {
                Warehouse targetWarehouse = warehouseService.findByCode(transferStock.getTargetWarehouseCode());
                Warehouse sourceWarehouse = warehouseService.findByCode(transferStock.getSourceWarehouseCode());

                Double transferringQty = transferStock.getTransferQty();
                Stock sourceRec = stockRepository.findByItemCodeAndWarehouseCode(transferStock.getItemCode(),
                        sourceWarehouse.getCode());
                Stock targetRec = stockRepository.findByItemCodeAndWarehouseCode(transferStock.getItemCode(),
                        targetWarehouse.getCode());

                if (sourceRec.getQuantity().compareTo(transferringQty) >= 0) {
                    if (null == targetRec) {
                        Item item = itemService.findOneByItemCode(transferStock.getItemCode());
                        stock.setId(null);
                        stock.setActive(true);
                        stock.setBarcode(item.getBarcode());
                        stock.setItemCode(transferStock.getItemCode());
                        stock.setItemName(item.getItemName());
                        stock.setSellingPrice(item.getSellingPrice());
                        stock.setItemCost(item.getItemCost());
                        stock.setQuantity(transferStock.getTransferQty());
                        stock.setCategoryCode(item.getItemCategory().getCode());
                        stock.setBrandCode(item.getBrand().getCode());
                        stock.setDeadStockLevel(item.getDeadStockLevel());
                        stock.setWarehouseCode(targetWarehouse.getCode());
                        stock.setWarehouseName(targetWarehouse.getName());
                        save(stock, "Creating Stock by Transfer In", transferStock.getItemCode());
                    } else {
                        targetRec.setQuantity(targetRec.getQuantity() + transferringQty);
                        save(targetRec, "Updating Stock by Transfer In", transferStock.getItemCode());
                    }

                    sourceRec.setQuantity(sourceRec.getQuantity() - transferringQty);
                    save(sourceRec, "Updating Stock by Transfer Out", transferStock.getItemCode());
                }

                transferStockRepository.save(transferStock);
                response.setCode(200);
                response.setMessage("Successfully Saved");
            } else {
                response.setCode(403);
                response.setMessage("No permission");
            }
            return response;
        } catch (Exception ex) {
            ex.printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Saving sub stock failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Saving Failed");
            return response;
        }
    }

    @Override
    @Transactional
    public Response batchStockTransfer(BatchStockTransfer batchStockTransfer) {
        try {

            for (TransferStock transferStock : batchStockTransfer.getBatch()) {

                Warehouse targetWarehouse = warehouseService.findByCode(transferStock.getTargetWarehouseCode());
                Warehouse sourceWarehouse = warehouseService.findByCode(transferStock.getSourceWarehouseCode());

                Double transferringQty = transferStock.getTransferQty();
                Stock sourceRec = stockRepository.findByItemCodeAndWarehouseCode(transferStock.getItemCode(),
                        sourceWarehouse.getCode());
                Stock targetRec = stockRepository.findByItemCodeAndWarehouseCode(transferStock.getItemCode(),
                        targetWarehouse.getCode());

                if (sourceRec.getQuantity().compareTo(transferringQty) >= 0) {
                    if (null == targetRec) {
                        Item item = itemService.findOneByItemCode(transferStock.getItemCode());
                        stock.setId(null);
                        stock.setActive(true);
                        stock.setBarcode(item.getBarcode());
                        stock.setItemCode(transferStock.getItemCode());
                        stock.setItemName(item.getItemName());
                        stock.setSellingPrice(item.getSellingPrice());
                        stock.setItemCost(item.getItemCost());
                        stock.setQuantity(transferStock.getTransferQty());
                        if (null != item.getItemCategory()) {
                            stock.setCategoryCode(item.getItemCategory().getCode());
                        }
                        if (null != item.getBrand()) {
                            stock.setBrandCode(item.getBrand().getCode());
                        }
                        stock.setDeadStockLevel(item.getDeadStockLevel());
                        stock.setWarehouseCode(targetWarehouse.getCode());
                        stock.setWarehouseName(targetWarehouse.getName());
                        save(stock, "Creating Stock by Transfer In", transferStock.getItemCode());
                    } else {
                        targetRec.setQuantity(targetRec.getQuantity() + transferringQty);
                        save(targetRec, "Updating Stock by Transfer In", transferStock.getItemCode());
                    }

                    sourceRec.setQuantity(sourceRec.getQuantity() - transferringQty);
                    save(sourceRec, "Updating Stock by Transfer Out", transferStock.getItemCode());
                }

                transferStockRepository.save(transferStock);
            }
            response.setCode(200);
            response.setMessage("Successfully Saved");

            return response;
        } catch (Exception ex) {
            ex.printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Saving sub stock failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Saving Failed");
            return response;
        }
    }

    @Override
    public List<Stock> getDeadStockList() {
        List<Stock> deadStock = new ArrayList<>();
        try {
            List<Stock> stock = stockRepository.findAll();
            stock.forEach(stk -> {
                if (Double.compare(stk.getQuantity(), stk.getDeadStockLevel()) <= 0) {
                    deadStock.add(stk);
                }
            });
            return deadStock;
        } catch (Exception ex) {
            ex.printStackTrace();
            LOGGER.error("getDeadStockList failed");
            return deadStock;
        }
    }

    @Override
    public Page<Stock> findAllStocks(Pageable pageable) {
        try {
            return stockRepository.findAllByWarehouseCode(pageable, userService.findLoggedInUser().getWarehouseCode());
        } catch (Exception ex) {
            LOGGER.error("Retrieving Dead Stock failed");
            return null;
        }
    }

    @Override
    public List<Stock> findAllByBarcodeLike(String code) {
        try {
            return stockRepository.findAllByBarcodeLikeIgnoreCase(code);
        } catch (Exception e) {
            LOGGER.error("Search item Failed: " + e.getMessage());
            return null;
        }
    }

    @Override
    public List<Stock> findAllByNameLike(String name) {
        try {
            List<Stock> items = stockRepository.findAllByItemNameLikeIgnoreCase(name);
            return items;
        } catch (Exception ex) {
            LOGGER.error("Search item Failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Stock> findAllByItemCategory(String code) {
        try {
            List<Stock> stocks = stockRepository.findAllByCategoryCode(code);
            return stocks;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<StockSummary> findStockSummary() {
        try {
            return customStockRepository.findStockSummary();
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Stock> findReorderListByWarehouse(int warehouseCode, Double threshold) {
        try {
            return customStockRepository.findReorderListByWarehouse(warehouseCode, threshold);
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Stock> findReorderList(Double threshold, String catCode, String brandCode) {
        try {
            if (threshold > 0 && !catCode.isEmpty() && brandCode.isEmpty()) {
                return customStockRepository.findReorderListForItemCategory(threshold, catCode);
            } else if (threshold > 0 && catCode.isEmpty() && !brandCode.isEmpty()) {
                return customStockRepository.findReorderListForItemBrand(threshold, brandCode);
            } else if (threshold > 0 && !catCode.isEmpty() && !brandCode.isEmpty()) {
                return customStockRepository.findReorderListForCatAndBrand(threshold, catCode, brandCode);
            } else {
                return customStockRepository.findReorderList(threshold);
            }
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean addStockMovement(String type, String itemCode, Stock newStock) {
        try {
            Stock oldStock = stockRepository.findByItemCodeAndWarehouseCode(
                    newStock.getItemCode(), newStock.getWarehouseCode());
            stockMovement.setId(null);
            if (null != oldStock) {
                stockMovement.setQuantity(newStock.getQuantity() - oldStock.getQuantity());
                stockMovement.setStockCountBefore(oldStock.getQuantity());
                stockMovement.setStockCountAfter(newStock.getQuantity());
            } else {
                stockMovement.setQuantity(newStock.getQuantity());
                stockMovement.setStockCountBefore(0.0);
                stockMovement.setStockCountAfter(newStock.getQuantity());
            }
            stockMovement.setType(type);
            stockMovement.itemCode(itemCode);
            stockMovement.setBarcode(newStock.getBarcode());
            stockMovement.setItemName(newStock.getItemName());
            stockMovement.setWhCode(newStock.getWarehouseCode());
            stockMovement.setDateTime(LocalDateTime.now());
            stockMovementRepository.save(stockMovement);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Find All  Failed " + ex.getMessage());
            return false;
        }
    }

    @Override
    public List<StockMovement> findStockMovementByItemAndDateBetween(String barcode, int whCode,
                                                                     LocalDate fromDate, LocalDate toDate) {
        try {
            return stockMovementRepository.findByBarcodeAndWhCodeAndDateTimeBetween(barcode, whCode,
                    fromDate.atStartOfDay(), toDate.plusDays(1).atStartOfDay());
        } catch (Exception ex) {
            LOGGER.error("Find Stock Movement  Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Stock> findStockByItemCategoryAndWh(String code, int whCode) {
        try {
            List<Stock> stocks = stockRepository.findAllByCategoryCodeAndWarehouseCode(code, whCode);
            return stocks;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Stock> findStockByBrandAndWh(String code, int whCode) {
        try {
            List<Stock> stocks = stockRepository.findAllByBrandCodeAndWarehouseCode(code, whCode);
            return stocks;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    @Transactional
    public Response bulkUpdateStocks(List<Stock> stocks) {
        try {
            if (userService.isAdminOrManagerLoggedIn()) {
                for (Stock updatedStock : stocks) {
                    Stock existingStock = stockRepository.findById(updatedStock.getId()).orElse(null);
                    if (existingStock != null) {
                        Double oldQuantity = existingStock.getQuantity();
                        Double oldDeadStockLevel = existingStock.getDeadStockLevel();

                        // Update quantity if changed
                        if (!oldQuantity.equals(updatedStock.getQuantity())) {
                            existingStock.setQuantity(updatedStock.getQuantity());

                            // Create action record for quantity change
                            MetaData stockAdjustment = metaDataService.searchMetaData("Adjust Stock", "Action");
                            action = new Action();
                            action.setType(stockAdjustment);
                            action.setReference(existingStock.getBarcode());
                            action.setReference2(existingStock.getItemName());
                            if (updatedStock.getQuantity().compareTo(oldQuantity) > 0) {
                                action.setOperator("+");
                            } else {
                                action.setOperator("-");
                            }
                            action.setChange(String.valueOf(updatedStock.getQuantity() - oldQuantity));
                            action.setRemark("Bulk update - quantity change");
                            actionService.save(action);
                        }

                        // Update dead stock level if changed
                        if ((oldDeadStockLevel == null && updatedStock.getDeadStockLevel() != null) ||
                            (oldDeadStockLevel != null && !oldDeadStockLevel.equals(updatedStock.getDeadStockLevel()))) {
                            existingStock.setDeadStockLevel(updatedStock.getDeadStockLevel());
                        }

                        // Save the updated stock
                        save(existingStock, "Bulk Update", existingStock.getItemCode());
                    }
                }

                response.setCode(200);
                response.setMessage("Successfully Updated");
            } else {
                response.setCode(403);
                response.setMessage("No Permission");
            }
            return response;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            response.setCode(501);
            response.setMessage("Update Failed");
            return response;
        }
    }

}
