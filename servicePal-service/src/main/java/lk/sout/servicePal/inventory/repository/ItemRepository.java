package lk.sout.servicePal.inventory.repository;


import lk.sout.servicePal.inventory.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by Madhawa Weerasinghe on 4/21/2018
 */

@Repository
public interface ItemRepository extends MongoRepository<Item, String> {

    List<Item> findAllByItemNameLikeIgnoreCaseAndActive(String name, <PERSON><PERSON><PERSON> activate);

    List<Item> findAllByItemTypeAndItemNameLikeIgnoreCaseAndActive(ItemType itemType, String name, Boolean activate);

    List<Item> findAllByItemTypeAndBarcodeLikeIgnoreCaseAndActive(ItemType itemType, String name, <PERSON>olean activate);

    List<Item> findTop10ByBarcodeLikeIgnoreCaseAndActive(String barcode, Boolean activate);

    List<Item> findAllByItemNameLikeIgnoreCase(String name);

    List<Item> findAllByItemCategory(ItemCategory category);

    List<Item> findAllByBrand(Brand brand);

    Item findByItemCode(String s);

    Item findByBarcode(String s);

    Item findByBarcodeIgnoreCase(String s);
}
