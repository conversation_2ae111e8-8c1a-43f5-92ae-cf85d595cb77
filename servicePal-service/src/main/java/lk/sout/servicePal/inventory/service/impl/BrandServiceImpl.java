package lk.sout.servicePal.inventory.service.impl;

import lk.sout.servicePal.inventory.entity.Brand;
import lk.sout.servicePal.inventory.repository.BrandRepository;
import lk.sout.servicePal.inventory.service.BrandService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class BrandServiceImpl implements BrandService{

    private static final Logger LOGGER = LoggerFactory.getLogger(BrandService.class);

    @Autowired
    BrandRepository brandRepository;

    public boolean save(Brand brand) {
        try {
            brand.setCode(String.valueOf(System.currentTimeMillis()));
            brandRepository.save(brand);
            LOGGER.info("Brand saved. " + brand.getName());
            return true;
        } catch (Exception ex) {
            LOGGER.error("Saving Brand Failed. " + brand.getName());
            return false;
        }
    }

    public boolean remove(Brand brand) {
        try {
            brandRepository.delete(brand);
            LOGGER.info("Brand removed. " + brand.getName());
            return true;
        } catch (Exception ex) {
            LOGGER.error("Removing Brand Failed. " + brand.getName());
            return false;
        }
    }

    public Iterable<Brand> findAll(Pageable pageable) {
        try {
            return brandRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Retrieving All brands failed");
            return null;
        }
    }

    public Brand findOne(String id) {
        try {
            Optional<Brand> brand = brandRepository.findById(id);
            return brand.get();
        } catch (Exception ex) {
            LOGGER.error("Retrieving a brand failed");
            return null;
        }
    }

    @Override
    public List<Brand> findByName(String key) {
        try {
            return brandRepository.findByNameLikeIgnoreCaseAndActive(key,true);
        } catch (Exception ex) {
            LOGGER.error("Retrieving a brand failed");
            return null;
        }
    }

    @Override
    public String delete(String id) {
        try{
            brandRepository.deleteById(id);
            return "success";
        }catch (Exception ex){
            LOGGER.error("Removing brand failed " + id + ". " + ex.getMessage());
            return "failed";
        }
    }


}
