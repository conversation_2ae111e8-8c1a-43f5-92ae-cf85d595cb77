package lk.sout.servicePal.inventory.controller;

import lk.sout.servicePal.inventory.entity.BatchStockTransfer;
import lk.sout.servicePal.inventory.entity.Stock;
import lk.sout.servicePal.inventory.entity.TransferStock;
import lk.sout.servicePal.inventory.service.StockService;
import lk.sout.servicePal.trade.entity.PurchaseInvoice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@RestController
@RequestMapping("/stock")
public class StockController {

    private final StockService stockService;

    @Autowired
    public StockController(StockService stockService) {
        this.stockService = stockService;
    }

    @RequestMapping(value = "/stockTransfer", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> stockTransfer(@RequestBody TransferStock transferStock) {
        try {
            return ResponseEntity.ok(stockService.transferStock(transferStock));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/batchStockTransfer", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> batchStockTransfer(@RequestBody BatchStockTransfer batchStockTransfer) {
        try {
            return ResponseEntity.ok(stockService.batchStockTransfer(batchStockTransfer));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByWarehouse", method = RequestMethod.GET)
    private ResponseEntity<?> findAllByWarehouse(@RequestParam("warehouseCode") int warehouseCode, @RequestParam("page") String page,
                                                 @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(stockService.findAllByWarehouse(PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize)), warehouseCode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findStockByWarehouseAndBarcodeLike", method = RequestMethod.GET)
    private ResponseEntity<?> findStockByWarehouseAndBarcodeLike(@RequestParam("barcode") String barcode) {
        try {
            return ResponseEntity.ok(stockService.findByUserWarehouseAndBarcodeLike(barcode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByBarcodeAndUserWarehouse", method = RequestMethod.GET)
    private ResponseEntity<?> findByBarcodeAndUserWarehouse(@RequestParam("barcode") String barcode) {
        try {
            return ResponseEntity.ok(stockService.findByBarcodeAndUserWarehouse(barcode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findStockByWarehouseAndItemNameLike", method = RequestMethod.GET)
    private ResponseEntity<?> findStockByWarehouseAndItemNameLike(@RequestParam("name") String itemName) {
        try {
            return ResponseEntity.ok(stockService.findByUserWarehouseAndItemNameLike(itemName));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByBarcodeAndWarehouse", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByBarcodeAndWarehouse(@RequestParam("barcode") String barcode,
                                                        @RequestParam("warehouseCode") int warehouseCode) {
        try {
            return ResponseEntity.ok(stockService.findOneByBarcodeAndWarehouse(barcode, warehouseCode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByBarcodeAndWarehouseLike", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByBarcodeAndWarehouseLike(@RequestParam("barcode") String barcode,
                                                            @RequestParam("warehouseCode") int warehouseCode) {
        try {
            return ResponseEntity.ok(stockService.findByUserWarehouseAndBarcodeLike(warehouseCode, barcode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByNameAndWarehouseLike", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByNameAndWarehouseLike(@RequestParam("name") String name,
                                                         @RequestParam("warehouseCode") int warehouseCode) {
        try {
            return ResponseEntity.ok(stockService.findByUserWarehouseAndItemNameLike(warehouseCode, name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }


    @RequestMapping(value = "/findByItemCodeAndWarehouse", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByItemCodeAndWarehouse(@RequestParam("itemCode") String itemCode,
                                                         @RequestParam("warehouseCode") int warehouseCode) {
        try {
            return ResponseEntity.ok(stockService.findByItemCodeAndWarehouse(itemCode, warehouseCode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/addStockManual", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> addStockManual(@RequestBody PurchaseInvoice pi) {
        try {
            return ResponseEntity.ok(stockService.createByPurchaseInvRecs(pi.getPurchaseInvoiceRecords(), true));
        } catch (Exception ex) {
            ex.printStackTrace();
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }


    @RequestMapping(value = "/adjustStock", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> adjustStock(@RequestParam("stockId") String stockId,
                                          @RequestParam("actualQuantity") Double actualQuantity,
                                          @RequestParam("remark") String remark) {
        try {
            return ResponseEntity.ok(stockService.adjust(stockId, actualQuantity, remark));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllStock", method = RequestMethod.GET)
    private ResponseEntity<?> findAllStock(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(stockService.findAllStocks(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByBarcode", method = RequestMethod.GET)
    private ResponseEntity<?> searchByBarcode(@RequestParam("barcode") String code) {
        try {
            return ResponseEntity.ok(stockService.findAllByBarcodeLike(code));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByNameLike", method = RequestMethod.GET)
    private ResponseEntity<?> findAllByNameLike(@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(stockService.findAllByNameLike(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllStockByItemCategory", method = RequestMethod.GET)
    private ResponseEntity<?> findAllStockByItemCategory(@RequestParam("code") String code) {
        try {
            return ResponseEntity.ok(stockService.findAllByItemCategory(code));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findStockSummary", method = RequestMethod.GET)
    private ResponseEntity<?> findStockSummary() {
        try {
            return ResponseEntity.ok(stockService.findStockSummary());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findReorderListByWarehouse", method = RequestMethod.GET)
    private ResponseEntity<?> findReorderListByWarehouse(@RequestParam("warehouseCode") int warehouseCode,
                                                         @RequestParam("threshold") Double threshold) {
        try {
            return ResponseEntity.ok(stockService.findReorderListByWarehouse(warehouseCode, threshold));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findReorderList", method = RequestMethod.GET)
    private ResponseEntity<?> findReorderList(@RequestParam("threshold") Double threshold,
                                              @RequestParam("catCode") String catCode,
                                              @RequestParam("brandCode") String brandCode) {
        try {
            return ResponseEntity.ok(stockService.findReorderList(threshold, catCode, brandCode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findStockMovement", method = RequestMethod.GET)
    private ResponseEntity<?> findStockMovementByItemAndDateBetween(@RequestParam("barcode") String barcode,
                                                                    @RequestParam("whCode") int whCode,
                                                                    @RequestParam("sDate") String sDate,
                                                                    @RequestParam("eDate") String eDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
            return ResponseEntity.ok(stockService.findStockMovementByItemAndDateBetween(barcode, whCode,
                    LocalDate.parse(sDate, formatter),
                    LocalDate.parse(eDate, formatter)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findStockByItemCategoryAndWh", method = RequestMethod.GET)
    private ResponseEntity<?> findStockByItemCategoryAndWh(@RequestParam("catCode") String code,
                                                           @RequestParam("whCode") int whCode) {
        try {
            return ResponseEntity.ok(stockService.findStockByItemCategoryAndWh(code, whCode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findStockByBrandAndWh", method = RequestMethod.GET)
    private ResponseEntity<?> findStockByBrandAndWh(@RequestParam("brandCode") String code,
                                                    @RequestParam("whCode") int whCode) {
        try {
            return ResponseEntity.ok(stockService.findStockByBrandAndWh(code, whCode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/bulkUpdateStocks", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> bulkUpdateStocks(@RequestBody List<Stock> stocks) {
        try {
            return ResponseEntity.ok(stockService.bulkUpdateStocks(stocks));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

}
