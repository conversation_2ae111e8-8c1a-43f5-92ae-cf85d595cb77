package lk.sout.servicePal.inventory.service.impl;

import lk.sout.servicePal.inventory.entity.ItemCategory;
import lk.sout.servicePal.inventory.repository.ItemCategoryRepository;
import lk.sout.servicePal.inventory.service.ItemCategoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ItemCategoryServiceImpl implements ItemCategoryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ItemCategoryServiceImpl.class);

    @Autowired
    ItemCategoryRepository itemCategoryRepository;

    @Override
    public boolean save(ItemCategory itemCategory) {
        try {
            itemCategory.setCode(String.valueOf(System.currentTimeMillis()));
            itemCategoryRepository.save(itemCategory);
            LOGGER.info("Item Category saved. " + itemCategory.getCategoryName());
            return true;
        } catch (Exception ex) {
            LOGGER.error("Saving Item Category Failed : " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<ItemCategory> findAll(Pageable pageable) {

        return itemCategoryRepository.findAll(pageable);
    }


    @Override
    public boolean remove(String id) {
        try {
            itemCategoryRepository.deleteById(id);
            LOGGER.info("item Category removed. ");
            return true;
        } catch (Exception ex) {
            LOGGER.error("Removing item Category Failed :" + ex.getMessage());
            return false;
        }
    }

    @Override
    public List<ItemCategory> findByName(String name) {
        try {
            return itemCategoryRepository.findItemCategoryByCategoryNameLikeIgnoreCaseAndActive( name,true);

        } catch (Exception e) {
            LOGGER.error("ItemCategory  Searching failed :" + e.getMessage());
            return null;
        }
    }

    @Override
    public int getCount() {
        return (int) itemCategoryRepository.count();
    }

    @Override
    public List<ItemCategory> findAllCategories() {
        try {
            List<ItemCategory> itemCategories =  itemCategoryRepository.findAll();
            return itemCategories;
        } catch (Exception ex) {
            LOGGER.error("Retrieving All Item Categories failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public ItemCategory findById(String id) {
        try {
            return itemCategoryRepository.findById(id).get();

        } catch (Exception e) {
            LOGGER.error("ItemCategory  Searching failed :" + e.getMessage());
            return null;
        }
    }


}
