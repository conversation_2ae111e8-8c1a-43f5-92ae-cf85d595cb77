package lk.sout.servicePal.inventory.repository;

import lk.sout.servicePal.inventory.entity.Stock;
import lk.sout.servicePal.inventory.entity.StockSummary;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasing<PERSON> on 4/22/2020
 */
@Repository
public class CustomStockRepository {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomStockRepository.class);

    @Autowired
    MongoTemplate mongoTemplate;

    public List<StockSummary> findStockSummary() {
        try {
            TypedAggregation<Stock> agg = newAggregation(Stock.class,
                    project("quantity", "sellingPrice", "itemCost", "warehouseName").
                            andExpression("quantity * sellingPrice").as("tempPriceValue").
                            andExpression("quantity * itemCost").as("tempCostValue"),
                    group("warehouseName").
                            count().as("noOfItems").sum("quantity").as("totalQuantity").
                            sum("tempPriceValue").as("totalPriceValue").sum("tempCostValue").
                            as("totalCostValue"),
                    project("warehouseName", "noOfItems", "totalQuantity", "totalPriceValue", "totalCostValue").
                            and("warehouseName").previousOperation()
            );
            AggregationResults<StockSummary> groupResults
                    = mongoTemplate.aggregate(agg, StockSummary.class);
            List<StockSummary> result = groupResults.getMappedResults();
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public List<Stock> findReorderListByWarehouse(int warehouseCode, Double threshold) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("warehouseCode").is(warehouseCode));
            query.addCriteria(Criteria.where("quantity").lte(threshold));
            query.fields().include("barcode").include("itemName").include("itemCode").
                    include("sellingPrice").include("quantity").include("deadStockLevel").
                    include("itemCost");
            return mongoTemplate.find(query, Stock.class);
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public List<Stock> findReorderList(Double threshold) {
        try {
            TypedAggregation<Stock> agg = newAggregation(Stock.class,
                    project("quantity", "itemCost", "itemName", "deadStockLevel", "itemCode", "barcode",
                            "categoryCode", "brandCode"),
                    group("itemCode").
                            sum("quantity").as("quantity").
                            max("itemCost").as("itemCost").max("itemName").as("itemName").
                            max("deadStockLevel").as("deadStockLevel").max("barcode").as("barcode"),
                    match(Criteria.where("quantity").lte(threshold)),
                    project("quantity", "itemCost", "itemName", "deadStockLevel", "itemCode", "barcode").
                            and("itemCode").previousOperation()
            );
            AggregationResults<Stock> groupResults
                    = mongoTemplate.aggregate(agg, Stock.class);
            List<Stock> result = groupResults.getMappedResults();
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public List<Stock> findReorderListForItemCategory(Double threshold, String catCode) {
        try {
            TypedAggregation<Stock> agg = newAggregation(Stock.class,
                    project("quantity", "itemCost", "itemName", "deadStockLevel", "itemCode", "barcode",
                            "categoryCode", "brandCode"),
                    match(Criteria.where("categoryCode").is(catCode)),
                    group("itemCode").
                            sum("quantity").as("quantity").
                            max("itemCost").as("itemCost").max("itemName").as("itemName").
                            max("deadStockLevel").as("deadStockLevel").max("barcode").as("barcode"),
                    match(Criteria.where("quantity").lte(threshold)),
                    project("quantity", "itemCost", "itemName", "deadStockLevel", "itemCode", "barcode").
                            and("itemCode").previousOperation()
            );
            AggregationResults<Stock> groupResults
                    = mongoTemplate.aggregate(agg, Stock.class);
            List<Stock> result = groupResults.getMappedResults();
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public List<Stock> findReorderListForItemBrand(Double threshold, String brandCode) {
        try {
            TypedAggregation<Stock> agg = newAggregation(Stock.class,
                    project("quantity", "itemCost", "itemName", "deadStockLevel", "itemCode", "barcode",
                            "categoryCode", "brandCode"),
                    match(Criteria.where("brandCode").is(brandCode)),
                    group("itemCode").
                            sum("quantity").as("quantity").
                            max("itemCost").as("itemCost").max("itemName").as("itemName").
                            max("deadStockLevel").as("deadStockLevel").max("barcode").as("barcode"),
                    match(Criteria.where("quantity").lte(threshold)),
                    project("quantity", "itemCost", "itemName", "deadStockLevel", "itemCode", "barcode").
                            and("itemCode").previousOperation()
            );
            AggregationResults<Stock> groupResults
                    = mongoTemplate.aggregate(agg, Stock.class);
            List<Stock> result = groupResults.getMappedResults();
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public List<Stock> findReorderListForCatAndBrand(Double threshold, String catCode, String brandCode) {
        try {
            TypedAggregation<Stock> agg = newAggregation(Stock.class,
                    project("quantity", "itemCost", "itemName", "deadStockLevel", "itemCode", "barcode",
                            "categoryCode", "brandCode"),
                    match(Criteria.where("categoryCode").is(catCode)),
                    match(Criteria.where("brandCode").is(brandCode)),
                    group("itemCode").
                            sum("quantity").as("quantity").
                            max("itemCost").as("itemCost").max("itemName").as("itemName").
                            max("deadStockLevel").as("deadStockLevel").max("barcode").as("barcode"),
                    match(Criteria.where("quantity").lte(threshold)),
                    project("quantity", "itemCost", "itemName", "deadStockLevel", "itemCode", "barcode").
                            and("itemCode").previousOperation()
            );
            AggregationResults<Stock> groupResults
                    = mongoTemplate.aggregate(agg, Stock.class);
            List<Stock> result = groupResults.getMappedResults();
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }


}
