package lk.sout.servicePal.inventory.service;

import lk.sout.servicePal.inventory.entity.Brand;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface BrandService {

    boolean save(Brand brand);

    boolean remove(Brand brand);

    Iterable<Brand> findAll(Pageable pageable);

    Brand findOne(String id);

    List<Brand> findByName(String key);

    String delete(String id);
}
