package lk.sout.servicePal.inventory.service;

import lk.sout.servicePal.inventory.entity.ItemCategory;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ItemCategoryService {

    boolean save(ItemCategory itemCategory);

    Iterable<ItemCategory> findAll(Pageable pageable);

    boolean remove(String id);

    List<ItemCategory> findByName(String name);

    int getCount();

    List<ItemCategory> findAllCategories();

    ItemCategory findById(String id);
}
