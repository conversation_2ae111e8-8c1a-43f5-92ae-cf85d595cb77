package lk.sout.servicePal.inventory.service;

import lk.sout.servicePal.inventory.entity.Rack;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface RackService {

    boolean save(Rack rack);

    boolean remove(Rack rack);

    Iterable<Rack> findAll(Pageable pageable);

    List<Rack> findAllRacks();

    Rack findOne(String id);

    List<Rack> findByRackNo(String key);

}
