/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package lk.sout.servicePal.inventory.repository;

import lk.sout.servicePal.inventory.entity.Stock;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface StockRepository extends MongoRepository<Stock, String> {

    Stock findByBarcodeAndWarehouseCode(String barcode, int no);

    @Query(fields = "{'barcode':1,'quantity':1,'itemName':1,'itemCode':1,'sellingPrice':1, 'itemCost':1}")
    List<Stock> findTop15ByWarehouseCodeAndBarcodeLikeIgnoreCaseAndActiveOrderBySellingPrice(int no, String barcode, boolean active);

    @Query(fields = "{'barcode':1,'quantity':1,'itemName':1,'itemCode':1,'sellingPrice':1, 'itemCost':1}")
    List<Stock> findByWarehouseCodeAndBarcodeLikeIgnoreCaseAndActiveOrderBySellingPrice(int no, String barcode, boolean active);

    @Query(fields = "{'barcode':1,'quantity':1,'itemName':1,'itemCode':1,'sellingPrice':1, 'itemCost':1}")
    List<Stock> findTop15ByWarehouseCodeAndItemNameLikeIgnoreCaseAndActiveOrderBySellingPrice(int no, String itemName, boolean active);

    @Query(fields = "{'barcode':1,'quantity':1,'itemName':1,'itemCode':1,'sellingPrice':1, 'itemCost':1}")
    List<Stock> findByWarehouseCodeAndItemNameLikeIgnoreCaseAndActiveOrderBySellingPrice(int no, String itemName, boolean active);

    Stock findByItemCodeAndWarehouseCode(String itemCode, int no);

    List<Stock> findByItemCode(String itemCode);

    //  @Query(fields = "{'barcode':1,'quantity':1,'sellingPrice':1,'itemCost':1,'warehouseName':1}")
    Page<Stock> findAllByWarehouseCode(Pageable pageable, int no);

    List<Stock> findByWarehouseCode(String id);

    List<Stock> findAllByBarcodeLikeIgnoreCase(String barcode);

    Stock findByBarcode(String barcode);

    List<Stock> findAllByItemNameLikeIgnoreCase(String name);

    List<Stock> findAllByCategoryCode(String code);

    List<Stock> findAllByCategoryCodeAndWarehouseCode(String code, int whCode);

    List<Stock> findAllByBrandCodeAndWarehouseCode(String code, int whCode);

}
