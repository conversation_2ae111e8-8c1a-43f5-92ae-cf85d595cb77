package lk.sout.servicePal.inventory.service;

import lk.sout.servicePal.inventory.entity.Warehouse;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface WarehouseService {

    boolean save(Warehouse warehouse);

    List<Warehouse> findAll();

    List<Warehouse> findByNameLike(String name);

    Warehouse findByName(String name);

    Warehouse findById(String id);

    Warehouse findByCode(int code);

    String delete(String id);

    List<Warehouse> findAllByName(String name);

    Iterable<Warehouse> findAll(Pageable pageable);
}
