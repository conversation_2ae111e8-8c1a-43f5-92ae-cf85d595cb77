package lk.sout.servicePal.inventory.repository;

import lk.sout.servicePal.inventory.entity.Brand;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 4/18/2018
 */
@Repository
public interface BrandRepository extends MongoRepository<Brand, String> {

    List<Brand> findByNameLikeIgnoreCaseAndActive(String key, Boolean active);

}
