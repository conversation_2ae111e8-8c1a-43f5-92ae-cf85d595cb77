package lk.sout.servicePal.trade.service;

import lk.sout.servicePal.trade.entity.Cashier;

import java.util.List;

public interface CashierService {

    boolean save(Cashier cashier);

    boolean topUpCashier(Double topUpAmount, String counterId);

    boolean deductFromCashier(Double amount, String counterId);

    boolean correctCashier(Double amount, String counterId);

    List<Cashier> findAll();

    Cashier findByCounter(String counter);

    boolean checkCounterStatus(String counter);

}
