package lk.sout.servicePal.trade.service.impl;

import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import lk.sout.core.entity.Transaction;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.TransactionService;
import lk.sout.servicePal.trade.entity.Cheque;
import lk.sout.servicePal.trade.entity.PurchaseInvoice;
import lk.sout.servicePal.trade.entity.SalesInvoice;
import lk.sout.servicePal.trade.repository.ChequeRepository;
import lk.sout.servicePal.trade.service.ChequeService;
import lk.sout.servicePal.trade.service.PurchaseInvoiceService;
import lk.sout.servicePal.trade.service.SalesInvoiceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.List;

@Service
public class ChequeServiceImpl implements ChequeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ChequeServiceImpl.class);

    @Autowired
    ChequeRepository chequeRepository;

    @Autowired
    MetaData status;

    @Autowired
    TransactionService transactionService;

    @Autowired
    SalesInvoiceService salesInvoiceService;

    @Autowired
    Transaction transaction;

    @Autowired
    Response response;

    @Autowired
    SalesInvoice salesInvoice;

    @Autowired
    PurchaseInvoice purchaseInvoice;

    @Autowired
    PurchaseInvoiceService purchaseInvoiceService;

    @Autowired
    MetaDataService metaDataService;

    DateFormat dateFormat = new SimpleDateFormat("MMM dd, yyyy");

    @Override
    public Response save(Cheque cheque) {
        try {
            if (cheque.getId() != null) {
                checkPaymentUpdate(cheque);
            } else {
                status = metaDataService.searchMetaData("Pending", "ChequeStatus");
                cheque.setStatus(status);
                Cheque cheque1 = chequeRepository.save(cheque);
                if (cheque1.getId() != null) {
                    response.setData(cheque.getChequeNo());
                    response.setCode(200);
                    response.setMessage("Cheque Payment Created Successfully");
                }
            }

        } catch (Exception e) {
            LOGGER.error("Creating Cheque Payment Failed on line " + e.getStackTrace()[0].getLineNumber() + " : " +
                    e.getMessage());
            response.setCode(501);
            response.setMessage("Creating Cheque Payment Failed");
            response.setData(e.getMessage());
        }
        return response;
    }

    @Override
    public Response updateCheque(String id, String comment, boolean isDeposit) {
        try {
            Cheque cheque = chequeRepository.findChequeById(id);
            MetaData depositStatus = metaDataService.searchMetaData("Deposited", "ChequeStatus");
            MetaData returnStatus = metaDataService.searchMetaData("Returned", "ChequeStatus");
            if (isDeposit){
                cheque.setStatus(depositStatus);
                cheque.setComment(comment);
                chequeRepository.save(cheque);
                response.setCode(200);
                response.setMessage("Cheque Deposited");
            }else {
                cheque.setStatus(returnStatus);
                cheque.setComment(comment);
                chequeRepository.save(cheque);
                response.setCode(200);
                response.setMessage("Cheque Returned");
            }
            return response;
        }catch (Exception e){
            response.setCode(500);
            response.setMessage("Cheque Updating Failed");
            return response;
        }
    }

    private void checkPaymentUpdate(Cheque cheque) {

//        if (cheque.getStatus() === )

/*        status = metaDataService.searchMetaData("Deposit", "ChequeStatus");
        MetaData siType = metaDataService.searchMetaData("SalesInvoice", "Income");
        MetaData piType = metaDataService.searchMetaData("PurchaseInvoice", "Expense");


        if (cheque.getStatus().getValue().equals(status.getValue())) {
            if (cheque.getRefType().equals("SalesInvoice")) {
                salesInvoice = new SalesInvoice();
                salesInvoice = salesInvoiceService.findByInvNo(cheque.getRefNo());
                double payment = cheque.getAmount() + salesInvoice.getPayment();

                if (payment >= salesInvoice.getTotalAmount()) {
                    salesInvoice.setPayment(salesInvoice.getTotalAmount());
                    salesInvoice.setBalance(Double.valueOf(0));
                } else {
                    salesInvoice.setPayment(payment);
                    double balance = payment - salesInvoice.getTotalAmount();
                    salesInvoice.setBalance(balance * (-1));
                }
                if (salesInvoice.getTotalAmount() <= salesInvoice.getPayment()) {
                    salesInvoice.setStatus(metaDataService.searchMetaData("Completed", "Status"));
                    salesInvoice.setPaymentMethod(metaDataService.searchMetaData("Cheque", "PaymentMethod"));
                } else {
                    salesInvoice.setStatus(metaDataService.searchMetaData("partial paid", "Status"));
                    salesInvoice.setPaymentMethod(metaDataService.searchMetaData("Credit", "PaymentMethod"));
                }

                salesInvoiceService.save(salesInvoice);

                transaction = new Transaction();
                transaction.setAmount(cheque.getAmount());
                transaction.setOperator("+");
                transaction.setRefNo(salesInvoice.getInvoiceNo());
                transaction.setRefType("Sales Invoice");
                //transaction.setThirdParty(salesInvoice.getCustomer().getId());
                transaction.setType(siType);
                transactionService.save(transaction);

                cheque.setStatus(status);
                Cheque cheque1 = chequeRepository.save(cheque);
                if (cheque1.getId() != null) {
                    response.setData(cheque.getChequeNo());
                    response.setCode(200);
                    response.setMessage("Cheque Payment Deposited Successfully");
                }
            } else if (cheque.getRefType().equals("PurchaseInvoice")) {

                purchaseInvoice = new PurchaseInvoice();
                purchaseInvoice = purchaseInvoiceService.findByPurchaseInvoiceNo(cheque.getRefNo());
                double amount = purchaseInvoice.getPayment() + cheque.getAmount();
                if (amount >= purchaseInvoice.getTotalAmount()) {
                    purchaseInvoice.setPayment(purchaseInvoice.getTotalAmount());
                    purchaseInvoice.setBalance(Double.valueOf(0));
                } else {
                    purchaseInvoice.setPayment(amount);
                    double balance = amount - purchaseInvoice.getTotalAmount();
                    purchaseInvoice.setBalance(balance * (-1));
                }
                if (purchaseInvoice.getTotalAmount() <= purchaseInvoice.getPayment()) {
                    purchaseInvoice.setStatus(metaDataService.searchMetaData("Completed", "Status"));
                } else {
                    purchaseInvoice.setStatus(metaDataService.searchMetaData("partial paid", "Status"));
                }

                purchaseInvoiceService.save(purchaseInvoice);

                transaction = new Transaction();
                transaction.setAmount(cheque.getAmount());
                transaction.setOperator("-");
                transaction.setRefNo(purchaseInvoice.getPurchaseInvoiceNo());
                transaction.setRefType("Purchase Invoice");
                transaction.setThirdParty(purchaseInvoice.getSupplier().getId());
                transaction.setDate(LocalDateTime.now());
                transaction.setType(piType);
                transactionService.save(transaction);

                cheque.setStatus(status);
                Cheque cheque1 = chequeRepository.save(cheque);
                if (cheque1.getId() != null) {
                    response.setData(cheque.getChequeNo());
                    response.setCode(200);
                    response.setMessage("Cheque Payment Deposited Successfully");
                }
            }


        } else {

            Cheque cheque1 = chequeRepository.save(cheque);
            if (cheque1.getId() != null) {
                response.setData(cheque.getChequeNo());
                response.setCode(200);
                response.setMessage("Cheque Payment Created Successfully");
            }
        }
 */
    }

    @Override
    public void update(String salesInvoiceId, String refType) {
        Cheque cheque1 = chequeRepository.findTopByOrderByIdDesc().get(0);
//        cheque1.setRefNo(salesInvoiceId);
//        cheque1.setRefType(refType);
        chequeRepository.save(cheque1);
    }

    @Override
    public List<Cheque> findAll() {
        try {
            // Pageable p = new PageRequest(0, 20);
            //  return chequeRepository.findAll(p).getContent();
            return chequeRepository.findAll();
        } catch (Exception ex) {
            LOGGER.error("Find All Cheque Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Cheque> findAllByOrderByIdDesc() {
        try {
            return chequeRepository.findAllByOrderByIdDesc();
        } catch (Exception ex) {
            LOGGER.error("Find All Item Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Cheque findById(String id) {
        return chequeRepository.findById(id).get();
    }

    @Override
    public List<Cheque> findAllByChequeNo(String chequeNo) {
        return chequeRepository.findAllByChequeNoLike(chequeNo);
    }

    @Override
    public List<Cheque> findAllByRefTypeLike(String refType) {
//        return chequeRepository.findAllByRefTypeLikeIgnoreCase(refType);
        return null;
    }

    @Override
    public List<Cheque> findAllByDate(LocalDate date) {
        try {
//            return chequeRepository.findAllByChequeDate(date);
            return null;
        } catch (Exception e) {
            LOGGER.error("Find All Cheque Failed " + e.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Cheque> findAllPending(Pageable pageable) {
        try{
            MetaData status = metaDataService.searchMetaData("Pending", "ChequeStatus");
            Iterable<Cheque> cheques = chequeRepository.findAllByStatusId(status.getId() ,pageable);
            return cheques;
        }catch (Exception e){
            return null;
        }
    }

    @Override
    public List<Cheque> findAllByStatus(String chequeStatusId) {
        try{
            List<Cheque> chequeList = chequeRepository.findAllByStatusId(chequeStatusId);
            return chequeList;
        }catch (Exception e){
            return null;
        }
    }

    @Override
    public List<Cheque> findAllByBank(String bankId) {
        try {
            List<Cheque> chequeList = chequeRepository.findAllByBankId(bankId);
            return chequeList;
        }catch (Exception e){
            return null;
        }
    }

    @Override
    public List<Cheque> findAllByCustomer(String customerId) {
        try{
            List<Cheque> chequeList = chequeRepository.findAllByCustomerId(customerId);
            return chequeList;
        }catch (Exception e){
            return null;
        }
    }

    @Override
    public Integer loadAvailableChequeQty() {
        try{
            MetaData pendingStatus = metaDataService.searchMetaData("Pending", "ChequeStatus");
            List<Cheque> chequeList = chequeRepository.findAllByChequeDateBetweenAndStatus(LocalDate.now().atStartOfDay(), LocalDate.now().plusDays(1), pendingStatus.getId());
            return chequeList.size();
        }catch (Exception e){
            return null;
        }
    }


}
