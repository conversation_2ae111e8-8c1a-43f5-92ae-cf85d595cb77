package lk.sout.servicePal.trade.service.impl;

import lk.sout.core.entity.Response;
import lk.sout.core.service.MetaDataService;
import lk.sout.servicePal.trade.entity.Customer;
import lk.sout.servicePal.trade.repository.CustomerRepository;
import lk.sout.servicePal.trade.service.CustomerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by <PERSON><PERSON>a Weerasinghe on 12/15/2019
 */

@Service
public class CustomerServiceImpl implements CustomerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomerServiceImpl.class);

    @Autowired
    CustomerRepository customerRepository;

    @Autowired
    Response response;

    @Autowired
    MetaDataService metaDataService;

    @Override
    public boolean save(Customer customer) {
        try {
            customerRepository.save(customer);
            response.setCode(200);
            response.setMessage("Customer Created Successfully");
            return true;
        } catch (Exception ex) {
            LOGGER.error("Creating Customer Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Creating Customer Failed");
            response.setData(ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<Customer> findAll(Pageable pageable) {
        try {
            return customerRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Customers Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Customer> findAllActive(boolean result) {
        try {
            return customerRepository.findAllByActive(result);
        } catch (Exception ex) {
            LOGGER.error("Find All Item Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Customer findById(String id) {
        try {
            return customerRepository.findById(id).get();
        } catch (Exception ex) {
            LOGGER.error("Find All Item Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Customer findByNicBr(String nicBr) {
        try {
            return customerRepository.findByNicBr(nicBr);
        } catch (Exception ex) {
            LOGGER.error("Find by NIC BR Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Customer> findByNicLike(String nic) {
        try {
            return customerRepository.findByNicBrLike(nic);
        } catch (Exception ex) {
            LOGGER.error("Find by NIC BR Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Customer> findByTpLike(String tp) {
        try {
            return customerRepository.findByTelephone1Like(tp);
        } catch (Exception ex) {
            LOGGER.error("Find by NIC BR Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Customer> findAllByNameLikeIgnoreCaseAndActive(String barcode, Boolean active) {
        return customerRepository.findAllByNameLikeIgnoreCase(barcode);
    }

    public Customer findDefaultCustomer() {
        Customer customer = customerRepository.findByName("Default Customer");
        return customer;
    }

    @Override
    public boolean checkNic(String nic) {
        try {
            if (null != customerRepository.findByNicBr(nic)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            LOGGER.error("check Customer by nic Failed: " + ex.getMessage());
            return false;
        }
    }

}
