package lk.sout.servicePal.trade.repository;

import lk.sout.servicePal.trade.entity.Customer;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 12/15/2019
 */
@Repository
public interface CustomerRepository extends MongoRepository<Customer, String> {

    Customer findByName(String defaultCustomer);

    List<Customer> findAllByActive(boolean active);

    List<Customer> findAllByNameLikeIgnoreCase(String name);

    Customer findByNicBr(String nic);

    List<Customer> findByNicBrLike(String nic);

    List<Customer> findByTelephone1Like(String tp);
}
