package lk.sout.servicePal.trade.entity;

import lk.sout.config.CascadeSave;
import lk.sout.core.entity.MetaData;
import lk.sout.servicePal.inventory.entity.Item;
import org.springframework.data.annotation.*;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Document
@Component
public class SalesInvoice {

    @Id
    private String id;

    @Indexed(unique = true)
    private String invoiceNo;

    private String jobNo;

    private LocalDateTime date;

    private LocalDateTime paidDate;

    @DBRef
    private List<Item> services;

    @DBRef
    private Item transport;

    private Double partsCharge;

    private Double serviceCharge;

    private Double transportCharge;

    private Double subTotal;

    private Double totalAmount;

    private Double cashlessAmount;

    private Double cashAmount;

    private String cardOrVoucherNo;

    // use for storing credit balance
    private Double balance;

    // use for storing cash balance gave to customer when he over paid.
    private Double cashBalance;

    private Double totalDiscount;

    private Double advancePayment;

    private Double payment;

    private String counter;

    @DBRef
    private MetaData paymentMethod;

    private LocalDate dueDate;

    @DBRef
    private MetaData status;

    @DBRef
    @CascadeSave
    private List<SalesInvoiceRecord> salesInvoiceRecords;

    private String customerName;

    @DBRef
    private Customer customer;

    private boolean directMode;

    private int warehouseCode;

    @CreatedDate
    private LocalDateTime createdDate;

    @CreatedBy
    private String createdBy;

    @LastModifiedDate
    private Date lastModifiedDate;

    @LastModifiedBy
    private String lastModifiedBy;

    public SalesInvoice() {
    }

    public String getId() {
        return id;
    }

    public String getJobNo() {
        return jobNo;
    }

    public void setJobNo(String jobNo) {
        this.jobNo = jobNo;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public List<SalesInvoiceRecord> getSalesInvoiceRecords() {
        return salesInvoiceRecords;
    }

    public void setSalesInvoiceRecords(List<SalesInvoiceRecord> salesInvoiceRecords) {
        this.salesInvoiceRecords = salesInvoiceRecords;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Double getBalance() {
        return balance;
    }

    public void setBalance(Double balance) {
        this.balance = balance;
    }

    public Double getTotalDiscount() {
        return totalDiscount;
    }

    public void setTotalDiscount(Double totalDiscount) {
        this.totalDiscount = totalDiscount;
    }

    public Double getPayment() {
        return payment;
    }

    public void setPayment(Double payment) {
        this.payment = payment;
    }

    public MetaData getStatus() {
        return status;
    }

    public void setStatus(MetaData status) {
        this.status = status;
    }

    public Customer getCustomer() {
        return customer;
    }

    public void setCustomer(Customer customer) {
        this.customer = customer;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public String getCounter() {
        return counter;
    }

    public void setCounter(String counter) {
        this.counter = counter;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public MetaData getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(MetaData paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getCustomerName() {
        return customer.getName();
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public LocalDateTime getDate() {
        return date;
    }

    public void setDate(LocalDateTime date) {
        this.date = date;
    }

    public LocalDate getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDate dueDate) {
        this.dueDate = dueDate;
    }

    public Double getCashBalance() {
        return cashBalance;
    }

    public void setCashBalance(Double cashBalance) {
        this.cashBalance = cashBalance;
    }

    public Double getSubTotal() {
        return subTotal;
    }

    public void setSubTotal(Double subTotal) {
        this.subTotal = subTotal;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public Double getPartsCharge() {
        return partsCharge;
    }

    public void setPartsCharge(Double partsCharge) {
        this.partsCharge = partsCharge;
    }

    public Item getTransport() {
        return transport;
    }

    public void setTransport(Item transport) {
        this.transport = transport;
    }

    public List<Item> getServices() {
        return services;
    }

    public void setServices(List<Item> services) {
        this.services = services;
    }

    public Double getServiceCharge() {
        return serviceCharge;
    }

    public void setServiceCharge(Double serviceCharge) {
        this.serviceCharge = serviceCharge;
    }

    public Double getTransportCharge() {
        return transportCharge;
    }

    public void setTransportCharge(Double transportCharge) {
        this.transportCharge = transportCharge;
    }

    public boolean isDirectMode() {
        return directMode;
    }

    public void setDirectMode(boolean directMode) {
        this.directMode = directMode;
    }

    public Double getAdvancePayment() {
        return advancePayment;
    }

    public void setAdvancePayment(Double advancePayment) {
        this.advancePayment = advancePayment;
    }

    public LocalDateTime getPaidDate() {
        return paidDate;
    }

    public void setPaidDate(LocalDateTime paidDate) {
        this.paidDate = paidDate;
    }

    public Double getCashlessAmount() {
        return cashlessAmount;
    }

    public void setCashlessAmount(Double cashlessAmount) {
        this.cashlessAmount = cashlessAmount;
    }

    public Double getCashAmount() {
        return cashAmount;
    }

    public void setCashAmount(Double cashAmount) {
        this.cashAmount = cashAmount;
    }

    public String getCardOrVoucherNo() {
        return cardOrVoucherNo;
    }

    public void setCardOrVoucherNo(String cardOrVoucherNo) {
        this.cardOrVoucherNo = cardOrVoucherNo;
    }

    public int getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(int warehouseCode) {
        this.warehouseCode = warehouseCode;
    }
}
