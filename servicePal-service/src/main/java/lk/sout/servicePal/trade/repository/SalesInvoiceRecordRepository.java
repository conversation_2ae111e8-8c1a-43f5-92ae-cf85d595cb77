package lk.sout.servicePal.trade.repository;

import lk.sout.servicePal.inventory.entity.Item;
import lk.sout.servicePal.trade.entity.SalesInvoiceRecord;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface SalesInvoiceRecordRepository extends MongoRepository<SalesInvoiceRecord,String> {

    List<SalesInvoiceRecord> findAllByItemAndDate(Item item, LocalDateTime date);

    List<SalesInvoiceRecord> findAllByItemAndDateBetween(Item item, LocalDateTime startDate, LocalDateTime endDate);

    List<SalesInvoiceRecord> findAllByItem(Item item);
}
