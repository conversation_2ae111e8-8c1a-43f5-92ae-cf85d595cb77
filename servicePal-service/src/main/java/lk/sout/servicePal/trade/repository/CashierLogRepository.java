package lk.sout.servicePal.trade.repository;

import lk.sout.core.entity.MetaData;
import lk.sout.servicePal.trade.entity.CashierLog;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface CashierLogRepository extends MongoRepository<CashierLog, String> {

    List<CashierLog> findByDateTimeBetween(LocalDateTime sDate, LocalDateTime eDate);

    List<CashierLog> findByCounterAndDateTimeBetween(String counter, LocalDateTime sDate, LocalDateTime eDate);

    List<CashierLog> findByCounterAndTypeAndDateTimeBetween(String counter, MetaData type, LocalDateTime sDate,
                                                            LocalDateTime eDate);

    List<CashierLog> findByTypeAndDateTimeBetween(MetaData type, LocalDateTime sDate, LocalDateTime eDate);

}
