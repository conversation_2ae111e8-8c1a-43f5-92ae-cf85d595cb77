package lk.sout.servicePal.trade.controller;

import lk.sout.servicePal.trade.service.CashierHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/cashierHistory")
public class CashierHistoryController {

    @Autowired
    CashierHistoryService cashierHistoryService;

    @RequestMapping(value = "/dayClose", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> dayClose(@RequestParam Double actualAmount, @RequestParam Double withdrawalAmount,
                                       @RequestParam Double balance) {
        try {
            return ResponseEntity.ok(cashierHistoryService.dayClose(actualAmount, withdrawalAmount, balance));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}

