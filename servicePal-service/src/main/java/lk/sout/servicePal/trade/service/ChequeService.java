package lk.sout.servicePal.trade.service;

import lk.sout.core.entity.Response;
import lk.sout.servicePal.trade.entity.Cheque;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasing<PERSON> on 1/6/2020
 */
public interface ChequeService {

    Response save(Cheque cheque);

    Response updateCheque(String id, String comment, boolean isDeposit);

    void update(String salesInvoiceId, String refType);

    List<Cheque> findAll();

    List<Cheque> findAllByOrderByIdDesc();

    Cheque findById(String id);

    List<Cheque> findAllByChequeNo(String chequeNo);

    List<Cheque> findAllByRefTypeLike(String refType);

    List<Cheque> findAllByDate(LocalDate date);

    Iterable<Cheque> findAllPending(Pageable pageable);

    List<Cheque> findAllByStatus(String chequeStatusId);

    List<Cheque> findAllByBank(String bankId);

    List<Cheque> findAllByCustomer(String customerId);

    Integer loadAvailableChequeQty();
}
