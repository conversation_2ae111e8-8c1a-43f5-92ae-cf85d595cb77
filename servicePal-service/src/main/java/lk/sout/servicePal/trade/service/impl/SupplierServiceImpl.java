/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package lk.sout.servicePal.trade.service.impl;

import lk.sout.servicePal.trade.entity.Supplier;
import lk.sout.servicePal.trade.repository.SupplierRepository;
import lk.sout.servicePal.trade.service.SupplierService;
import lk.sout.core.entity.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> PC
 */
@Service
public class SupplierServiceImpl implements SupplierService {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(SupplierServiceImpl.class);
    
    @Autowired
    SupplierRepository supplierRepository;
    
    @Autowired
    Response response;
    
    @Override
    public Response save(Supplier supplier) {
        try {
            supplierRepository.save(supplier);
            response.setCode(200);
            response.setMessage("Supplier Created Successfully");
            return response;
        } catch (Exception ex) {
            LOGGER.error("Creating supplier Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Creating supplier Failed");
            response.setData(ex.getMessage());
            return response;
        }
    }
    
    @Override
    public Iterable<Supplier> findAll(Pageable pageable) {
        return  supplierRepository.findAll(pageable);
    }

    @Override
    public List<Supplier> findAllActive(boolean result) {
        try {
            return supplierRepository.findAllByNameNotAndActiveOrderByIdDesc("Default Supplier",result);
        } catch (Exception ex) {
            LOGGER.error("Find All Item Failed " + ex.getMessage());
            return null;
        }
    }
    
    @Override
    public Supplier findBySupplierCode(String supplierCode) {
        return supplierRepository.findByRegNoLikeAndNameNot(supplierCode,"Default Supplier");
    }

    @Override
    public Supplier findById(String id) {
        return supplierRepository.findById(id).get();
    }

    @Override
    public Supplier findByCode(String supplierCode) {
        return supplierRepository.findByRegNo(supplierCode);
    }

    @Override
    public List<Supplier> findByNameLikeIgnoreCase(String supplierName) {
        return supplierRepository.findByNameLikeIgnoreCaseAndRegNoNot(supplierName,"default id");
    }

    @Override
    public boolean findSupplierCode(String text) {
        boolean check = false;
        if (supplierRepository.findByRegNoIgnoreCase(text) != null) {
            check = true;
        } else if (supplierRepository.findByRegNoIgnoreCase(text) == null) {
            check = false;
        }
        return check;
    }

    @Override
    public Supplier findDefaultSupplier() {
        Supplier supplier =  supplierRepository.findByName("Default Supplier");
        return supplier;
    }
}
