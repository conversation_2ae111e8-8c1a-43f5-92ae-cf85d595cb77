package lk.sout.servicePal.trade.service;

import lk.sout.servicePal.trade.entity.Customer;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Created by Madhawa Weerasinghe on 12/15/2019
 */
public interface CustomerService {

    boolean save(Customer customer);

    Iterable<Customer> findAll(Pageable pageable);

    List<Customer> findAllActive(boolean result);

    Customer findByNicBr(String nicBr);

    List<Customer> findByNicLike(String nic);

    List<Customer> findByTpLike(String tp);

    Customer findById(String id);

    Customer findDefaultCustomer();

    List<Customer> findAllByNameLikeIgnoreCaseAndActive(String barcode, Boolean active);

    boolean checkNic(String nic);
}
