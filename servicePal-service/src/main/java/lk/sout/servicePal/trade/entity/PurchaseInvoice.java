package lk.sout.servicePal.trade.entity;

import lk.sout.config.CascadeSave;
import lk.sout.core.entity.MetaData;
import org.springframework.data.annotation.*;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Document
public class PurchaseInvoice {

    @Id
    private String id;

    @Indexed(unique = true)
    private String purchaseInvoiceNo;

    //bill no given by vendor
    private String invoiceNo;

    private LocalDateTime date;

    @DBRef
    @CascadeSave
    private List<PurchaseInvoiceRecord> purchaseInvoiceRecords;

    private Double totalAmount;

    private Double payment;

    private Double balance;

    private LocalDate dueDate;

    private Double discount;

    //@DBRef
    private MetaData paymentMethod;

    @DBRef
    private Supplier supplier;

    //@DBRef
    private MetaData status;

    @CreatedDate
    private LocalDateTime createdDate;

    @CreatedBy
    private String createdBy;

    @LastModifiedDate
    private Date lastModifiedDate;

    @LastModifiedBy
    private String lastModifiedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public LocalDateTime getDate() {
        return date;
    }

    public void setDate(LocalDateTime date) {
        this.date = date;
    }

    public List<PurchaseInvoiceRecord> getPurchaseInvoiceRecords() {
        return purchaseInvoiceRecords;
    }

    public void setPurchaseInvoiceRecords(List<PurchaseInvoiceRecord> purchaseInvoiceRecords) {
        this.purchaseInvoiceRecords = purchaseInvoiceRecords;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Double getPayment() {
        return payment;
    }

    public void setPayment(Double payment) {
        this.payment = payment;
    }

    public Double getBalance() {
        return balance;
    }

    public void setBalance(Double balance) {
        this.balance = balance;
    }

    public Supplier getSupplier() {
        return supplier;
    }

    public void setSupplier(Supplier supplier) {
        this.supplier = supplier;
    }

    public MetaData getStatus() {
        return status;
    }

    public void setStatus(MetaData status) {
        this.status = status;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public LocalDate getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDate dueDate) {
        this.dueDate = dueDate;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public MetaData getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(MetaData paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getPurchaseInvoiceNo() {
        return purchaseInvoiceNo;
    }

    public void setPurchaseInvoiceNo(String purchaseInvoiceNo) {
        this.purchaseInvoiceNo = purchaseInvoiceNo;
    }

}
