package lk.sout.servicePal.trade.repository;

import lk.sout.core.entity.MetaData;
import lk.sout.servicePal.trade.entity.CashRecord;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface CashRecordRepository extends MongoRepository<CashRecord, String> {

    List<CashRecord> findByDateBetween(LocalDateTime sDate, LocalDateTime eDate);

    List<CashRecord> findByCounterAndDateBetween(String counter, LocalDateTime sDate, LocalDateTime eDate);

    List<CashRecord> findByCounterAndTypeAndDateBetween(String counter, String typeId, LocalDateTime sDate, LocalDateTime eDate);

    List<CashRecord> findByTypeAndDateBetween(String typeId, LocalDateTime sDate, LocalDateTime eDate);

    List<CashRecord> findByPurposeAndCounterAndDateBetween(MetaData purpose, String counter, LocalDateTime sDate,
                                                           LocalDateTime eDate);

    @Query(fields = "{'amount':1}")
    List<CashRecord> findByDateBetweenAndCounterAndType(LocalDateTime sDate, LocalDateTime eLocalDateTime, String counter,
                                                        MetaData type);

    @Query(fields = "{'amount':1}")
    List<CashRecord> findByDateBetweenAndCounterAndTypeAndPurposeNot(LocalDateTime sDate, LocalDateTime eLocalDateTime,
                                                                     String counter, MetaData type, MetaData purpose);

    @Query(fields = "{'amount':1}")
    List<CashRecord> findByDateBetweenAndCounterAndTypeAndPurpose(LocalDateTime sDate, LocalDateTime eLocalDateTime,
                                                                  String counter, MetaData type, MetaData purpose);

}
