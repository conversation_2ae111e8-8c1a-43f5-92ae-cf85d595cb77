package lk.sout.servicePal.trade.service;

import lk.sout.servicePal.trade.entity.PurchaseInvoice;
import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 2/4/2020
 */
public interface PurchaseInvoiceService {

    Response save(PurchaseInvoice purchaseInvoice);

    Iterable<PurchaseInvoice> findAll(Pageable pageable);

    List<PurchaseInvoice> findAllBySupplierOrderByIdDesc(String supplier);

    List<PurchaseInvoice> findAllByMetaDataNotCompleted(MetaData metaData);

    PurchaseInvoice searchByInvoiceNo(String invoiceNo);

    List<PurchaseInvoice> findBySupplierId(String id);

    List<PurchaseInvoice> findAllByDate(LocalDate date);

    PurchaseInvoice findById(String id);

    PurchaseInvoice findByPurchaseInvoiceNo(String id);
}
