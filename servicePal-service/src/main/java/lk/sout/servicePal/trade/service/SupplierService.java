package lk.sout.servicePal.trade.service;

import lk.sout.servicePal.trade.entity.Supplier;
import lk.sout.core.entity.Response;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Created by <PERSON><PERSON>a Weerasinghe on 2/4/2020
 */
public interface SupplierService {

    Response save(Supplier supplier);

    Iterable<Supplier> findAll(Pageable pageable);

    List<Supplier> findAllActive(boolean result);

    Supplier findBySupplierCode(String supplierCode);

    Supplier findById(String id);

    Supplier findByCode(String supplierCode);

    List<Supplier> findByNameLikeIgnoreCase(String supplierName);

    boolean findSupplierCode(String text);

    Supplier findDefaultSupplier();
}
