package lk.sout.servicePal.trade.service;

import lk.sout.servicePal.inventory.entity.Item;
import lk.sout.servicePal.trade.entity.SalesInvoiceRecord;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by <PERSON><PERSON>a Weerasinghe on 2/4/2020
 */
public interface SalesInvoiceRecordService {
    List<SalesInvoiceRecord> findAllByItemAndDate(Item item, LocalDate sDate, LocalDate eDate);

    List<SalesInvoiceRecord> findAllByItem(Item item);
}
