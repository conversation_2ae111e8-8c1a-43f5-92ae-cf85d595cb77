package lk.sout.servicePal.trade.controller;

import lk.sout.servicePal.trade.entity.Supplier;
import lk.sout.servicePal.trade.service.SupplierService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/supplier")
public class SupplierController {

    @Autowired
    SupplierService supplierService;

    @RequestMapping(value = "/save" ,method = RequestMethod.POST,produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody Supplier supplier){
        try{
            return ResponseEntity.ok(supplierService.save(supplier));
        }catch (Exception ex){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll" ,method = RequestMethod.GET)
    private ResponseEntity<?> findAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize){
        try {
            return ResponseEntity.ok(supplierService.findAll(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchById", method = RequestMethod.GET)
    private ResponseEntity<?> searchById(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(supplierService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchByName", method = RequestMethod.GET)
    private ResponseEntity<?> searchByName (@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(supplierService.findByNameLikeIgnoreCase(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

}
