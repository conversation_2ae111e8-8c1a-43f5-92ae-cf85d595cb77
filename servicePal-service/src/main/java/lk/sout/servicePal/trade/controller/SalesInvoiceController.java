package lk.sout.servicePal.trade.controller;

import lk.sout.servicePal.trade.entity.SalesInvoice;
import lk.sout.servicePal.trade.service.SalesInvoiceService;
import lk.sout.servicePal.trade.service.impl.SalesInvoiceServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/salesInvoice")
public class SalesInvoiceController {

    @Autowired
    SalesInvoiceService salesInvoiceService;

    @RequestMapping(value = "/findAllPages", method = RequestMethod.GET)
    private ResponseEntity<?> getAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(salesInvoiceService.findAll(PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize), Sort.by(Sort.Direction.DESC, "createdDate"))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody SalesInvoice salesInvoice) {
        try {
            return ResponseEntity.ok(salesInvoiceService.save(salesInvoice));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    //@PreAuthorize("hasRole('ADMIN')")
    @RequestMapping(value = "/amendSi", method = RequestMethod.GET)
    private ResponseEntity<?> amendSi(@RequestParam("invNo") String invNo) {
        try {
            return ResponseEntity.ok(salesInvoiceService.amendSi(invNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllPendingPages", method = RequestMethod.GET)
    private ResponseEntity<?> findAllPendingPages(@RequestParam("page") String page,
                                                  @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(salesInvoiceService.findAllPendingPages(PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize), Sort.by(Sort.Direction.DESC, "createdDate"))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllPendingSiForMonth", method = RequestMethod.GET)
    private ResponseEntity<?> findAllPendingSiForMonth() {
        try {
            return ResponseEntity.ok(salesInvoiceService.findAllPendingSiForMonth());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllPendingByRange", method = RequestMethod.GET)
    private ResponseEntity<?> findAllPendingByRange(@RequestParam("rangeId") String rangeId,
                                                    @RequestParam("page") String page,
                                                    @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(salesInvoiceService.findAllPendingByRange(rangeId, PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize), Sort.by(Sort.Direction.DESC, "createdDate"))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllPendingBetween", method = RequestMethod.GET)
    private ResponseEntity<?> findAllPendingByRange(@RequestParam("sDate") String sDate,
                                                    @RequestParam("eDate") String eDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
            return ResponseEntity.ok(salesInvoiceService.findAllPendingBetween(LocalDate.parse(sDate, formatter),
                    LocalDate.parse(eDate, formatter)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByCustomer", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findAllByCustomer(@RequestParam String nicBr) {
        try {
            return ResponseEntity.ok(salesInvoiceService.findByCustomerNicBr(nicBr));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByCustomerPending", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findAllByCustomerPending(@RequestParam String nicBr) {
        try {
            return ResponseEntity.ok(salesInvoiceService.findAllByCustomerPending(nicBr));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByInvoiceNo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByInvoiceNo(@RequestParam String invNo) {
        try {
            return ResponseEntity.ok(salesInvoiceService.findByInvNo(invNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByJobNo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByJobNo(@RequestParam String jobNo) {
        try {
            return ResponseEntity.ok(salesInvoiceService.findByJobNo(jobNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/payBalance", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> payBalance(@RequestParam String siNo, @RequestParam Double amount) {
        try {
            return ResponseEntity.ok(salesInvoiceService.payBalance(siNo, amount));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

}
