package lk.sout.servicePal.trade.service;

import lk.sout.servicePal.trade.entity.PurchaseInvoiceRecord;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 2/4/2020
 */
public interface PurchaseInvoiceRecordService {
    List<PurchaseInvoiceRecord> findAllByItemAndDate(String item, LocalDate sDate, LocalDate eDate);

    List<PurchaseInvoiceRecord> findAllByItem(String item);
}
