package lk.sout.servicePal.trade.service;

import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import lk.sout.servicePal.trade.entity.SalesInvoice;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by Mad<PERSON>a Weerasinghe on 2/4/2020
 */
public interface SalesInvoiceService {

    Iterable<SalesInvoice> findAll(Pageable pageable);

    Response save(SalesInvoice salesInvoice);

    Response amendSi(String invoiceNo);

    Iterable<SalesInvoice> findAllPendingPages(Pageable pageable);

    List<SalesInvoice> findAllPendingSiForMonth();

    Iterable<SalesInvoice> findAllPendingByRange(String rangeId, Pageable pageable);

    Iterable<SalesInvoice> findAllPendingBetween(LocalDate sDate, LocalDate eDate);

    List<SalesInvoice> findAllByOrderByIdDesc();

    List<SalesInvoice> findAllByCustomerOrderByIdDesc(String customer);

    List<SalesInvoice> findByCustomerNicBr(String nicBr);

    List<SalesInvoice> findAllByCustomerPending(String nicBr);

    SalesInvoice findByJobNo(String jobNo);

    SalesInvoice findByInvNo(String invNo);

    List<SalesInvoice> findAllByPaymentMethod(String paymentMethod);

    List<SalesInvoice> findByDate(LocalDate Date);

    List<SalesInvoice> findAllByMetaDataNotCompleted(MetaData metaData);

    List<SalesInvoice> findBySalesInvoiceIdLikeIgnoreCase(String invoiceNo);

    Response payBalance(String siNo, Double amount);

    SalesInvoice findById(String id);
}
