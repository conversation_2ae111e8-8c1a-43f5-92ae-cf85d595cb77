package lk.sout.servicePal.trade.service.impl;

import lk.sout.servicePal.inventory.entity.Item;
import lk.sout.servicePal.trade.entity.SalesInvoiceRecord;
import lk.sout.servicePal.trade.repository.SalesInvoiceRecordRepository;
import lk.sout.servicePal.trade.service.SalesInvoiceRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class SalesInvoiceRecordServiceImpl implements SalesInvoiceRecordService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SalesInvoiceRecordServiceImpl.class);

    @Autowired
    SalesInvoiceRecordRepository salesInvoiceRecordRepository;

    @Override
    public List<SalesInvoiceRecord> findAllByItemAndDate(Item item, LocalDate sDate, LocalDate eDate) {
        try {
            return salesInvoiceRecordRepository.findAllByItemAndDateBetween(item, sDate.atStartOfDay(), eDate.atStartOfDay());
        } catch (Exception ex) {
            LOGGER.error("Find All  Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoiceRecord> findAllByItem(Item item) {
        try {
            return salesInvoiceRecordRepository.findAllByItem(item);
        } catch (Exception ex) {
            LOGGER.error("Find All  Failed " + ex.getMessage());
            return null;
        }
    }
}
