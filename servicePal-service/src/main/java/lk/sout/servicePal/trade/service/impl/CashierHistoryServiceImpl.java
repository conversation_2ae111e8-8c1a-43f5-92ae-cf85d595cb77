package lk.sout.servicePal.trade.service.impl;

import lk.sout.core.entity.User;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.UserService;
import lk.sout.servicePal.trade.entity.CashRecord;
import lk.sout.servicePal.trade.entity.Cashier;
import lk.sout.servicePal.trade.entity.CashierHistory;
import lk.sout.servicePal.trade.repository.CashierHistoryRepository;
import lk.sout.servicePal.trade.repository.CashierRepository;
import lk.sout.servicePal.trade.service.CashRecordService;
import lk.sout.servicePal.trade.service.CashierHistoryService;
import lk.sout.servicePal.trade.service.CashierService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class CashierHistoryServiceImpl implements CashierHistoryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CashierHistoryServiceImpl.class);

    @Autowired
    CashierHistoryRepository cashierHistoryRepository;

    @Autowired
    CashierRepository cashierRepository;

    @Autowired
    CashRecordService cashRecordService;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    CashierService cashierService;

    @Autowired
    CashierHistory cashierHistory;

    @Autowired
    UserService userService;

    public boolean save(CashierHistory cashierHistory) {
        try {
            CashRecord cashRecord = new CashRecord();
            cashRecord.setPurpose(metaDataService.searchMetaData("Day End", "CashOutPurpose"));
            cashRecord.setType(metaDataService.searchMetaData("Cash Out", "Cash"));
            cashRecord.setAmount(cashierHistory.getWithdrawalAmount());
            cashRecord.setCounter(cashierHistory.getCounter());
            cashRecord.setDate(cashierHistory.getDate());
            if (cashierHistory.getShortage() != 0) {
                cashierService.correctCashier(cashierHistory.getActualAmount(), "1");
            }
            cashRecordService.save(cashRecord);
            cashierHistoryRepository.save(cashierHistory);
            LOGGER.info("CashierHistory saved");
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("Saving CashierHistory Failed: " + e.getMessage());
            return false;
        }
    }

    public List<CashierHistory> findAll() {
        try {
            return cashierHistoryRepository.findAll();
        } catch (Exception e) {
            LOGGER.error("Saving CashierHistory Failed: " + e.getMessage());
            return null;
        }
    }

    public CashierHistory findByDateAndCounter(LocalDate date, String counter) {
        try {
            return cashierHistoryRepository.findByDateAndCounter(date, counter);
        } catch (Exception e) {
            LOGGER.error("Finding CashierHistory by Date Failed: " + e.getMessage());
            return null;
        }
    }

    public Boolean existsByCounter(String counter) {
        try {
            return cashierHistoryRepository.existsByCounter(counter);
        } catch (Exception e) {
            LOGGER.error("Finding CashierHistory by Date Failed: " + e.getMessage());
            return null;
        }
    }

    @Override
    public boolean dayClose(Double actualAmount, Double withdrawalAmount, Double balance) {
        try {
            User user = userService.findLoggedInUser();
            Cashier cashier = cashierService.findByCounter(user.getCounter());
            cashierHistory.setOpeningBalance(cashier.getOpeningBalance());
            cashierHistory.setClosingBalance(balance);
            cashierHistory.setCashierAmountBeforeDayEnd(cashier.getCurrentBalance());
            cashierHistory.setWithdrawalAmount(withdrawalAmount);
            cashierHistory.setActualAmount(actualAmount);
            cashierHistory.setShortage(cashierHistory.getActualAmount() - cashierHistory.getCashierAmountBeforeDayEnd());
            cashierHistory.setCounter(user.getCounter());
            cashierHistory.setDate(cashier.getLastStartedDate());
            save(cashierHistory);
            return true;
        } catch (Exception e) {
            LOGGER.error("Finding CashierHistory by Date Failed: " + e.getMessage());
            return false;
        }
    }

}
