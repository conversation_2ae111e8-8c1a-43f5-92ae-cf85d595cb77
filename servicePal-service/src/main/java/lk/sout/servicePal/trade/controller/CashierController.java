package lk.sout.servicePal.trade.controller;

import lk.sout.servicePal.trade.service.CashierService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/cashier")
public class CashierController {

    @Autowired
    CashierService cashierService;

    @RequestMapping(value = "/findByCounter", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByCounter(@RequestParam String counterNo) {
        try {
            return ResponseEntity.ok(cashierService.findByCounter(counterNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/checkCashierStatus", method = RequestMethod.GET)
    private ResponseEntity<?> checkCashierStatus(@RequestParam String counterNo){
        try {
            return ResponseEntity.ok(cashierService.checkCounterStatus(counterNo));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

}
