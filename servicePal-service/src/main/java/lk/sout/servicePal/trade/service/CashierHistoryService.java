package lk.sout.servicePal.trade.service;

import lk.sout.servicePal.trade.entity.CashierHistory;

import java.time.LocalDate;
import java.util.List;

public interface CashierHistoryService {

    boolean save(CashierHistory cashierHistory);

    List<CashierHistory> findAll();

    CashierHistory findByDateAndCounter(LocalDate date, String counter);

    Boolean existsByCounter(String counter);

    boolean dayClose(Double actualAmount, Double withdrawalAmount, Double balance);
}
