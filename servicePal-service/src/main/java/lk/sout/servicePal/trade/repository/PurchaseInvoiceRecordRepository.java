package lk.sout.servicePal.trade.repository;

import lk.sout.servicePal.trade.entity.PurchaseInvoiceRecord;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface PurchaseInvoiceRecordRepository extends MongoRepository<PurchaseInvoiceRecord, String> {

    List<PurchaseInvoiceRecord> findAllBybarcodeOrderByDateDesc(String code);

    List<PurchaseInvoiceRecord> findAllBybarcodeAndDateBetween(String code, LocalDateTime sDate, LocalDateTime eDate);
}
