package lk.sout.servicePal.trade.repository;


import lk.sout.servicePal.trade.entity.CashierHistory;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;

@Repository
public interface CashierHistoryRepository extends MongoRepository<CashierHistory, String> {

    CashierHistory findByDateAndCounter(LocalDate date, String counter);

    boolean existsByCounter(String counter);
}
