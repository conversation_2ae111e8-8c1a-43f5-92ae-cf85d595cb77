package lk.sout.servicePal.trade.controller;

import lk.sout.servicePal.trade.entity.PurchaseInvoice;
import lk.sout.servicePal.trade.service.PurchaseInvoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/purchaseInvoice")
public class PurchaseInvoiceController {

    @Autowired
    PurchaseInvoiceService purchaseInvoiceService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody PurchaseInvoice purchaseInvoice) {
        try {
            return ResponseEntity.ok(purchaseInvoiceService.save(purchaseInvoice));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllPages", method = RequestMethod.GET)
    private ResponseEntity<?> findAllPages(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(purchaseInvoiceService.
                    findAll(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchBySupplier", method = RequestMethod.GET)
    private ResponseEntity<?> searchBySupplier(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(purchaseInvoiceService.findBySupplierId(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchByInvoiceNo", method = RequestMethod.GET)
    private ResponseEntity<?> searchByInvoiceId(@RequestParam("invoiceNo") String invoiceNo) {
        try {
            return ResponseEntity.ok(purchaseInvoiceService.searchByInvoiceNo(invoiceNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findById", method = RequestMethod.GET)
    private ResponseEntity<?> findById(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(purchaseInvoiceService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

}
