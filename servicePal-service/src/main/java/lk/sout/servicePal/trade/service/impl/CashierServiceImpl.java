package lk.sout.servicePal.trade.service.impl;

import lk.sout.core.entity.Action;
import lk.sout.core.service.ActionService;
import lk.sout.core.service.MetaDataService;
import lk.sout.servicePal.trade.entity.Cashier;
import lk.sout.servicePal.trade.repository.CashierRepository;
import lk.sout.servicePal.trade.service.CashierService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class CashierServiceImpl implements CashierService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CashierServiceImpl.class);

    @Autowired
    CashierRepository cashierRepository;

    @Autowired
    Cashier newCashier;

    @Autowired
    Action action;

    @Autowired
    ActionService actionService;

    @Autowired
    MetaDataService metaDataService;

    public boolean save(Cashier cashier) {
        try {
            cashierRepository.save(cashier);
            LOGGER.info("Cashier saved");
            return true;
        } catch (Exception e) {
            LOGGER.error("Saving Cashier Failed: " + e.getMessage());
            return false;
        }
    }

    public boolean topUpCashier(Double topUpAmount, String counterId) {
        try {
            Cashier cashier = cashierRepository.findCashierByCounter(counterId);
            if (null != cashier) {
                Double avlBalance = cashier.getCurrentBalance();
                cashier.setCurrentBalance(avlBalance + topUpAmount);
                cashierRepository.save(cashier);
            } else {
                newCashier.setId(null);
                newCashier.setOpeningBalance(0.0);
                newCashier.setCounter(counterId);
                newCashier.setCurrentBalance(topUpAmount);
                cashierRepository.save(newCashier);
            }
            return true;
        } catch (Exception ex) {
            LOGGER.error("Top up Cashier Failed");
            return false;
        }
    }

    public boolean deductFromCashier(Double amount, String counterId) {
        try {
            Cashier cashier = cashierRepository.findCashierByCounter(counterId);
            if (null != cashier) {
                Double avlBalance = cashier.getCurrentBalance();
                if (avlBalance.compareTo(amount) >= 0) {
                    cashier.setCurrentBalance(avlBalance - amount);
                    cashierRepository.save(cashier);
                } else {
                    return false;
                }
            }
            return true;
        } catch (Exception ex) {
            LOGGER.error("Deducting From Cashier Failed");
            return false;
        }
    }

    public boolean correctCashier(Double amount, String counterId) {
        try {
            Cashier cashier = cashierRepository.findCashierByCounter(counterId);
            if (null != cashier) {
                cashier.setCurrentBalance(amount);
                cashierRepository.save(cashier);

                action.setOperator(cashier.getCurrentBalance().compareTo(amount) > 0 ? "-" : "+");
                action.setReference(counterId);
                action.setChange(String.valueOf(amount - cashier.getCurrentBalance()));
                action.setRemark("Cashier balance altered");
                action.setType(metaDataService.searchMetaData("Cashier Amount Alter", "Action"));
                actionService.save(action);
            } else {
                return false;
            }
            return true;
        } catch (Exception ex) {
            LOGGER.error("Deducting From Cashier Failed");
            return false;
        }
    }

    public List<Cashier> findAll() {
        try {
            return cashierRepository.findAll();
        } catch (Exception ex) {
            LOGGER.error("Retrieving Cashier details failed");
            return null;
        }
    }

    public Cashier findByCounter(String counter) {
        try {
            Cashier cashier = cashierRepository.findCashierByCounter(counter);
            return cashier;
        } catch (Exception ex) {
            LOGGER.error("Retrieving Cashier details failed");
            return null;
        }
    }

    @Override
    public boolean checkCounterStatus(String counter) {
        Cashier cashier = findByCounter(counter);
        if (!cashier.isActive() && cashier.getLastClosedDate().isEqual(LocalDate.now())) {
            return false;
        }
        if (cashier.isActive() && cashier.getLastStartedDate().isBefore(LocalDate.now())) {
            return false;
        }
        //this should located at last
        if (cashier.getLastStartedDate().isBefore(LocalDate.now())) {
            return false;
        }
        return true;
    }

}
