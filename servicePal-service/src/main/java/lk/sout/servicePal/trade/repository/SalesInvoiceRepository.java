/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package lk.sout.servicePal.trade.repository;

import lk.sout.core.entity.MetaData;
import lk.sout.servicePal.trade.entity.Customer;
import lk.sout.servicePal.trade.entity.SalesInvoice;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

//import lk.sout.erp.entity.Customer;

/**
 * <AUTHOR>
 */
@Repository
public interface SalesInvoiceRepository extends MongoRepository<SalesInvoice, String> {

    List<SalesInvoice> findByDate(LocalDate date);

    List<SalesInvoice> findAllByOrderByIdDesc();

    List<SalesInvoice> findAllByCustomerOrderByIdDesc(String customer);

    List<SalesInvoice> findAllByStatusNot(MetaData metaData);

    Page<SalesInvoice> findAllByStatus(MetaData metaData, Pageable pageable);

    Page<SalesInvoice> findAllByStatusAndCreatedDateBetween(MetaData metaData, LocalDateTime sDate,
                                                            LocalDateTime eDate, Pageable pageable);

    List<SalesInvoice> findAllByStatusAndCreatedDateBetween(MetaData metaData, LocalDateTime sDate,
                                                            LocalDateTime eDate);

    List<SalesInvoice> findAllByStatusNotAndCreatedDateBetween(MetaData metaData, LocalDateTime sDate,
                                                            LocalDateTime eDate);

    List<SalesInvoice> findAllByCustomer(Customer customer);

    List<SalesInvoice> findAllByCustomerAndStatusNot(Customer customer, MetaData status);

    List<SalesInvoice> findByInvoiceNoLikeIgnoreCase(String salesInvoiceId);

    SalesInvoice findByInvoiceNo(String salesInvoiceId);

    SalesInvoice findByJobNo(String jobNo);

    List<SalesInvoice> findAllByPaymentMethodValue(String paymentMethod);
}
