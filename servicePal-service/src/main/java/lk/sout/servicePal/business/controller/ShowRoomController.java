package lk.sout.servicePal.business.controller;

import lk.sout.servicePal.business.entity.ShowRoom;
import lk.sout.servicePal.business.service.ShowRoomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasing<PERSON> on 9/3/2020
 */
@RestController
@RequestMapping("/showRoom")
public class ShowRoomController {

    @Autowired
    ShowRoomService showRoomService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody ShowRoom showRoom) {
        try {
            return ResponseEntity.ok(showRoomService.save(showRoom));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllPageable", method = RequestMethod.GET)
    public ResponseEntity<?> findAllPageable(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        return ResponseEntity.ok(showRoomService.findAll(PageRequest.of(Integer.parseInt(page),
                Integer.parseInt(pageSize))));
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public ResponseEntity<?> findAll() {
        return ResponseEntity.ok(showRoomService.findAll());
    }
}
