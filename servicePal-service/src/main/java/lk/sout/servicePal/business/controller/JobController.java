package lk.sout.servicePal.business.controller;

import lk.sout.servicePal.business.entity.Job;
import lk.sout.servicePal.business.entity.JobEstimate;
import lk.sout.servicePal.business.service.JobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * Created by Madhawa Weerasinghe on 12/16/2019
 */

@RestController
@RequestMapping("/job")
public class JobController {

    @Autowired
    private JobService jobService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody Job job) {
        try {
            return ResponseEntity.ok(jobService.save(job));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/saveEstimate", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> saveEstimate(@RequestBody JobEstimate jobEstimate) {
        try {
            return ResponseEntity.ok(jobService.saveEstimate(jobEstimate));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/approveEstimate", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> approveEstimate(@RequestParam("jobNo") String jobNo,
                                              @RequestParam("approvedDate") String approvedDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
            return ResponseEntity.ok(jobService.approveEstimate(jobNo, LocalDate.parse(approvedDate, formatter)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/changeAssignment", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> changeAssignment(@RequestParam("jobNo") String jobNo, @RequestParam("empId") String empNo) {
        try {
            return ResponseEntity.ok(jobService.changeAssignment(jobNo, empNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/completingJob", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> completingJob(@RequestBody Job job) {
        try {
            return ResponseEntity.ok(jobService.completingJob(job));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public ResponseEntity<?> findAllCustomers(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        return ResponseEntity.ok(jobService.findAll(PageRequest.of(Integer.parseInt(page),
                Integer.parseInt(pageSize), Sort.by(Sort.Direction.DESC, "createdDate"))));
    }


    @RequestMapping(value = "/searchByJobNo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchByJobNo(@RequestParam String jobNo) {
        try {
            return ResponseEntity.ok(jobService.findByJobNo(jobNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchWoNo", method = RequestMethod.GET,
            produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchWoNo(@RequestParam String woNo) {
        try {
            return ResponseEntity.ok(jobService.findWoNo(woNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchByWoNo", method = RequestMethod.GET,
            produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchByWoNo(@RequestParam String woNo) {
        try {
            return ResponseEntity.ok(jobService.findByWoNo(woNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchBySerialNo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchBySerialNo(@RequestParam String serialNo) {
        try {
            return ResponseEntity.ok(jobService.searchBySerialNo(serialNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchByCustomerId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchByCustomerId(@RequestParam String customerId) {
        try {
            return ResponseEntity.ok(jobService.searchByCustomerId(customerId));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchByCustomerNic", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchByCustomerNic(@RequestParam String customerNic) {
        try {
            return ResponseEntity.ok(jobService.searchByCustomerNic(customerNic));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByJobStatus", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByJobStatus(@RequestParam String jobStatusId, @RequestParam("page") String page,
                                              @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(jobService.findByStatus(jobStatusId, PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findById(@RequestParam String id) {
        try {
            return ResponseEntity.ok(jobService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findJobsInProgress", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findJobsInProgress(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(jobService.findJobsInProgress(PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findNewJobs", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findNewJobs(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(jobService.findNewJobs(PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findCompletedJobs", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findCompletedJobs(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(jobService.findCompleted(PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/topUpAdvance", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> topUpAdvance(@RequestParam String jobNo, @RequestParam Double amount) {
        try {
            return ResponseEntity.ok(jobService.topUpAdvancePayment(jobNo, amount));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/updateRemark", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> updateRemark(@RequestParam String jobNo, @RequestParam("remark") String remark) {
        try {
            return ResponseEntity.ok(jobService.updateRemark(jobNo, remark));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

}
