package lk.sout.servicePal.business.service;

import lk.sout.servicePal.business.entity.Machine;
import lk.sout.servicePal.business.entity.MaintenanceRecord;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 12/15/2019
 */
public interface MaintenanceRecordService {

    Iterable<MaintenanceRecord> findAll(Pageable pageable);

    MaintenanceRecord findById(String id);

    MaintenanceRecord findBySerialNo(String serial);

    Iterable<MaintenanceRecord> findByCustomer(String customerId, Pageable pageable);

    List<MaintenanceRecord> searchBySerialNoLike(String serial);

}
