package lk.sout.servicePal.business.repository;

import lk.sout.servicePal.business.entity.Machine;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 12/15/2019
 */
@Repository
public interface MachineRepository extends MongoRepository<Machine, String> {

    Page<Machine> findAllByActive(boolean active, Pageable pageable);

    Machine findBySerialNo(String serialNo);

    Page<Machine> findByCustomer(String customerId, Pageable pageable);

    Page<Machine> findByBrand(String brandId, Pageable pageable);

    List<Machine> findTop10BySerialNoLikeIgnoreCaseAndActive(String serialNo, boolean active);

    Page<Machine> findByModelNoLikeIgnoreCaseAndActive(String serialNo, boolean active, Pageable pageable);
}
