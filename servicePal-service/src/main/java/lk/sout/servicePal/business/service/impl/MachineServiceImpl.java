package lk.sout.servicePal.business.service.impl;

import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import lk.sout.core.service.MetaDataService;
import lk.sout.servicePal.business.entity.Machine;
import lk.sout.servicePal.business.entity.MaintenanceRecord;
import lk.sout.servicePal.business.repository.MachineRepository;
import lk.sout.servicePal.business.service.MachineService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Madhawa Weerasinghe on 12/15/2019
 */

@Service
public class MachineServiceImpl implements MachineService {

    private static final Logger LOGGER = LoggerFactory.getLogger(JobServiceImpl.class);

    @Autowired
    MachineRepository machineRepository;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    Response response;

    @Override
    public boolean save(Machine machine) {
        try {
            machineRepository.save(machine);
            response.setCode(200);
            response.setMessage("Machine Created Successfully");
            return true;
        } catch (Exception ex) {
            LOGGER.error("Creating Machine Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Creating Machine Failed");
            response.setData(ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<Machine> findAll(Pageable pageable) {
        try {
            return machineRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Machine Categories Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Machine> findByActive(boolean active, Pageable pageable) {
        try {
            return machineRepository.findAllByActive(active, pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Machine Categories by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Machine findById(String id) {
        try {
            return machineRepository.findById(id).get();
        } catch (Exception ex) {
            LOGGER.error("Find All Machine Categories by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Machine findBySerialNo(String serial) {
        try {
            return machineRepository.findBySerialNo(serial);
        } catch (Exception ex) {
            LOGGER.error("Find By Machine Serial Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Machine> findByCustomer(String customerId, Pageable pageable) {
        try {
            return machineRepository.findByCustomer(customerId, pageable);
        } catch (Exception ex) {
            LOGGER.error("Find By Customer Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Machine> findByBrand(String brandId, Pageable pageable) {
        try {
            return machineRepository.findByBrand(brandId, pageable);
        } catch (Exception ex) {
            LOGGER.error("Find By Brand Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Machine> searchBySerialNoLike(String serial) {
        try {
            return machineRepository.findTop10BySerialNoLikeIgnoreCaseAndActive(serial, true);
        } catch (Exception ex) {
            LOGGER.error("Search By Machine Serial Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Machine> searchByModelNoLike(String modelNo, Pageable pageable) {
        try {
            return machineRepository.findByModelNoLikeIgnoreCaseAndActive(modelNo, true, pageable);
        } catch (Exception ex) {
            LOGGER.error("Search By Machine Model No Failed " + ex.getMessage());
            return null;
        }
    }
}
