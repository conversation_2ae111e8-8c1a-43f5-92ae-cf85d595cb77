package lk.sout.servicePal.business.controller;

import lk.sout.servicePal.business.entity.Machine;
import lk.sout.servicePal.business.service.MachineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Created by <PERSON><PERSON><PERSON> on 12/16/2019
 */

@RestController
@RequestMapping("/machine")
public class MachineController {

    @Autowired
    private MachineService machineService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody Machine machine) {
        try {
            return ResponseEntity.ok(machineService.save(machine));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public ResponseEntity<?> findAllCustomers(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        return ResponseEntity.ok(machineService.findAll(PageRequest.of(Integer.parseInt(page),
                Integer.parseInt(pageSize))));
    }

    @RequestMapping(value = "/findByActive", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByActive(@RequestParam boolean active, @RequestParam("page") String page,
                                           @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(machineService.findByActive(active, PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }


    @RequestMapping(value = "/findById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findById(@RequestParam String id) {
        try {
            return ResponseEntity.ok(machineService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findBySerialNo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findBySerialNo(@RequestParam String serialNo) {
        try {
            return ResponseEntity.ok(machineService.findBySerialNo(serialNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchBySerialNoLike", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchBySerialNoLike(@RequestParam String serialNo) {
        try {
            return ResponseEntity.ok(machineService.searchBySerialNoLike(serialNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchByModelNoLike", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchByModelNoLike(@RequestParam String modelNo, @RequestParam("page") String page,
                                                  @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(machineService.searchByModelNoLike(modelNo, PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByCustomer", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByCustomer(@RequestParam String customerId, @RequestParam("page") String page,
                                             @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(machineService.findByCustomer(customerId, PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByBrand", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> brandId(@RequestParam String brandId, @RequestParam("page") String page,
                                      @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(machineService.findByBrand(brandId, PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

}
