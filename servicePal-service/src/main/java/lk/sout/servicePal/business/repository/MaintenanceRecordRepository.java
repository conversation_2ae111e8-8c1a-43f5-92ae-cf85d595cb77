package lk.sout.servicePal.business.repository;

import lk.sout.servicePal.business.entity.Machine;
import lk.sout.servicePal.business.entity.MachineCategory;
import lk.sout.servicePal.business.entity.MaintenanceRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 12/15/2019
 */
public interface MaintenanceRecordRepository extends MongoRepository<MaintenanceRecord, String> {

    MaintenanceRecord findByMachineSerial(String serialNo);

    Page<MaintenanceRecord> findByCustomer(String customerId, Pageable pageable);

    List<MaintenanceRecord> findByMachineSerialLikeIgnoreCase(String serialNo);

}
