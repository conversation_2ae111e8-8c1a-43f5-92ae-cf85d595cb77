package lk.sout.servicePal.business.service;

import lk.sout.servicePal.business.entity.Machine;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 12/15/2019
 */
public interface MachineService {

    boolean save(Machine machine);

    Iterable<Machine> findAll(Pageable pageable);

    Iterable<Machine> findByActive(boolean active, Pageable pageable);

    Machine findById(String id);

    Machine findBySerialNo(String serial);

    Iterable<Machine> findByCustomer(String customerId, Pageable pageable);

    Iterable<Machine> findByBrand(String brandId, Pageable pageable);

    List<Machine> searchBySerialNoLike(String serial);

    Iterable<Machine> searchByModelNoLike(String modelNo, Pageable pageable);
}
