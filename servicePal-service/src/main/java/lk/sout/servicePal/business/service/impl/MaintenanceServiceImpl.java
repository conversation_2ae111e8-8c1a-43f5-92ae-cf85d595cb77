package lk.sout.servicePal.business.service.impl;

import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import lk.sout.core.service.MetaDataService;
import lk.sout.servicePal.business.entity.Maintenance;
import lk.sout.servicePal.business.entity.Maintenance;
import lk.sout.servicePal.business.entity.MaintenanceRecord;
import lk.sout.servicePal.business.repository.MachineRepository;
import lk.sout.servicePal.business.repository.MaintenanceRecordRepository;
import lk.sout.servicePal.business.repository.MaintenanceRepository;
import lk.sout.servicePal.business.service.MachineService;
import lk.sout.servicePal.business.service.MaintenanceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weera<PERSON> on 12/15/2019
 */

@Service
public class MaintenanceServiceImpl implements MaintenanceService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MaintenanceServiceImpl.class);

    @Autowired
    MaintenanceRepository maintenanceRepository;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    Response response;

    @Override
    public boolean save(Maintenance maintenance) {
        try {
            if (maintenance.getVisitsPerYear() != 0 && maintenance.getWarrantyStart() != null
                    && maintenance.getWarrantyExpire() != null) {

                List<MaintenanceRecord> maintenanceRecords = new ArrayList<>();

                MetaData jobStatus = metaDataService.searchMetaData("Job Created", "Job Status");

                int cycles = 12 / maintenance.getVisitsPerYear();

                for (int cycle = 1; cycle <= cycles; cycle++) {
                    MaintenanceRecord mRec = new MaintenanceRecord();
                    mRec.setDate(maintenance.getWarrantyStart().plusMonths(cycle * cycles));
                   /* mRec.setMachineSerial(maintenance.getSerialNo());
                    mRec.setStatus(jobStatus);
                    mRec.setBrand(maintenance.getBrand());
                    mRec.setModel(maintenance.getModelNo());*/
                    mRec.setCustomer(maintenance.getCustomer());
                    mRec.setCharge(maintenance.getTotalMaintenanceCharge() / maintenance.getVisitsPerYear());
                    maintenanceRecords.add(mRec);
                }
                maintenance.setMaintenanceRecords(maintenanceRecords);
            }
            maintenanceRepository.save(maintenance);
            response.setCode(200);
            response.setMessage("Maintenance Created Successfully");
            return true;
        } catch (Exception ex) {
            LOGGER.error("Creating Maintenance Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Creating Maintenance Failed");
            response.setData(ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<Maintenance> findAll(Pageable pageable) {
        try {
            return maintenanceRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Maintenance Categories Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Maintenance> findAllByActive(Pageable pageable) {
        try {
          //  return maintenanceRepository.findAllByActive(true, pageable);
            return null;
        } catch (Exception ex) {
            LOGGER.error("Find All Maintenance Categories by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Maintenance findById(String id) {
        try {
            return maintenanceRepository.findById(id).get();
        } catch (Exception ex) {
            LOGGER.error("Find All Maintenance Categories by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Maintenance> findBySerialNo(String serial) {
        try {
            //return maintenanceRepository.findBySerialNoAndActive(serial, true);
            return null;
        } catch (Exception ex) {
            LOGGER.error("Find By Maintenance Serial Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Maintenance> findByCustomer(String customerId, Pageable pageable) {
        try {
            //return maintenanceRepository.findByCustomer(customerId, pageable);
            return null;
        } catch (Exception ex) {
            LOGGER.error("Find By Customer Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Maintenance> findByBrand(String brandId, Pageable pageable) {
        try {
            //return maintenanceRepository.findByBrand(brandId, pageable);
            return null;
        } catch (Exception ex) {
            LOGGER.error("Find By Brand Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Maintenance> searchBySerialNoLike(String serial) {
        try {
            //return maintenanceRepository.findTop10BySerialNoLikeIgnoreCaseAndActive(serial, true);
            return null;
        } catch (Exception ex) {
            LOGGER.error("Search By Maintenance Serial Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Maintenance> searchByModelNoLike(String modelNo, Pageable pageable) {
        try {
          //  return maintenanceRepository.findByModelNoLikeIgnoreCaseAndActive(modelNo, true, pageable);
            return null;
        } catch (Exception ex) {
            LOGGER.error("Search By Maintenance Model No Failed " + ex.getMessage());
            return null;
        }
    }
}
