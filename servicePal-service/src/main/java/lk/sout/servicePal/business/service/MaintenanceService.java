package lk.sout.servicePal.business.service;

import lk.sout.servicePal.business.entity.Maintenance;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 12/15/2019
 */
public interface MaintenanceService {

    boolean save(Maintenance Maintenance);

    Iterable<Maintenance> findAll(Pageable pageable);

    Iterable<Maintenance> findAllByActive(Pageable pageable);

    Maintenance findById(String id);

    List<Maintenance> findBySerialNo(String serial);

    Iterable<Maintenance> findByCustomer(String customerId, Pageable pageable);

    Iterable<Maintenance> findByBrand(String brandId, Pageable pageable);

    List<Maintenance> searchBySerialNoLike(String serial);

    Iterable<Maintenance> searchByModelNoLike(String modelNo, Pageable pageable);
}
