package lk.sout.servicePal.business.entity;

import lk.sout.core.entity.MetaData;
import lk.sout.servicePal.inventory.entity.Item;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by Madhawa Weerasinghe on 12/15/2019
 */

@Document
public class JobEstimate {

    @Id
    private String id;

    private String preparedBy; //username

    private List<JobEstimateRecord> jobEstimateRecordList;

    private String approvedBy; //username

    private LocalDate createdDate;

    private String jobNo;

    @DBRef
    private List<Item> services;

    @DBRef
    private Item transport;

    private Double subContractorCharge;

    private Double partsCharge;

    private Double serviceCharge;

    private Double transportCharge;

    private Double discount;

    private Double totalAmount;

    @DBRef
    private MetaData approveMethod;

    private boolean approved;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<JobEstimateRecord> getJobEstimateRecordList() {
        return jobEstimateRecordList;
    }

    public void setJobEstimateRecordList(List<JobEstimateRecord> jobEstimateRecordList) {
        this.jobEstimateRecordList = jobEstimateRecordList;
    }

    public LocalDate getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDate createdDate) {
        this.createdDate = createdDate;
    }

    public MetaData getApproveMethod() {
        return approveMethod;
    }

    public void setApproveMethod(MetaData approveMethod) {
        this.approveMethod = approveMethod;
    }

    public String getJobNo() {
        return jobNo;
    }

    public void setJobNo(String jobNo) {
        this.jobNo = jobNo;
    }

    public Double getSubContractorCharge() {
        return subContractorCharge;
    }

    public void setSubContractorCharge(Double subContractorCharge) {
        this.subContractorCharge = subContractorCharge;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public List<Item> getServices() {
        return services;
    }

    public void setServices(List<Item> services) {
        this.services = services;
    }

    public Item getTransport() {
        return transport;
    }

    public void setTransport(Item transport) {
        this.transport = transport;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public Double getPartsCharge() {
        return partsCharge;
    }

    public void setPartsCharge(Double partsCharge) {
        this.partsCharge = partsCharge;
    }

    public Double getServiceCharge() {
        return serviceCharge;
    }

    public void setServiceCharge(Double serviceCharge) {
        this.serviceCharge = serviceCharge;
    }

    public Double getTransportCharge() {
        return transportCharge;
    }

    public void setTransportCharge(Double transportCharge) {
        this.transportCharge = transportCharge;
    }

    public String getPreparedBy() {
        return preparedBy;
    }

    public void setPreparedBy(String preparedBy) {
        this.preparedBy = preparedBy;
    }

    public String getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(String approvedBy) {
        this.approvedBy = approvedBy;
    }

    public boolean isApproved() {
        return approved;
    }

    public void setApproved(boolean approved) {
        this.approved = approved;
    }
}
