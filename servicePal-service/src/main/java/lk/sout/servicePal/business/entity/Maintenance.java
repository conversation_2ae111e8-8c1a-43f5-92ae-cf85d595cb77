package lk.sout.servicePal.business.entity;

import lk.sout.config.CascadeSave;
import lk.sout.core.entity.MetaData;
import lk.sout.servicePal.inventory.entity.Brand;
import lk.sout.servicePal.trade.entity.Customer;
import org.springframework.data.annotation.*;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * Created by Madhawa Weerasinghe on 12/14/2019
 */

@Document
public class Maintenance {

    @Id
    private String id;

    private String agreementNo;

    @DBRef
    private List<Machine> machines;

    private Customer customer;

    private Date nextServiceDate;

    private String remark;

    private int visitsPerYear;

    // Current service no which is to be done.
    private int currentVisitNo;

    private Double totalMaintenanceCharge;

    private Double paidAmount;

    private LocalDate warrantyStart;

    private LocalDate warrantyExpire;

    private MetaData paymentStatus;

    @DBRef
    @CascadeSave
    private List<MaintenanceRecord> maintenanceRecords;

    private boolean active;

    @CreatedDate
    private Date createdDate;

    @CreatedBy
    private String createdBy;

    @LastModifiedDate
    private Date lastModifiedDate;

    @LastModifiedBy
    private String lastModifiedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Customer getCustomer() {
        return customer;
    }

    public void setCustomer(Customer customer) {
        this.customer = customer;
    }

    public List<Machine> getMachines() {
        return machines;
    }

    public void setMachines(List<Machine> machines) {
        this.machines = machines;
    }

    public int getCurrentVisitNo() {
        return currentVisitNo;
    }

    public void setCurrentVisitNo(int currentVisitNo) {
        this.currentVisitNo = currentVisitNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public LocalDate getWarrantyExpire() {
        return warrantyExpire;
    }

    public void setWarrantyExpire(LocalDate warrantyExpire) {
        this.warrantyExpire = warrantyExpire;
    }

    public int getVisitsPerYear() {
        return visitsPerYear;
    }

    public void setVisitsPerYear(int visitsPerYear) {
        this.visitsPerYear = visitsPerYear;
    }

    public LocalDate getWarrantyStart() {
        return warrantyStart;
    }

    public void setWarrantyStart(LocalDate warrantyStart) {
        this.warrantyStart = warrantyStart;
    }

    public List<MaintenanceRecord> getMaintenanceRecords() {
        return maintenanceRecords;
    }

    public void setMaintenanceRecords(List<MaintenanceRecord> maintenanceRecords) {
        this.maintenanceRecords = maintenanceRecords;
    }

    public Double getTotalMaintenanceCharge() {
        return totalMaintenanceCharge;
    }

    public void setTotalMaintenanceCharge(Double totalMaintenanceCharge) {
        this.totalMaintenanceCharge = totalMaintenanceCharge;
    }

    public MetaData getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(MetaData paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }

    public Date getNextServiceDate() {
        return nextServiceDate;
    }

    public void setNextServiceDate(Date nextServiceDate) {
        this.nextServiceDate = nextServiceDate;
    }

    public Double getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(Double paidAmount) {
        this.paidAmount = paidAmount;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }


}
