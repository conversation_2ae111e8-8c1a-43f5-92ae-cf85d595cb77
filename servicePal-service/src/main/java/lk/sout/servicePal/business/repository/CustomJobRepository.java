package lk.sout.servicePal.business.repository;

import lk.sout.servicePal.business.entity.Job;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 4/22/2020
 */
@Repository
public class CustomJobRepository {

    @Autowired
    MongoTemplate mongoTemplate;

    public Page<Job> findAllForTable(Pageable pageable) {
        Query query = new Query().with(pageable);
        query.fields().include("id").include("customer").include("machine").include("jobStatus").include("jobDate").
                include("technicians").include("jobNo").include("woNumber");
        List<Job> filteredJobs = mongoTemplate.find(query, Job.class, "job");
        return PageableExecutionUtils.getPage(filteredJobs, pageable,
                () -> mongoTemplate.count((Query.of(query).limit(-1).skip(-1)), Job.class));
    }
}
