package lk.sout.servicePal.business.repository;

import lk.sout.servicePal.business.entity.Commission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasing<PERSON> on 4/22/2020
 */
@Repository
public class CustomCommissionRepository {

    @Autowired
    MongoTemplate mongoTemplate;

    public List<Commission> findAllForTable() {
        Query query = new Query();
        query.fields().include("id").include("date").include("jobNo").include("amount").include("empId").
                include("empName");
        query.isSorted();
        query.limit(10);
        return mongoTemplate.find(query, Commission.class, "commission");
    }
}
