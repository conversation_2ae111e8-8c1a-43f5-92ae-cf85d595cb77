package lk.sout.servicePal.business.entity;

import lk.sout.servicePal.hr.entity.Employee;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasing<PERSON> on 12/15/2019
 */
public class JobEstimateRecord {

    private String barcode;

    private String itemCode;

    private String itemName;

    private Double unitPrice;

    private Double price;

    private Double quantity;

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public Double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(Double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }
}
