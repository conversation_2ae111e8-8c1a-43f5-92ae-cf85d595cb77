package lk.sout.servicePal.business.repository;

import lk.sout.servicePal.business.entity.Job;
import lk.sout.servicePal.business.entity.Machine;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 12/15/2019
 */
@Repository
public interface JobRepository extends MongoRepository<Job, String> {

    Page<Job> findAllByJobStatus(String id, Pageable pageable);

    Page<Job> findAllByJobStatusIn(String[] ids, Pageable pageable);

    Job findByJobNo(String jobNo);

    @Query(fields = "{'woNumber':1}")
    List<Job> findByWoNumberLikeIgnoreCase(String woNumber);

    Job findByWoNumber(String woNumber);

    List<Job> findByCustomer(String customerId);

    List<Job> findByMachine(Machine machine);

}
