package lk.sout.servicePal.business.entity;

import lk.sout.config.CascadeSave;
import lk.sout.core.entity.MetaData;
import lk.sout.servicePal.hr.entity.Employee;
import lk.sout.servicePal.inventory.entity.Warehouse;
import lk.sout.servicePal.trade.entity.Customer;
import lk.sout.servicePal.trade.entity.SalesInvoice;
import org.springframework.data.annotation.*;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * Created by Madhawa Weerasinghe on 12/14/2019
 */
@Document
public class Job {

    @Id
    private String id;

    private String jobNo;

    private LocalDate jobDate;

    @DBRef
    private Customer customer;

    @DBRef
    private Machine machine;

    private String remark;

    @CascadeSave
    @DBRef
    private JobEstimate jobEstimate;

    @DBRef
    ShowRoom showRoom;

    private LocalDate estimateCreatedDate;

    private LocalDate estimateApprovedDate;

    @DBRef
    private MetaData jobStatus;

    private String refNo;

    @DBRef
    private Employee inspectedBy;

    @DBRef
    private List<Employee> assignments;

    private List<JobCommission> jobCommissions;

    private LocalDate completedDate;

    private String woNumber;

    private String defect;

    private String lessParts;

    private String damages;

    @DBRef
    private SalesInvoice invoice;

    private Double advancePayment;

    @DBRef
    private Warehouse warehouse;

    @CreatedDate
    private Date createdDate;

    @CreatedBy
    private String createdBy;

    @LastModifiedDate
    private Date lastModifiedDate;

    @LastModifiedBy
    private String lastModifiedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getJobNo() {
        return jobNo;
    }

    public void setJobNo(String jobNo) {
        this.jobNo = jobNo;
    }

    public LocalDate getJobDate() {
        return jobDate;
    }

    public void setJobDate(LocalDate jobDate) {
        this.jobDate = jobDate;
    }

    public Customer getCustomer() {
        return customer;
    }

    public void setCustomer(Customer customer) {
        this.customer = customer;
    }

    public Machine getMachine() {
        return machine;
    }

    public void setMachine(Machine machine) {
        this.machine = machine;
    }

    public JobEstimate getJobEstimate() {
        return jobEstimate;
    }

    public void setJobEstimate(JobEstimate jobEstimate) {
        this.jobEstimate = jobEstimate;
    }

    public MetaData getJobStatus() {
        return jobStatus;
    }

    public void setJobStatus(MetaData jobStatus) {
        this.jobStatus = jobStatus;
    }

    public List<JobCommission> getJobCommissions() {
        return jobCommissions;
    }

    public void setJobCommissions(List<JobCommission> jobCommissions) {
        this.jobCommissions = jobCommissions;
    }

    public List<Employee> getAssignments() {
        return assignments;
    }

    public void setAssignments(List<Employee> assignments) {
        this.assignments = assignments;
    }

    public String getRefNo() {
        return refNo;
    }

    public void setRefNo(String refNo) {
        this.refNo = refNo;
    }

    public Employee getInspectedBy() {
        return inspectedBy;
    }

    public void setInspectedBy(Employee inspectedBy) {
        this.inspectedBy = inspectedBy;
    }

    public LocalDate getCompletedDate() {
        return completedDate;
    }

    public void setCompletedDate(LocalDate completedDate) {
        this.completedDate = completedDate;
    }

    public String getDefect() {
        return defect;
    }

    public void setDefect(String defect) {
        this.defect = defect;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public LocalDate getEstimateApprovedDate() {
        return estimateApprovedDate;
    }

    public void setEstimateApprovedDate(LocalDate estimateApprovedDate) {
        this.estimateApprovedDate = estimateApprovedDate;
    }

    public SalesInvoice getInvoice() {
        return invoice;
    }

    public void setInvoice(SalesInvoice invoice) {
        this.invoice = invoice;
    }

    public LocalDate getEstimateCreatedDate() {
        return estimateCreatedDate;
    }

    public void setEstimateCreatedDate(LocalDate estimateCreatedDate) {
        this.estimateCreatedDate = estimateCreatedDate;
    }

    public String getLessParts() {
        return lessParts;
    }

    public void setLessParts(String lessParts) {
        this.lessParts = lessParts;
    }

    public String getDamages() {
        return damages;
    }

    public void setDamages(String damages) {
        this.damages = damages;
    }

    public String getWoNumber() {
        return woNumber;
    }

    public void setWoNumber(String woNumber) {
        this.woNumber = woNumber;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public ShowRoom getShowRoom() {
        return showRoom;
    }

    public void setShowRoom(ShowRoom showRoom) {
        this.showRoom = showRoom;
    }

    public Double getAdvancePayment() {
        return advancePayment;
    }

    public void setAdvancePayment(Double advancePayment) {
        this.advancePayment = advancePayment;
    }

    public Warehouse getWarehouse() {
        return warehouse;
    }

    public void setWarehouse(Warehouse warehouse) {
        this.warehouse = warehouse;
    }
}
