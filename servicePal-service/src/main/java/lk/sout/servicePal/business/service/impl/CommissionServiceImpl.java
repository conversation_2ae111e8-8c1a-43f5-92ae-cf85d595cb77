package lk.sout.servicePal.business.service.impl;

import lk.sout.servicePal.business.entity.Commission;
import lk.sout.servicePal.business.repository.CommissionRepository;
import lk.sout.servicePal.business.repository.CustomCommissionRepository;
import lk.sout.servicePal.business.service.CommissionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by Madhawa Weerasinghe on 5/5/2020
 */
@Service
public class CommissionServiceImpl implements CommissionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommissionServiceImpl.class);

    @Autowired
    CommissionRepository commissionRepository;

    @Autowired
    CustomCommissionRepository customCommissionRepository;

    @Override
    public boolean save(Commission commission) {
        try {
            commissionRepository.save(commission);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Saving Commission Failed " + ex.getMessage());
            return false;
        }
    }

    @Override
    public List<Commission> findAll() {
        try {
            return customCommissionRepository.findAllForTable();
        } catch (Exception ex) {
            LOGGER.error("Find All Commissions Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Commission> findByJobNo(String jobNo) {
        try {
            return commissionRepository.findByJobNo(jobNo);
        } catch (Exception ex) {
            LOGGER.error("Find Commissions by Job No Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Commission> findByEmpIdAndDateBetween(String empId, LocalDate sDate, LocalDate eDate) {
        try {
            return commissionRepository.findByEmployeeAndDateBetween(empId, sDate, eDate);
        } catch (Exception ex) {
            LOGGER.error("Find Commissions by Job No Failed " + ex.getMessage());
            return null;
        }
    }

}
