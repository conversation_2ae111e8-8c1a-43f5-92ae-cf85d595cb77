package lk.sout.servicePal.business.service;

import lk.sout.servicePal.business.entity.MachineCategory;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 12/15/2019
 */
public interface MachineCategoryService {

    boolean save(MachineCategory machineCategory);

    Iterable<MachineCategory> findAll(Pageable pageable);

    List<MachineCategory> findByActive(boolean active);

    MachineCategory findById(String id);

    List<MachineCategory> findByName(String key);
}
