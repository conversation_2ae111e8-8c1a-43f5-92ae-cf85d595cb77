package lk.sout.servicePal.business.controller;

import lk.sout.servicePal.business.entity.MachineCategory;
import lk.sout.servicePal.business.service.MachineCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 12/16/2019
 */

@RestController
@RequestMapping("/machineCategory")
public class MachineCategoryController {

    @Autowired
    private MachineCategoryService machineCategoryService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody MachineCategory machineCategory) {
        try {
            return ResponseEntity.ok(machineCategoryService.save(machineCategory));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public ResponseEntity<?> findAllCustomers(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        return ResponseEntity.ok(machineCategoryService.findAll(PageRequest.of(Integer.parseInt(page),
                Integer.parseInt(pageSize))));
    }

    @RequestMapping(value = "/findByActive", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByActive(@RequestParam boolean active) {
        try {
            return ResponseEntity.ok(machineCategoryService.findByActive(active));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByName", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByName(@RequestParam String key) {
        try {
            return ResponseEntity.ok(machineCategoryService.findByName(key));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }


    @RequestMapping(value = "/findById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findById(@RequestParam String id) {
        try {
            return ResponseEntity.ok(machineCategoryService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }

    }

}
