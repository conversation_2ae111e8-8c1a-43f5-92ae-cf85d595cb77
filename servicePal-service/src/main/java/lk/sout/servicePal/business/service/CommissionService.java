package lk.sout.servicePal.business.service;

import lk.sout.servicePal.business.entity.Commission;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 5/5/2020
 */

public interface CommissionService {

    boolean save(Commission commission);

    List<Commission> findAll();

    List<Commission> findByJobNo(String jobNo);

    List<Commission> findByEmpIdAndDateBetween(String empId, LocalDate sDate, LocalDate eDate);
}
