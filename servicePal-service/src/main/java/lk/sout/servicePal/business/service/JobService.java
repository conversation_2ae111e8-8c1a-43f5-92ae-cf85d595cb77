package lk.sout.servicePal.business.service;

import lk.sout.core.entity.Response;
import lk.sout.servicePal.business.entity.Job;
import lk.sout.servicePal.business.entity.JobEstimate;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 12/15/2019
 */
public interface JobService {

    Response save(Job job);

    boolean updateJob(Job job);

    Iterable<Job> findAll(Pageable pageable);

    Iterable<Job> findByStatus(String id, Pageable pageable);

    Job findByJobNo(String id);

    Job findByWoNo(String woNo);

    List<Job> findWoNo(String woNo);

    List<Job> searchBySerialNo(String serialNo);

    List<Job> searchByCustomerId(String customerId);

    List<Job> searchByCustomerNic(String customerNic);

    Job findById(String id);

    boolean saveEstimate(JobEstimate jobEstimate);

    boolean approveEstimate(String jobNo, LocalDate estimateApprovedDate);

    Response completingJob(Job job);

    boolean changeAssignment(String jobNo, String empNo);

    Iterable<Job> findJobsInProgress(Pageable pageable);

    Iterable<Job> findNewJobs(Pageable pageable);

    Iterable<Job> findCompleted(Pageable pageable);

    Response updateRemark(String jobNo, String remark);

    Response topUpAdvancePayment(String jobNo, Double amount);
}
