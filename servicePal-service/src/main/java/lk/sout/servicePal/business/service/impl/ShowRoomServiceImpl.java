package lk.sout.servicePal.business.service.impl;

import lk.sout.core.entity.Response;
import lk.sout.servicePal.business.entity.ShowRoom;
import lk.sout.servicePal.business.repository.ShowRoomRepository;
import lk.sout.servicePal.business.service.ShowRoomService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 12/15/2019
 */

@Service
public class ShowRoomServiceImpl implements ShowRoomService {

    private static final Logger LOGGER = LoggerFactory.getLogger(JobServiceImpl.class);

    @Autowired
    ShowRoomRepository showRoomRepository;

    @Autowired
    Response response;

    @Override
    public boolean save(ShowRoom showRoom) {
        try {
            showRoomRepository.save(showRoom);
            response.setCode(200);
            response.setMessage("Showroom Created Successfully");
            return true;
        } catch (Exception ex) {
            LOGGER.error("Creating Showroom Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Creating Showroom Failed");
            response.setData(ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<ShowRoom> findAll(Pageable pageable) {
        try {
            return showRoomRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All ShowRooms Pageable Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<ShowRoom> findAll() {
        try {
            return showRoomRepository.findAll();
        } catch (Exception ex) {
            LOGGER.error("Find All ShowRooms Failed " + ex.getMessage());
            return null;
        }
    }
}
