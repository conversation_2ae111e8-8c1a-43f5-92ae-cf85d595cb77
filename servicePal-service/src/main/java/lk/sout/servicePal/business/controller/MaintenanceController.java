package lk.sout.servicePal.business.controller;

import lk.sout.servicePal.business.service.MaintenanceRecordService;
import lk.sout.servicePal.business.service.MaintenanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 12/16/2019
 */

@RestController
@RequestMapping("/maintenance")
public class MaintenanceController {

    @Autowired
    private MaintenanceRecordService maintenanceRecordService;

    @Autowired
    private MaintenanceService maintenanceService;

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public ResponseEntity<?> findAllCustomers(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        return ResponseEntity.ok(maintenanceService.findAllByActive(PageRequest.of(Integer.parseInt(page),
                Integer.parseInt(pageSize))));
    }

    @RequestMapping(value = "/findById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findById(@RequestParam String id) {
        try {
            return ResponseEntity.ok(maintenanceService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findBySerialNo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findBySerialNo(@RequestParam String serialNo) {
        try {
            return ResponseEntity.ok(maintenanceService.findBySerialNo(serialNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchBySerialNoLike", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> searchBySerialNoLike(@RequestParam String serialNo) {
        try {
            return ResponseEntity.ok(maintenanceService.searchBySerialNoLike(serialNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByCustomer", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByCustomer(@RequestParam String customerId, @RequestParam("page") String page,
                                             @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(maintenanceService.findByCustomer(customerId, PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

}
