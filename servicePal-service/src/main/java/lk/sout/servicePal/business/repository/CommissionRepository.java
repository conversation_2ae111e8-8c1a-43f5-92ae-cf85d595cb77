package lk.sout.servicePal.business.repository;

import lk.sout.servicePal.business.entity.Commission;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasing<PERSON> on 5/5/2020
 */
@Repository
public interface CommissionRepository extends MongoRepository<Commission, String> {

    List<Commission> findByJobNo(String jobNo);

    List<Commission> findByEmployeeAndDateBetween(String empId, LocalDate sDate, LocalDate eDate);

}
