package lk.sout.servicePal.business.repository;

import lk.sout.servicePal.business.entity.Machine;
import lk.sout.servicePal.business.entity.Maintenance;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasing<PERSON> on 12/15/2019
 */
@Repository
public interface MaintenanceRepository extends MongoRepository<Maintenance, String> {

    /*Page<Maintenance> findAllByActive(boolean active, Pageable pageable);

    Page<Maintenance> findByBrand(String brandId, Pageable pageable);

    //List<Maintenance> findBySerialNoAndActive(String serialNo,boolean active);

    Page<Maintenance> findByCustomer(String customerId, Pageable pageable);

    List<Maintenance> findTop10BySerialNoLikeIgnoreCaseAndActive(String serialNo, boolean active);*/

   // Page<Maintenance> findByModelNoLikeIgnoreCaseAndActive(String serialNo, boolean active, Pageable pageable);
}
