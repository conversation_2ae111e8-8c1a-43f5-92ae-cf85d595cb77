package lk.sout.servicePal.business.service.impl;

import lk.sout.core.entity.Response;
import lk.sout.servicePal.business.entity.MachineCategory;
import lk.sout.servicePal.business.repository.MachineCategoryRepository;
import lk.sout.servicePal.business.service.MachineCategoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by Madhawa Weerasinghe on 12/15/2019
 */

@Service
public class MachineCategoryServiceImpl implements MachineCategoryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(JobServiceImpl.class);

    @Autowired
    MachineCategoryRepository machineCategoryRepository;

    @Autowired
    Response response;

    @Override
    public boolean save(MachineCategory machineCategory) {
        try {
            machineCategoryRepository.save(machineCategory);
            response.setCode(200);
            response.setMessage("MachineCategory Created Successfully");
            return true;
        } catch (Exception ex) {
            LOGGER.error("Creating MachineCategory Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Creating MachineCategory Failed");
            response.setData(ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<MachineCategory> findAll(Pageable pageable) {
        try {
            return machineCategoryRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Machine Categories Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public MachineCategory findById(String id) {
        try {
            return machineCategoryRepository.findById(id).get();
        } catch (Exception ex) {
            LOGGER.error("Find All Machine Categories Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<MachineCategory> findByName(String key) {
        try {
            return machineCategoryRepository.findAllByNameLike(key);
        } catch (Exception ex) {
            LOGGER.error("Find All Machine by name Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<MachineCategory> findByActive(boolean active) {
        try {
            return machineCategoryRepository.findAllByActive(active);
        } catch (Exception ex) {
            LOGGER.error("Find All Machine Categories by status Failed " + ex.getMessage());
            return null;
        }
    }
}
