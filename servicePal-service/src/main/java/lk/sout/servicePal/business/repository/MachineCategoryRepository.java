package lk.sout.servicePal.business.repository;

import lk.sout.servicePal.business.entity.MachineCategory;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 12/15/2019
 */
public interface MachineCategoryRepository extends MongoRepository<MachineCategory, String> {

    List<MachineCategory> findAllByActive(boolean active);

    List<MachineCategory> findAllByNameLike(String name);
}
