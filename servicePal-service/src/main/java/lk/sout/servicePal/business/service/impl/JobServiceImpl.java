package lk.sout.servicePal.business.service.impl;

import lk.sout.core.entity.*;
import lk.sout.core.service.*;
import lk.sout.servicePal.business.entity.*;
import lk.sout.servicePal.business.repository.CustomJobRepository;
import lk.sout.servicePal.business.repository.JobRepository;
import lk.sout.servicePal.business.service.CommissionService;
import lk.sout.servicePal.business.service.JobService;
import lk.sout.servicePal.business.service.MachineService;
import lk.sout.servicePal.hr.entity.Employee;
import lk.sout.servicePal.hr.service.EmployeeService;
import lk.sout.servicePal.inventory.entity.Item;
import lk.sout.servicePal.inventory.entity.Stock;
import lk.sout.servicePal.inventory.entity.Warehouse;
import lk.sout.servicePal.inventory.service.ItemService;
import lk.sout.servicePal.inventory.service.StockService;
import lk.sout.servicePal.inventory.service.WarehouseService;
import lk.sout.servicePal.trade.service.CustomerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Madhawa Weerasinghe on 12/15/2019
 */

@Service
public class JobServiceImpl implements JobService {

    private static final Logger LOGGER = LoggerFactory.getLogger(JobServiceImpl.class);

    @Autowired
    JobRepository jobRepository;

    @Autowired
    CustomJobRepository customJobRepository;

    @Autowired
    Response response;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    SequenceService sequenceService;

    @Autowired
    EmployeeService employeeService;

    @Autowired
    Sequence sequence;

    @Autowired
    CommissionService commissionService;

    @Autowired
    MachineService machineService;

    @Autowired
    CustomerService customerService;

    @Autowired
    ItemService itemService;

    @Autowired
    Commission commission;

    @Autowired
    Transaction transaction;

    @Autowired
    TransactionService transactionService;

    @Autowired
    UserService userService;

    @Autowired
    StockService stockService;

    @Override
    @Transactional
    public Response save(Job job) {
        try {
            // Generate Job Number Atomically
            String seqId = sequenceService.getNextSequence("Job No");
            job.setJobNo(seqId);

            // Determine Job Status
            MetaData jobStatus = (job.getAssignments().size() > 0) ?
                    metaDataService.searchMetaData("Job Assigned", "Job Status") :
                    metaDataService.searchMetaData("Job Created", "Job Status");
            job.setJobStatus(jobStatus);

            // Save Job
            Job savedJob = jobRepository.save(job);

            // Handle Advance Payment
            if (job.getAdvancePayment() != null && job.getAdvancePayment().compareTo(0.0) > 0) {
                MetaData siType = metaDataService.searchMetaData("SalesInvoice", "Income");

                Transaction transaction = new Transaction();
                transaction.setAmount(job.getAdvancePayment());
                transaction.setOperator("+");
                transaction.setRefNo(savedJob.getJobNo());
                transaction.setRefType("Job Advance");
                transaction.setType(siType);
                transaction.setDate(LocalDateTime.now());
                transactionService.save(transaction);
            }

            // Prepare Response
            response.setCode(200);
            response.setMessage("Job Created Successfully");
            response.setData(job.getId());
            return response;
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Creating Job Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Creating Job Failed");
            response.setData(ex.getMessage());
            return response;
        }
    }

    @Override
    public boolean updateJob(Job job) {
        try {
            jobRepository.save(job);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Updating job failed " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<Job> findAll(Pageable pageable) {
        try {
            return customJobRepository.findAllForTable(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Jobs Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Job> findByStatus(String id, Pageable pageable) {
        try {
            return jobRepository.findAllByJobStatus(id, pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Jobs by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Job findByJobNo(String id) {
        try {
            return jobRepository.findByJobNo(id);
        } catch (Exception ex) {
            LOGGER.error("Find All Jobs by Job No Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Job> findWoNo(String woNo) {
        try {
            return jobRepository.findByWoNumberLikeIgnoreCase(woNo);
        } catch (Exception ex) {
            LOGGER.error("Find All Jobs by W.O No Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Job findByWoNo(String woNo) {
        try {
            return jobRepository.findByWoNumber(woNo);
        } catch (Exception ex) {
            LOGGER.error("Find Jobs by W.O No Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Job> searchBySerialNo(String serialNo) {
        try {
            return jobRepository.findByMachine(machineService.findBySerialNo(serialNo));
        } catch (Exception ex) {
            LOGGER.error("Find Jobs by Serial No Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Job> searchByCustomerId(String customerId) {
        try {
            return jobRepository.findByCustomer(customerId);
        } catch (Exception ex) {
            LOGGER.error("Find Jobs by Customer Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Job> searchByCustomerNic(String customerNic) {
        try {
            return jobRepository.findByCustomer(customerService.findByNicBr(customerNic).getId());
        } catch (Exception ex) {
            LOGGER.error("Find Jobs by Customer Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Job findById(String id) {
        try {
            return jobRepository.findById(id).get();
        } catch (Exception ex) {
            LOGGER.error("Find All Jobs by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean saveEstimate(JobEstimate jobEstimate) {
        try {
            Job job = findByJobNo(jobEstimate.getJobNo());
            if (job == null) {
                LOGGER.error("Job not found for job number: {}", jobEstimate.getJobNo());
                return false;
            }

            MetaData estimatedStatus = metaDataService.searchMetaData("Job Estimated", "Job Status");
            MetaData createdStatus = metaDataService.searchMetaData("Job Created", "Job Status");
            MetaData assignedStatus = metaDataService.searchMetaData("Job Assigned", "Job Status");

            if (estimatedStatus == null || createdStatus == null || assignedStatus == null) {
                LOGGER.error("Required metadata not found");
                return false;
            }

            jobEstimate.setPreparedBy(SecurityContextHolder.getContext().getAuthentication().getName());
            job.setJobEstimate(jobEstimate);

            if (jobEstimate.getId() != null) {
                job.setEstimateCreatedDate(LocalDate.now());
            }

            if (job.getJobStatus() != null &&
                    (job.getJobStatus().getId().equals(createdStatus.getId()) ||
                            job.getJobStatus().getId().equals(assignedStatus.getId()))) {
                job.setJobStatus(estimatedStatus);
            }

            jobRepository.save(job);
            return true;
        } catch (Exception ex) {
            LOGGER.error("An unexpected error occurred: {}", ex.getMessage(), ex);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean approveEstimate(String jobNo, LocalDate estimateApprovedDate) {
        try {
            Job job = findByJobNo(jobNo);
            MetaData estimatedStatus = metaDataService.searchMetaData("Job Estimated", "Job Status");
            MetaData inProgressStatus = metaDataService.searchMetaData("Job In Progress", "Job Status");
            job.setEstimateApprovedDate(estimateApprovedDate);
            job.getJobEstimate().setApproved(true);
            job.getJobEstimate().setApprovedBy(SecurityContextHolder.getContext().getAuthentication().getName());
            if (job.getJobStatus().getId().equals(estimatedStatus.getId())) {
                job.setJobStatus(inProgressStatus);
            }
            jobRepository.save(job);
            return true;
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Changing Assignment Failed " + ex.getMessage());
            return false;
        }
    }

    @Override
    @Transactional
    // sending only necessary data to complete the job with this job obj
    public Response completingJob(Job job) {
        try {
            Job savedJob = findByJobNo(job.getJobNo());

            User user = userService.findLoggedInUser();

            if (null != savedJob.getJobEstimate()) {
                List<JobEstimateRecord> jobEstimateRecords = savedJob.getJobEstimate().getJobEstimateRecordList();
                if (null != jobEstimateRecords) {
                    for (JobEstimateRecord jer : jobEstimateRecords) {
                        Item item = itemService.findOneByBarcode(jer.getBarcode());
                        Stock stock = stockService.findByItemCodeAndWarehouse(item.getItemCode(), user.getWarehouseCode());
                        if (!item.getItemCode().equals("0") && stock.getQuantity().compareTo((jer.getQuantity())) < 0) {
                            response.setCode(150);
                            response.setMessage("Stock Not Available");
                            response.setData("Stock Not Available");
                            return response;
                        }
                    }

                    for (JobEstimateRecord jer : jobEstimateRecords) {
                        Item item = itemService.findOneByBarcode(jer.getBarcode());
                        Stock stock = stockService.findByItemCodeAndWarehouse(item.getItemCode(), user.getWarehouseCode());
                        if (!item.getItemCode().equals("0") && stock.getQuantity().compareTo((jer.getQuantity())) >= 0) {
                            stockService.deductFromStock(item.getItemCode(), user.getWarehouseCode(), jer.getQuantity());
                        }
                    }
                }
            }

            if (null != job.getJobCommissions()) {
                for (JobCommission jobCommission : job.getJobCommissions()) {
                    commission.setId(null);
                    commission.setAmount(jobCommission.getCommission());
                    commission.setDate(LocalDate.now());
                    commission.setEmployee(jobCommission.getEmployee());
                    commission.setEmpName(jobCommission.getEmployee().getName());
                    commission.setJobNo(job.getJobNo());
                    commissionService.save(commission);
                }
            }

            MetaData completedStatus = metaDataService.searchMetaData("Job Completed", "Job Status");
            savedJob.setInspectedBy(job.getInspectedBy());
            savedJob.setJobStatus(completedStatus);
            savedJob.setCompletedDate(job.getCompletedDate());
            jobRepository.save(savedJob);
            response.setCode(200);
            response.setMessage("Job Completed Successfully");
            return response;
        } catch (
                Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Completing Job Failed " + ex.getMessage());
            response.setCode(501);
            response.setMessage("Completing Job Failed");
            response.setData(ex.getMessage());
            ex.printStackTrace();
            return response;
        }

    }

    @Override
    public boolean changeAssignment(String jobNo, String empId) {
        try {
            Job job = findByJobNo(jobNo);
            Employee emp = employeeService.findById(empId).get();
            MetaData createdStatus = metaDataService.searchMetaData("Job Created", "Job Status");
            MetaData assignedStatus = metaDataService.searchMetaData("Job Assigned", "Job Status");
            List<Employee> employees = new ArrayList<>();
            employees.add(emp);
            if (job.getJobStatus().getId().equals(createdStatus.getId())) {
                job.setJobStatus(assignedStatus);
            }
            jobRepository.save(job);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Changing Assignment Failed " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<Job> findJobsInProgress(Pageable pageable) {
        try {
            MetaData inProgress = metaDataService.searchMetaData("Job In Progress", "Job Status");
            return jobRepository.findAllByJobStatus(inProgress.getId(), pageable);
        } catch (Exception ex) {
            LOGGER.error("Find Jobs InProgress Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Job> findNewJobs(Pageable pageable) {
        try {
            MetaData created = metaDataService.searchMetaData("Job Created", "Job Status");
            MetaData assigned = metaDataService.searchMetaData("Job Assigned", "Job Status");
            MetaData estimated = metaDataService.searchMetaData("Job Estimated", "Job Status");
            String[] ids = {created.getId(), assigned.getId(), estimated.getId()};
            return jobRepository.findAllByJobStatusIn(ids, pageable);
        } catch (Exception ex) {
            LOGGER.error("Find New Jobs Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Job> findCompleted(Pageable pageable) {
        try {
            MetaData completed = metaDataService.searchMetaData("Job Completed", "Job Status");
            return jobRepository.findAllByJobStatus(completed.getId(), pageable);
        } catch (Exception ex) {
            LOGGER.error("Find Completed Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Response updateRemark(String jobNo, String remark) {
        try {
            Job job = jobRepository.findByJobNo(jobNo);
            String remark2 = (null != job.getRemark() ? job.getRemark() : "");
            if (!remark.isEmpty()) {
                job.setRemark(remark2 + "\r\n" + remark);
                jobRepository.save(job);
            }
            response.setMessage("Remark Updated");
            response.setCode(200);
            return response;
        } catch (Exception ex) {
            LOGGER.error("Saving Remark Failed " + ex.getMessage());
            response.setMessage("Remark Not Updated");
            response.setCode(500);
            return response;
        }
    }

    @Override
    @Transactional
    public Response topUpAdvancePayment(String jobNo, Double amount) {
        try {
            Job job = jobRepository.findByJobNo(jobNo);
            if (!job.getJobStatus().getId().equals(metaDataService.searchMetaData("Job Closed",
                    "Job Status").getId()) && amount.compareTo(0.0) > 0) {
                Double advanceAvailable = (null != job.getAdvancePayment() ? job.getAdvancePayment() : 0.0);
                job.setAdvancePayment(advanceAvailable + amount);
                jobRepository.save(job);

                MetaData incomeType = metaDataService.searchMetaData("Advance Payment", "Income");

                transaction = new Transaction();
                transaction.setAmount(amount);
                transaction.setOperator("+");
                transaction.setRefNo(jobNo);
                transaction.setRefType("Advance Payment");
                transaction.setType(incomeType);
                transaction.setDate(LocalDateTime.now());
                transactionService.save(transaction);
            }
            response.setMessage("Job Advance Updated");
            response.setCode(200);
            return response;
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Job advance Failed " + ex.getMessage());
            response.setMessage("Job Advance Not Updated");
            response.setCode(500);
            return response;
        }
    }

}
