package lk.sout.servicePal.business.service.impl;

import lk.sout.core.entity.Response;
import lk.sout.servicePal.business.entity.MaintenanceRecord;
import lk.sout.servicePal.business.repository.MaintenanceRecordRepository;
import lk.sout.servicePal.business.service.MaintenanceRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by Madhawa Weerasinghe on 12/15/2019
 */

@Service
public class MaintenanceRecordRecordServiceImpl implements MaintenanceRecordService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MaintenanceRecordRecordServiceImpl.class);

    @Autowired
    MaintenanceRecordRepository maintenanceRecordRepository;

    @Autowired
    Response response;

    @Override
    public Iterable<MaintenanceRecord> findAll(Pageable pageable) {
        try {
            return maintenanceRecordRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Machine Categories Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public MaintenanceRecord findById(String id) {
        try {
            return maintenanceRecordRepository.findById(id).get();
        } catch (Exception ex) {
            LOGGER.error("Find All Machine Categories by status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public MaintenanceRecord findBySerialNo(String serial) {
        try {
            return maintenanceRecordRepository.findByMachineSerial(serial);
        } catch (Exception ex) {
            LOGGER.error("Find By Machine Serial Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<MaintenanceRecord> findByCustomer(String customerId, Pageable pageable) {
        try {
            return maintenanceRecordRepository.findByCustomer(customerId, pageable);
        } catch (Exception ex) {
            LOGGER.error("Find By Customer Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<MaintenanceRecord> searchBySerialNoLike(String serial) {
        try {
            return maintenanceRecordRepository.findByMachineSerialLikeIgnoreCase(serial);
        } catch (Exception ex) {
            LOGGER.error("Search By Machine Serial Failed " + ex.getMessage());
            return null;
        }
    }

}
