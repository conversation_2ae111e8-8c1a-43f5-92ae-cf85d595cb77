package lk.sout.servicePal.business.controller;

import lk.sout.servicePal.business.service.CommissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;

@RestController
@RequestMapping("/commission")
public class CommissionController {

    @Autowired
    CommissionService commissionService;

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public ResponseEntity<?> findAllCustomers() {
        return ResponseEntity.ok(commissionService.findAll());
    }


    @RequestMapping(value = "/findByJobNo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByJobNo(@RequestParam String jobNo) {
        try {
            return ResponseEntity.ok(commissionService.findByJobNo(jobNo));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByEmpIdAndDateBetween", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByEmpIdAndDateBetween(@RequestParam String empId, @RequestParam String sDate,
                                                        @RequestParam String eDate) {
        try {
            return ResponseEntity.ok(commissionService.findByEmpIdAndDateBetween(empId, LocalDate.parse(sDate),
                    LocalDate.parse(eDate)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }


}
