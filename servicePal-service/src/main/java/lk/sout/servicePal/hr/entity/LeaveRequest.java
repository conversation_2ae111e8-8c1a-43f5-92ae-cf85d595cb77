package lk.sout.servicePal.hr.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Document
public class LeaveRequest {
    @Id
    private String id;
    private Date from;
    private Date to;

    @DBRef
    private Employee employee;
    private Employee coveringEmp;
    private String reason;

    private Leave type;

    private String epf;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getFrom() {
        return from;
    }

    public void setFrom(Date from) {
        this.from = from;
    }

    public Date getTo() {
        return to;
    }

    public void setTo(Date to) {
        this.to = to;
    }

    public Employee getEmployee() {
        return employee;
    }

    public void setEmployee(Employee employee) {
        this.employee = employee;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getEpf() {
        return epf;
    }

    public void setEpf(String epf) {
        this.epf = epf;
    }

    public Employee getCoveringEmp() {
        return coveringEmp;
    }

    public void setCoveringEmp(Employee coveringEmp) {
        this.coveringEmp = coveringEmp;
    }

    public Leave getType() {
        return type;
    }

    public void setType(Leave type) {
        this.type = type;
    }
}
