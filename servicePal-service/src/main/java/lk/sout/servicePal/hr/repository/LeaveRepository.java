package lk.sout.servicePal.hr.repository;

import lk.sout.servicePal.hr.entity.Leave;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface LeaveRepository extends MongoRepository<Leave, String> {

    Page<Leave> findAll(Pageable pageable);

    Leave findByLeaveType(String s);

}
