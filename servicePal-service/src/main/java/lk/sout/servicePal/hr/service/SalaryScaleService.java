package lk.sout.servicePal.hr.service;

import lk.sout.servicePal.hr.entity.SalaryScale;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface SalaryScaleService {

    boolean save(SalaryScale salaryScale);

    Iterable<SalaryScale> findAll(Pageable pageable);

    List<SalaryScale> findByName(String any);

    SalaryScale findById(String id);

    List<SalaryScale> findAllSalaryScale();

    boolean findBySalaryScaleName(String name);
}

