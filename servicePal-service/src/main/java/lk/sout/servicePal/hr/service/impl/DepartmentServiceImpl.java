package lk.sout.servicePal.hr.service.impl;

import lk.sout.servicePal.hr.entity.Department;
import lk.sout.servicePal.hr.repository.DepartmentRepository;
import lk.sout.servicePal.hr.service.DepartmentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class DepartmentServiceImpl implements DepartmentService {

    final static Logger LOGGER = LoggerFactory.getLogger(DepartmentServiceImpl.class);

    @Autowired
    DepartmentRepository departmentRepository;

    @Override
    public boolean save(Department department) {
        try {
            departmentRepository.save(department);
            return true;
        }catch (Exception e){
            return false;
        }
    }

    @Override
    public Iterable<Department> findAll(Pageable pageable) {
        try {
            return departmentRepository.findAll(pageable);

        }catch (Exception e){
            return null;
        }
    }

    @Override
    public List<Department> findAllDepartment() {
        try {
            return departmentRepository.findAll();

        }catch (Exception e){
            return null;
        }
    }

    @Override
    public boolean findByDepartmentName(String name) {
        try {
            Department department = departmentRepository.findByDepartmentName(name);
            if (department != null) {
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            LOGGER.error("check Designation Failed: " + ex.getMessage());
            return false;
        }
    }


    @Override
    public Optional<Department> findDepartment(String s) {
        try {
            return departmentRepository.findById(s);
        }catch (Exception e){
            return null;
        }
    }}
