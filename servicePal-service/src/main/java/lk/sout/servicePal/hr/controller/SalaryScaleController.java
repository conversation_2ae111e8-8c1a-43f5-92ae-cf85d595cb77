package lk.sout.servicePal.hr.controller;
import lk.sout.servicePal.hr.entity.SalaryScale;
import lk.sout.servicePal.hr.service.SalaryScaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/salaryScale")
public class SalaryScaleController {

    @Autowired
    SalaryScaleService salaryScaleService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody SalaryScale salaryScale) {
        try {
            return ResponseEntity.ok(salaryScaleService.save(salaryScale));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }

    }


    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    private ResponseEntity<?> getAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(salaryScaleService.findAll(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/searchByName", method = RequestMethod.GET)
    private ResponseEntity<?> searchByName (@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(salaryScaleService.findByName(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
    @RequestMapping(value = "/searchByID", method = RequestMethod.GET)
    private ResponseEntity<?> searchByID (@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(salaryScaleService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAll" ,method = RequestMethod.GET)
    private ResponseEntity<?> findAll(){
        try {
            return ResponseEntity.ok(salaryScaleService.findAllSalaryScale());
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findBySalaryScaleName", method = RequestMethod.GET)
    private ResponseEntity<?> findBySalaryScaleName (@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(salaryScaleService.findBySalaryScaleName(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}
