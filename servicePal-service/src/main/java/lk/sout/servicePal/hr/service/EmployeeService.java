package lk.sout.servicePal.hr.service;

import lk.sout.servicePal.hr.entity.Employee;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 6/21/2018
 */
public interface EmployeeService {

    boolean save(Employee employee);

    Employee findByUsername(String username);

    String remove(Employee employee);

    Optional<Employee> findById(String id);

    List<Employee> findEmployeeByNameLike(String key);

    Iterable<Employee> findAll(Pageable pageable);

    boolean checkEpf(String epf);

    boolean checkNic(String nic);
}
