package lk.sout.servicePal.hr.repository;

import lk.sout.servicePal.hr.entity.Employee;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 6/18/2018
 */
@Repository
public interface EmployeeRepository extends MongoRepository<Employee, String> {

    List<Employee> findByNameLikeIgnoreCase(String key, boolean b);

    Page<Employee> findAll(Pageable pageable);

    Employee findByUserName(String username);

    Employee findByEpfNo(String no);

    Employee findByNic(String nic);
}
