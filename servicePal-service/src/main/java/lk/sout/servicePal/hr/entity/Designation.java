package lk.sout.servicePal.hr.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;


/**
 * Created by <PERSON><PERSON><PERSON> on 4/24/2018
 */
@Document
@Component
public class Designation {

    @Id
    private String id;

    private String designationName;

    private boolean active;

    public String getId() { return id;
    }

    public void setId(String id) { this.id = id;
    }

    public String getDesignationName() {
        return designationName;
    }

    public void setDesignationName(String designationName) {
        this.designationName = designationName;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        active = active;
    }
}
