package lk.sout.core.service.impl;

import lk.sout.core.entity.Permission;
import lk.sout.core.repository.ModuleRepository;
import lk.sout.core.repository.PermissionRepository;
import lk.sout.core.service.CommonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 5/3/2019
 */
@Service
public class CommonServiceImpl implements CommonService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommonServiceImpl.class);

    @Autowired
    PermissionRepository permissionRepository;

    @Autowired
    ModuleRepository moduleRepository;

    @Override
    public List<Permission> findRelatedRoutes(String route) {
        try {
            Permission permission = permissionRepository.findByRoute(route);
            return permissionRepository.findByModule(permission.getModule());
        } catch (Exception ex) {
            LOGGER.error("find related routes Failed: " + ex.getMessage());
            return null;
        }
    }
}
