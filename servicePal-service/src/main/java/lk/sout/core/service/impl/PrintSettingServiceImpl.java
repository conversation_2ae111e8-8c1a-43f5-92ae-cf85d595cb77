package lk.sout.core.service.impl;

import lk.sout.core.entity.PrintSetting;
import lk.sout.core.repository.PrintSettingRepository;
import lk.sout.core.service.PrintSettingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PrintSettingServiceImpl implements PrintSettingService {

    final static Logger LOGGER = LoggerFactory.getLogger(PrintSettingServiceImpl.class);

    @Autowired
    private PrintSettingRepository printSettingRepository;

    public PrintSetting findAll() {
        try {
            return printSettingRepository.findAll().get(0);
        } catch (Exception ex) {
            LOGGER.error("print setting retrieving failed" + ex.getMessage());
            return null;
        }
    }

    public String save(PrintSetting printSetting) {
        try {
            printSettingRepository.save(printSetting);
            return "success";
        } catch (Exception ex) {
            LOGGER.error("creating print setting failed " + ex.getMessage());
            return ex.getMessage();
        }
    }
}
