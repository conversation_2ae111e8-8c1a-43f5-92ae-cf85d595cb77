
package lk.sout.core.service.impl;

import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Transaction;
import lk.sout.core.repository.TransactionRepository;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.TransactionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class TransactionServiceImpl implements TransactionService {

    final static Logger LOGGER = LoggerFactory.getLogger(TransactionServiceImpl.class);

    @Autowired
    TransactionRepository transactionRepository;

    @Autowired
    MetaDataService metaDataService;

    @Override
    public boolean save(Transaction transaction) {
        try {
            transactionRepository.save(transaction);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public Iterable<Transaction> findAll(Pageable pageable) {
        try {
            return transactionRepository.findAll(pageable);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Transaction> findByType(MetaData type) {
        try {
            return transactionRepository.findAllByType(type);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Transaction> findByThirdParty(String thirdParty) {
        try {
            return transactionRepository.findAllByThirdParty(thirdParty);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Transaction> findAllByDateBetween(LocalDate startDate, LocalDate endDate) {
        try {
            return transactionRepository.findAllByCreatedDateBetween(startDate.atStartOfDay(), endDate.atStartOfDay());
        } catch (Exception e) {
            return null;
        }
    }

    public Transaction findByRefNoAndRefType(String ref, String refType) {
        try {
            return transactionRepository.findByRefNoAndRefType(ref, refType);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Transaction> findByDateRangeAndOperator(LocalDate startDate, LocalDate endDate, String operator) {
        try {
            if (operator.equals("plus")) {
                operator = "+";
            } else {
                operator = "-";
            }
            List<Transaction> records = transactionRepository.findByOperatorAndDateBetween(operator, startDate.atStartOfDay(),
                    endDate.atStartOfDay());
            return records;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<Transaction> findByRangeFilterAndOperator(String rangeId, String operator) {
        try {
            MetaData range = metaDataService.findById(rangeId);
            LocalDate sDate = LocalDate.now();
            LocalDate eDate = LocalDate.now();
            LocalDate today = LocalDate.now();

            if (operator.equals("plus")) {
                operator = "+";
            } else {
                operator = "-";
            }

            if (range.getValue().equals("Today")) {
                sDate = today;
                eDate = today.plusDays(1);
            }
            if (range.getValue().equals("This Week")) {
                sDate = today.with((DayOfWeek.MONDAY));
                eDate = today.with((DayOfWeek.SUNDAY));
            }
            if (range.getValue().equals("This Month")) {
                sDate = today.withDayOfMonth(1);
                eDate = today.withDayOfMonth(today.lengthOfMonth());
            }
            if (range.getValue().equals("This Year")) {
                sDate = today.withDayOfYear(1);
                eDate = today.withDayOfYear(today.lengthOfYear());
            }

            return transactionRepository.findByOperatorAndDateBetween(operator, sDate.atStartOfDay(), eDate.atStartOfDay());
        } catch (Exception e) {
            return null;
        }
    }

}
