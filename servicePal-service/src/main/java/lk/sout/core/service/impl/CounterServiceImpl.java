package lk.sout.core.service.impl;

import lk.sout.core.entity.Counter;
import lk.sout.core.repository.CounterRepository;
import lk.sout.core.service.CounterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CounterServiceImpl implements CounterService {

    @Autowired
    CounterRepository counterRepository;

    @Override
    public List<Counter> findAll() {
        return counterRepository.findAll();
    }

    @Override
    public Counter findByCounterId(String id) {
        return counterRepository.findCounterById(id);
    }

    @Override
    public List<Counter> searchByNameLike(String counterNo) {
        return counterRepository.findAllByCounterNoLikeIgnoreCase(counterNo);
    }
}
