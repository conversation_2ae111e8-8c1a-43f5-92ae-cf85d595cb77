package lk.sout.core.service.impl;

import lk.sout.core.entity.*;
import lk.sout.core.entity.Module;
import lk.sout.core.repository.ModuleRepository;
import lk.sout.core.repository.PermissionRepository;
import lk.sout.core.repository.UserRepository;
import lk.sout.core.service.UserService;
import lk.sout.servicePal.inventory.entity.Warehouse;
import lk.sout.servicePal.inventory.repository.WarehouseRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class UserServiceImpl implements UserService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserServiceImpl.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ModuleRepository moduleRepository;

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private BCryptPasswordEncoder passwordEncoder;

    @Autowired
    Response response;

    @Autowired
    WarehouseRepository warehouseRepository;

    public List<User> findAll() {
        return userRepository.findAllByActive(true);
    }

    @Override
    public String delete(String id) {
        try {
            userRepository.deleteById(id);
            return "success";
        } catch (Exception ex) {
            LOGGER.error("Removing user failed " + ex.getMessage());
            return "failed";
        }
    }

    @Override
    public User findByUsername(String username) {
        try {
            return userRepository.findByUsername(username);
        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public List<Permission> findAvailablePermissions(String userName) {
        try {
            return userRepository.findByUsername(userName).getPermissions();
        } catch (Exception ex) {
            LOGGER.error("Finding enabled permissions failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Permission> findDesktopPermissions(String userName) {
        try {
            List<Permission> permissions = userRepository.findByUsername(userName).getDesktopPermissions();
            return permissions;
        } catch (Exception ex) {
            LOGGER.error("Finding desktop permissions failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Permission> findPermissionsByModule(String moduleId) {
        try {
            return permissionRepository.findByModule(moduleRepository.findById(moduleId).get());
        } catch (Exception ex) {
            LOGGER.error("Find Permissions by Module " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Warehouse> findWarehouse(String warehouseId) {
        return null;
    }


    @Override
    public boolean saveDesktopPerms(String username, List<Permission> permissions) {
        try {
            User user = userRepository.findByUsername(username);
            user.setDesktopPermissions(permissions);
            userRepository.save(user);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Add Desktop Permission failed " + ex.getMessage());
            return false;
        }
    }

    @Override
    public List<Module> getEnabledModules() {
        try {
            List<Module> modules = moduleRepository.findAllByActivated(true);
            return modules;
        } catch (Exception ex) {
            LOGGER.error("Finding enabled modules failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Warehouse> getEnabledWarehouses() {
        try {
            List<Warehouse> warehouse = warehouseRepository.findAllByActive(true);
            return warehouse;
        } catch (Exception ex) {
            LOGGER.error("Finding enabled warehouses failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Module findModule(String moduleId) {
        return moduleRepository.findById(moduleId).get();
    }

    @Override
    public Permission findPermission(String permissionId) {
        return permissionRepository.findById(permissionId).get();
    }

    @Override
    public Boolean checkName(String username) {
        try {
            if (null != userRepository.findByUsername(username)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            LOGGER.error("check Customer by nic Failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Response update(User user) {
        try {
            userRepository.save(user);
            response.setCode(200);
            response.setMessage("Desktop permission added successfully");
        } catch (Exception e) {
            response.setMessage("Add desktop permission failed");
        }
        return response;
    }

    @Override
    public User searchByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    @Override
    public Permission findPermissionsByName(String permName) {
        return permissionRepository.findByName(permName);
    }

    @Override
    public User findLoggedInUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String userName = authentication.getName();
            User user = userRepository.findByUsername(userName);
            return user;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public boolean isAdminLoggedIn() {
        boolean found = false;
        for (UserRole role : findLoggedInUser().getUserRoles()) {
            if (role.getName().toString().equals("ADMIN")) {
                return true;
            }
        }
        return found;
    }

    @Override
    public boolean isAdminOrManagerLoggedIn() {
        boolean found = false;
        for (UserRole role : findLoggedInUser().getUserRoles()) {
            if (role.getName().toString().equals("ADMIN") ||
                    role.getName().toString().equals("MANAGER")) {
                return true;
            }
        }
        return found;
    }

    @Override
    public boolean setupCounter(String counterId, String userId) {
        try {
            User user = userRepository.findUserById(userId);
            user.setCounter(counterId);
            userRepository.save(user);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public User findOne(String username) {
        return userRepository.findByUsername(username);
    }

    @Override
    public Optional<User> findById(String id) {
        return userRepository.findById(id);
    }

    @Override
    public boolean save(User user) {
        try {
            if (user.getId() == null || !user.getPassword().equals("NOCHNG")) {
                user.setPassword(passwordEncoder.encode(user.getPassword()));
            }
            if (user.getId() != null && user.getPassword().equals("NOCHNG")) {
                user.setPassword(userRepository.findById(user.getId()).get().getPassword());
            }
            userRepository.save(user);
            return true;
        } catch (Exception ex) {
            LOGGER.error("saving user failed: " + ex.getMessage());
            return false;
        }
    }

}
