package lk.sout.core.service;

import lk.sout.core.entity.Expense;
import lk.sout.core.entity.ExpenseType;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;


public interface ExpenseTypeService {

    boolean save(ExpenseType expenseType);

    Iterable<ExpenseType> findAll(Pageable pageable);

    ExpenseType findById(String id);

    List<ExpenseType> findByExpenseTypeNameLike(String s);

    List<ExpenseType> findByExpenseCategory(String id);
}
