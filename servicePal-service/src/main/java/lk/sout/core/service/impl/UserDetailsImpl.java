package lk.sout.core.service.impl;

import lk.sout.core.entity.UserRole;
import lk.sout.core.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by Mad<PERSON>a Weerasinghe on 3/4/2022
 */

@Service(value = "userService")
public class UserDetailsImpl implements UserDetailsService {

    @Autowired
    private UserRepository userRepository;

    @Override
    public UserDetails loadUserByUsername(String userId) throws UsernameNotFoundException {
        lk.sout.core.entity.User user = userRepository.findByUsername(userId);
        if (user == null) {
            throw new UsernameNotFoundException("Invalid username or password.");
        }
        return new User(user.getUsername(), user.getPassword(),
                getAuthority(user.getUserRoles()));
    }

    private Set<GrantedAuthority> getAuthority(List<UserRole> userRoles) {
        Set<GrantedAuthority> ga = new HashSet<>();
        for (UserRole role : userRoles) {
            ga.add(new SimpleGrantedAuthority(role.getName().toString()));
        }
        return ga;
    }

}
