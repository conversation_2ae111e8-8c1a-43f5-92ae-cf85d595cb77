package lk.sout.core.service.impl;

import lk.sout.core.entity.*;
import lk.sout.core.service.ExpenseTypeService;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.TransactionService;
import lk.sout.core.repository.ExpenseRepository;
import lk.sout.servicePal.hr.service.EmployeeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ExpenseServiceImpl implements lk.sout.core.service.ExpenseService {

    final static Logger LOGGER = LoggerFactory.getLogger(ExpenseServiceImpl.class);

    @Autowired
    ExpenseRepository expenseRepository;

    @Autowired
    TransactionService transactionService;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    EmployeeService employeeService;

    @Autowired
    ExpenseTypeService expenseTypeService;
    @Override
    @Transactional
    public boolean save(Expense expense) {
        try {
            if (null != expense.getId()) {
                return false;
            }
            MetaData transactionType = metaDataService.searchMetaData("Other", "Expense");
            ExpenseType expenseType = expenseTypeService.findById(expense.getType().getId());
            expenseRepository.save(expense);
            Transaction transaction = new Transaction();
            transaction.setAmount(expense.getAmount());
            transaction.setOperator("-");
            transaction.setRefType(expenseType.getName());
            transaction.setThirdParty(expenseType.getCategory().getName());
            transaction.setType(transactionType);
            transaction.setRefNo(expense.getId());
            transactionService.save(transaction);
            return true;
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("create expense failed " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<Expense> findAll(Pageable pageable) {
        try {
            return expenseRepository.findAllByOrderByIdDesc(pageable);
        } catch (Exception e) {
            LOGGER.info("Get All Invoices Failed! - " + e.getMessage());
            return null;
        }
    }

    @Override
    public Expense findById(String id) {
        try {
            return expenseRepository.findById(id).get();
        } catch (Exception ex) {
            LOGGER.info("Find by Id failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Expense> findByExpenseCategory(String s, Pageable pageable) {
        try {
            List<ExpenseType> expenseTypes = expenseTypeService.findByExpenseCategory(s);
            return expenseRepository.findAllByTypeIn(expenseTypes.stream().map(ExpenseType::getId).
                    collect(Collectors.toList()), pageable);
        } catch (Exception e) {
            LOGGER.info("Get All Invoice Failed! - " + e.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Expense> findByExpenseType(String s, Pageable pageable) {
        try {
            return expenseRepository.findAllByType(s, pageable);
        } catch (Exception e) {
            LOGGER.info("Get All Invoice Failed! - " + e.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Expense> findByDateBetween(LocalDate from, LocalDate to, Pageable pageable) {
        try {
            return expenseRepository.findAllByDateBetween(from.atStartOfDay(), to.plusDays(1).atStartOfDay(), pageable);
        } catch (Exception ex) {
            LOGGER.error("Retrieving All Rent failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Expense> findByDateRangeFilter(String rangeId, Pageable pageable) {
        try {
            MetaData range = metaDataService.findById(rangeId);
            LocalDate sDate = LocalDate.now();
            LocalDate eDate = LocalDate.now();
            LocalDate today = LocalDate.now();

            if (range.getValue().equals("Today")) {
                sDate = today;
                eDate = today.plusDays(1);
            }
            if (range.getValue().equals("This Week")) {
                sDate = today.with((DayOfWeek.MONDAY));
                eDate = today.with((DayOfWeek.SUNDAY)).plusDays(1);
            }
            if (range.getValue().equals("This Month")) {
                sDate = today.withDayOfMonth(1);
                eDate = today.withDayOfMonth(today.lengthOfMonth());
            }
            if (range.getValue().equals("This Year")) {
                sDate = today.withDayOfYear(1);
                eDate = today.withDayOfYear(today.lengthOfYear());
            }
            return expenseRepository.findAllByDateBetween(sDate.atStartOfDay(), eDate.atStartOfDay(), pageable);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public Iterable<Expense> findByEmployee(String id, Pageable pageable) {
        try {
            return expenseRepository.findAllByResponsiblePerson(id, pageable);
        } catch (Exception ex) {
            LOGGER.error("Retrieving All Rent failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Expense> findByTypeAndDateBetween(String typeId, LocalDate from, LocalDate to) {
        try {
            return expenseRepository.findAllByTypeAndDateBetween(typeId, from.atStartOfDay(), to.plusDays(1).atStartOfDay());
        } catch (Exception ex) {
            LOGGER.error("Retrieving All Rent failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Expense> findByEmployeeAndDateBetween(String empId, LocalDate from, LocalDate to) {
        try {
            return expenseRepository.findAllByResponsiblePersonAndDateBetween(empId, from.atStartOfDay(), to.plusDays(1).atStartOfDay());
        } catch (Exception ex) {
            LOGGER.error("Retrieving All Rent failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<Expense> findByEmployeeAndType(String empId, String typeId, Pageable pageable) {
        try {
            return expenseRepository.findAllByResponsiblePersonAndType(empId, typeId, pageable);
        } catch (Exception ex) {
            LOGGER.error("Retrieving All Rent failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Expense> findByTypeAndEmployeeAndDateBetween(String typeId, String empId, LocalDate from, LocalDate to) {
        try {
            return expenseRepository.findAllByResponsiblePersonAndTypeAndDateBetween(empId, typeId, from.atStartOfDay(),
                    to.plusDays(1).atStartOfDay());
        } catch (Exception ex) {
            LOGGER.error("Retrieving All Rent failed: " + ex.getMessage());
            return null;
        }
    }
}



