package lk.sout.core.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

import java.lang.annotation.Inherited;

/**
 * Created by <PERSON><PERSON><PERSON> Weera<PERSON> on 12/13/2021
 */

@Document
@Component
public class PrintSetting {

    @Id
    private String id;

    private String salesInvoiceFooter;

    private String jobCardFooter;

    private String customerJobReceiptFooter;

    private String estimateFooter;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSalesInvoiceFooter() {
        return salesInvoiceFooter;
    }

    public void setSalesInvoiceFooter(String salesInvoiceFooter) {
        this.salesInvoiceFooter = salesInvoiceFooter;
    }

    public String getJobCardFooter() {
        return jobCardFooter;
    }

    public void setJobCardFooter(String jobCardFooter) {
        this.jobCardFooter = jobCardFooter;
    }

    public String getCustomerJobReceiptFooter() {
        return customerJobReceiptFooter;
    }

    public void setCustomerJobReceiptFooter(String customerJobReceiptFooter) {
        this.customerJobReceiptFooter = customerJobReceiptFooter;
    }

    public String getEstimateFooter() {
        return estimateFooter;
    }

    public void setEstimateFooter(String estimateFooter) {
        this.estimateFooter = estimateFooter;
    }
}
