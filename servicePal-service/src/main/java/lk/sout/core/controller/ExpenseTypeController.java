package lk.sout.core.controller;

import lk.sout.core.entity.Expense;
import lk.sout.core.entity.ExpenseType;
import lk.sout.core.service.ExpenseTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/expenseType")
public class ExpenseTypeController {

    @Autowired
    ExpenseTypeService expenseTypeService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody ExpenseType expenseType) {
        try {
            return ResponseEntity.ok(expenseTypeService.save(expenseType));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllPage", method = RequestMethod.GET)
    private ResponseEntity<?> findAllPage(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(expenseTypeService.findAll(PageRequest.of(Integer.parseInt(page),
                    Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByName", method = RequestMethod.GET)
    private ResponseEntity<?> findByName(@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(expenseTypeService.findByExpenseTypeNameLike(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByExpenseCategory", method = RequestMethod.GET)
    private ResponseEntity<?> findAllByExpenseCategory(@RequestParam("categoryId") String categoryId) {
        try {
            return ResponseEntity.ok(expenseTypeService.findByExpenseCategory(categoryId));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

}

