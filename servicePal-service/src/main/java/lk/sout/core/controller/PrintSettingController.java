package lk.sout.core.controller;

import lk.sout.core.entity.PrintSetting;
import lk.sout.core.entity.UserRole;
import lk.sout.core.service.PrintSettingService;
import lk.sout.core.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/printSetting")
public class PrintSettingController {

    @Autowired
    private PrintSettingService printSettingService;

    @RequestMapping(value = "/findAll", method = RequestMethod.GET)
    public PrintSetting listRole() {
        return printSettingService.findAll();
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody PrintSetting printSetting) {
        try {
            return ResponseEntity.ok(printSettingService.save(printSetting));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

}

