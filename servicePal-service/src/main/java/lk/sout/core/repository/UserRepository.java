package lk.sout.core.repository;

import lk.sout.core.entity.User;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserRepository extends MongoRepository<User, String> {

    User findByUsername(String username);

    List<User> findAllByActive(boolean active);

    User findUserById(String id);

}
