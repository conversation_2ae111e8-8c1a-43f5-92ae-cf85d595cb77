package lk.sout.core.repository;

import lk.sout.core.entity.Module;
import lk.sout.core.entity.Permission;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 2/8/2019
 */

@Repository
public interface PermissionRepository extends MongoRepository<Permission, String> {

    Permission findByRoute(String route);

    List<Permission> findByModule(Module module);

    Permission findByName(String permName);
}
