package lk.sout.core.repository;

import lk.sout.core.entity.Expense;
import lk.sout.core.entity.PrintSetting;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface PrintSettingRepository extends MongoRepository<PrintSetting, String> {
}

