package lk.sout.config;

import lk.sout.core.entity.*;
import lk.sout.core.entity.Module;
import lk.sout.core.repository.*;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.PermissionService;
import lk.sout.servicePal.business.entity.Commission;
import lk.sout.servicePal.business.entity.Job;
import lk.sout.servicePal.business.entity.JobEstimateRecord;
import lk.sout.servicePal.business.entity.ShowRoom;
import lk.sout.servicePal.business.repository.CommissionRepository;
import lk.sout.servicePal.business.repository.JobRepository;
import lk.sout.servicePal.business.repository.ShowRoomRepository;
import lk.sout.servicePal.inventory.entity.*;
import lk.sout.servicePal.inventory.repository.*;
import lk.sout.servicePal.trade.entity.*;
import lk.sout.servicePal.trade.repository.*;
import lk.sout.servicePal.trade.service.SalesInvoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Configuration
@ComponentScan({"lk.sout.core.service", "lk.sout.servicePal.inventory.service"})
public class InitDataRunner implements CommandLineRunner {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserRoleRepository userRoleRepository;

    BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();

    @Autowired
    PermissionService permissionService;

    @Autowired
    CounterRepository counterRepository;

    @Autowired
    CompanyRepository companyRepository;

    @Autowired
    ItemRepository itemRepository;

    @Autowired
    StockRepository stockRepository;

    @Autowired
    UserGroupRepository userGroupRepository;

    @Autowired
    SequenceRepository seqRepository;

    @Autowired
    ModuleRepository moduleRepository;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    ItemTypeRepository itemTypeRepository;

    @Autowired
    UOMRepository uomRepository;

    @Autowired
    JobRepository jobRepository;

    @Autowired
    WarehouseRepository warehouseRepository;

    @Autowired
    CustomerRepository customerRepository;

    @Autowired
    UOM uom;

    @Autowired
    ExpenseRepository expenseRepository;

    @Autowired
    SalesInvoiceRepository salesInvoiceRepository;

    @Autowired
    SalesInvoiceService salesInvoiceService;

    @Autowired
    ActionRepository actionRepository;

    @Autowired
    TransactionRepository transactionRepository;

    @Autowired
    PurchaseInvoiceRepository purchaseInvoiceRepository;

    @Autowired
    PurchaseInvoiceRecordRepository purchaseInvoiceRecordRepository;

    @Autowired
    SalesInvoiceRecordRepository salesInvoiceRecordRepository;

    @Autowired
    CommissionRepository commissionRepository;

    @Autowired
    ShowRoomRepository showRoomRepository;

    @Autowired
    CashierRepository cashierRepository;

    @Autowired
    BrandRepository brandRepository;

    @Autowired
    ItemCategoryRepository itemCategoryRepository;

    @Autowired
    ItemSubCategoryRepository subCategoryRepository;

    @Override
    public void run(String... strings) throws Exception {
        try {
            if (userRoleRepository.count() < 1) {
                List<UserRole> userRoles = new ArrayList<>();
                for (RoleName autho : RoleName.values()) {
                    UserRole userRole = new UserRole();
                    userRole.setName(autho);
                    userRoles.add(userRole);
                }
                userRoleRepository.saveAll(userRoles);
            }

            if (itemTypeRepository.count() < 1) {

                ItemType itemType = new ItemType();
                itemType.setName("Service");
                itemType.setActive(true);
                itemTypeRepository.save(itemType);

                ItemType itemType1 = new ItemType();
                itemType1.setName("Transport");
                itemType1.setActive(true);
                itemTypeRepository.save(itemType1);

                ItemType itemType3 = new ItemType();
                itemType3.setName("Item");
                itemType3.setActive(true);
                itemTypeRepository.save(itemType3);

                ItemType itemType4 = new ItemType();
                itemType4.setName("Spare part");
                itemType4.setActive(true);
                itemTypeRepository.save(itemType4);
            }

            if (companyRepository.count() < 1) {
                Company thisBusiness = new Company();
                thisBusiness.setLogo("");
                thisBusiness.setName("");
                thisBusiness.setSlogan("");
                companyRepository.save(thisBusiness);
            }

            if (warehouseRepository.count() < 1) {
                Warehouse warehouse = new Warehouse();
                warehouse.setName("Main Store");
                warehouse.setCode(0);
                //this is not active cause we dont need to see this in search
                warehouse.setActive(false);
                warehouseRepository.save(warehouse);
            }

            if (moduleRepository.count() < 1) {
                Module admin = new Module("Admin", "Admin", true);
                moduleRepository.save(admin);

                Module hr = new Module("HR", "HR", true);
                moduleRepository.save(hr);

                Module job = new Module("Job", "Job", true);
                moduleRepository.save(job);

                Module inventory = new Module("Inventory", "Inventory", true);
                moduleRepository.save(inventory);

                Module trade = new Module("Trade", "Trade", true);
                moduleRepository.save(trade);

                Module machine = new Module("Machine", "Machine", true);
                moduleRepository.save(machine);

                Module report = new Module("Report", "Report", true);
                moduleRepository.save(report);
            }

            Module admin = moduleRepository.findByName("Admin");
            Permission newUser = new Permission("Create User", "admin/new_user", true, "fa fa-user-plus", admin);
            Permission manageUser = new Permission("User Management", "admin/manage_users", true, "fa fa-users", admin);
            Permission managePermission = new Permission("User Permissions", "admin/user_permissions", true, "fa fa-key", admin);
            Permission companyDetails = new Permission("Setup Company", "admin/company_detail", true, "fa fa-building", admin);
            Permission printSettings = new Permission("Print Settings", "admin/print_settings", true, "fa fa-print", admin);
            Permission setupCounters = new Permission("Setup Counter", "admin/setupCounter", true, "fas fa-cash-register", admin);

            permissionService.saveIfUnavailable(newUser);
            permissionService.saveIfUnavailable(manageUser);
            permissionService.saveIfUnavailable(managePermission);
            permissionService.saveIfUnavailable(companyDetails);
            permissionService.saveIfUnavailable(printSettings);
            permissionService.saveIfUnavailable(setupCounters);

            Module inventory = moduleRepository.findByName("Inventory");
            Permission manualStockIn = new Permission("Manual Stock In", "inventory/manual_stock_in", true, "fa fa-database", inventory);
            Permission stockDetails = new Permission("Item Details", "inventory/item_details", true, "fa fa-info-circle", inventory);
            Permission addItem = new Permission("Create Item", "inventory/create_item", true, "fa fa-cart-plus", inventory);
            Permission brand = new Permission("Brands", "inventory/brand", true, "fa fa-tag", inventory);
            Permission uomPerm = new Permission("Units of Measure", "inventory/uom", true, "fa fa-balance-scale", inventory);
            Permission itemType = new Permission("Item Types", "inventory/item_type", true, "fa fa-cubes", inventory);
            Permission itemCategory = new Permission("Item Category", "inventory/item_category", true, "fa fa-sitemap", inventory);
            Permission itemSubCategory = new Permission("Item Subcategory", "inventory/sub_category", true, "fa fa-layer-group", inventory);
            Permission viewMainStock = new Permission("Main Stock", "inventory/view_main_stock", true, "fa fa-warehouse", inventory);
            Permission manageWarehouse = new Permission("Manage Warehouse", "inventory/manage_warehouse", true, "fa fa-industry", inventory);
            Permission transferStock = new Permission("Stock Transfer", "inventory/stock_transfer", true, "fa fa-exchange-alt", inventory);
            Permission batchStockTransfer = new Permission("Stock Transfer", "inventory/batch-stock-transfer", true, "fa fa-truck-loading", inventory);
            Permission rack = new Permission("Manage Racks", "inventory/rack", true, "fa fa-th", inventory);

            permissionService.saveIfUnavailable(addItem);
            permissionService.saveIfUnavailable(manualStockIn);
            permissionService.saveIfUnavailable(stockDetails);
            permissionService.saveIfUnavailable(brand);
            permissionService.saveIfUnavailable(uomPerm);
            permissionService.saveIfUnavailable(itemType);
            permissionService.saveIfUnavailable(itemCategory);
            permissionService.saveIfUnavailable(itemSubCategory);
            permissionService.saveIfUnavailable(viewMainStock);
            permissionService.saveIfUnavailable(batchStockTransfer);
            permissionService.saveIfUnavailable(manageWarehouse);
            permissionService.saveIfUnavailable(transferStock);
            permissionService.saveIfUnavailable(rack);

            Module hr = moduleRepository.findByName("HR");
            Permission leaveAdd = new Permission("Leave Types", "hr/leave_types", true, "fa fa-calendar-plus", hr);
            Permission manageLeaves = new Permission("Manage Leaves", "hr/manage_leaves", true, "fa fa-calendar-check", hr);
            Permission leaveRequest = new Permission("Requesting Leaves", "hr/leave_request", true, "fa fa-calendar-alt", hr);
            Permission employeeHierarchy = new Permission("Employee Hierarchy", "hr/employee_hierarchy", true, "fa fa-sitemap", hr);
            Permission employee = new Permission("Employee", "hr/employee", true, "fa fa-user-plus", hr);
            Permission manageEmployee = new Permission("Manage Employee", "hr/manage_employee", true, "fa fa-users-cog", hr);
            Permission department = new Permission("Department", "hr/department", true, "fa fa-building", hr);
            Permission designation = new Permission("Designation", "hr/designation", true, "fa fa-id-badge", hr);
            Permission manageSalaryScale = new Permission("Salary Scale", "hr/manage_salary_scale", true, "fa fa-money-bill-alt", hr);

            permissionService.saveIfUnavailable(employee);
            permissionService.saveIfUnavailable(manageEmployee);
            permissionService.saveIfUnavailable(department);
            permissionService.saveIfUnavailable(designation);
            permissionService.saveIfUnavailable(manageSalaryScale);
            permissionService.saveIfUnavailable(leaveAdd);
            permissionService.saveIfUnavailable(manageLeaves);
            permissionService.saveIfUnavailable(leaveRequest);
            permissionService.saveIfUnavailable(employeeHierarchy);

            Module job = moduleRepository.findByName("Job");
            Permission newJob = new Permission("New Job", "job/new_job", true, "fa fa-plus-circle", job);
            Permission newJobs = new Permission("New Jobs", "job/new_jobs", true, "fa fa-clipboard-list", job);
            Permission jobsInProgress = new Permission("Jobs in Progress", "job/jobs_in_progress", true, "fa fa-spinner", job);
            Permission completedJobs = new Permission("Completed Jobs", "job/completed_jobs", true, "fa fa-check-circle", job);
            Permission manageJobs = new Permission("Manage Jobs", "job/manage_jobs", true, "fa fa-tasks", job);

            permissionService.saveIfUnavailable(newJob);
            permissionService.saveIfUnavailable(newJobs);
            permissionService.saveIfUnavailable(jobsInProgress);
            permissionService.saveIfUnavailable(completedJobs);
            permissionService.saveIfUnavailable(manageJobs);

            Module machine = moduleRepository.findByName("Machine");
            Permission newMachine = new Permission("New Machine", "machine/new_machine", true, "fa fa-cogs", machine);
            Permission manageMachine = new Permission("Manage Machines", "machine/manage_machine", true, "fa fa-wrench", machine);
            Permission machineCategory = new Permission("Machine Category", "machine/machine_category", true, "fa fa-list-alt", machine);
            Permission agreements = new Permission("Maintenance Agreements", "machine/maintenance_agreements", true, "fa fa-file-contract", machine);

            permissionService.saveIfUnavailable(newMachine);
            permissionService.saveIfUnavailable(manageMachine);
            permissionService.saveIfUnavailable(machineCategory);
            permissionService.saveIfUnavailable(agreements);

            Module trade = moduleRepository.findByName("Trade");
            Permission newPurchaseInv = new Permission("New Purchase Invoice", "trade/new_purchase_invoice", true, "fa fa-shopping-cart", trade);
            Permission managePurchaseInv = new Permission("Manage Purchase Invoice", "trade/manage_purchase_invoices", true, "fa fa-file-invoice", trade);
            Permission newSalesInv = new Permission("New Sales Invoice", "trade/new_sales_invoice", true, "fa fa-receipt", trade);
            Permission manageSalesInv = new Permission("Manage Sales Invoice", "trade/manage_sales_invoices", true, "fa fa-file-alt", trade);
            Permission newCustomer = new Permission("New Customer", "trade/new_customer", true, "fa fa-user-plus", trade);
            Permission manageCustomer = new Permission("Manage Customer", "trade/manage_customer", true, "fa fa-users", trade);
            Permission manageSupplier = new Permission("Manage Supplier", "trade/manage_supplier", true, "fa fa-truck", trade);
            Permission expenseType = new Permission("Expense Type", "trade/expense_type", true, "fa fa-tags", trade);
            Permission newExpense = new Permission("New Expense", "trade/new_expense", true, "fa fa-money-bill", trade);
            Permission cashierPerm = new Permission("Cashier", "trade/cashier", true, "fas fa-cash-register", trade);

            permissionService.saveIfUnavailable(newPurchaseInv);
            permissionService.saveIfUnavailable(managePurchaseInv);
            permissionService.saveIfUnavailable(newSalesInv);
            permissionService.saveIfUnavailable(manageSalesInv);
            permissionService.saveIfUnavailable(newCustomer);
            permissionService.saveIfUnavailable(manageCustomer);
            permissionService.saveIfUnavailable(manageSupplier);
            permissionService.saveIfUnavailable(expenseType);
            permissionService.saveIfUnavailable(newExpense);
            permissionService.saveIfUnavailable(cashierPerm);

            Module report = moduleRepository.findByName("Report");
            Permission stockValueReport = new Permission("Stock Report", "report/stock_report", true, "fa fa-boxes", report);
            Permission reorderReport = new Permission("Reorder Report", "report/reorder_report", true, "fa fa-sync", report);
            Permission creditReport = new Permission("Credit Report", "report/credit_report", true, "fa fa-credit-card", report);
            Permission empCommissionReport = new Permission("Employee Commission Report", "report/emp_com_report", true, "fa fa-percentage", report);
            Permission salesReport = new Permission("Income Report", "report/income_report", true, "fa fa-chart-line", report);
            Permission profitReport = new Permission("Profit Report", "report/profit_report", true, "fa fa-chart-bar", report);
            Permission expenseReport = new Permission("Expense Report", "report/expense_report", true, "fa fa-chart-pie", report);
            Permission cashInOutReport = new Permission("Cash In Out Report", "report/cash_in_out_report", true, "fa fa-exchange-alt", report);
            Permission cashierSummaryReport = new Permission("Cashier Summary Report", "report/cashier_summary_report", true, "fa fa-cash-register", report);
            Permission stockMovementReport = new Permission("Stock Movement Report", "report/stock_movement_report", true, "fa fa-truck-moving", report);
            //Permission summaryReport = new Permission("Summary Report", "report/summary_report", true, "fa fa-file-alt", report);
            //Permission cashFlow = new Permission("Cash Flow", "report/cash_flow", true, "fa fa-money-bill-wave", report);

            permissionService.saveIfUnavailable(stockValueReport);
            permissionService.saveIfUnavailable(reorderReport);
            permissionService.saveIfUnavailable(creditReport);
            permissionService.saveIfUnavailable(empCommissionReport);
            permissionService.saveIfUnavailable(salesReport);
            permissionService.saveIfUnavailable(profitReport);
            permissionService.saveIfUnavailable(expenseReport);
            permissionService.saveIfUnavailable(cashierSummaryReport);
            permissionService.saveIfUnavailable(cashInOutReport);
            permissionService.saveIfUnavailable(stockMovementReport);
            //permissionService.saveIfUnavailable(summaryReport);
            //permissionService.saveIfUnavailable(cashFlow);

            if (counterRepository.count() < 1) {
                Counter counter = new Counter();
                counter.setCounterNo("01");
                counterRepository.save(counter);
            }

            if (userRepository.findByUsername("admin") == null) {
                User user = new User();
                user.setUserRoles(userRoleRepository.findAll());
                user.setEmail("<EMAIL>");
                user.setActive(true);
                user.setFirstName("admin");
                user.setLastName("admin");
                user.setUsername("admin");
                user.setCounter(counterRepository.findByCounterNo("01").getCounterNo());
                user.setPermissions(permissionService.findAll());
                user.setPassword(bCryptPasswordEncoder.encode("admin"));
                userRepository.save(user);
            }

            if (null == cashierRepository.findCashierByCounter("01")) {
                Cashier cashier = new Cashier();
                cashier.setCounter("01");
                cashier.setCurrentBalance(0.0);
                cashier.setOpeningBalance(0.0);
                cashier.setActive(true);
                cashier.setLastClosedDate(LocalDate.now().minusDays(1));
                cashier.setLastStartedDate(LocalDate.now());
                cashierRepository.save(cashier);
            }

            if (null == itemRepository.findByItemCode("0")) {
                Item item = new Item();
                item.setBarcode(Constant.NO_STOCK_BARCODE);
                item.setItemCode("0");
                item.setManageStock(true);
                item.setItemCost(0.0);
                item.setSellingPrice(0.0);
                item.setItemName("Item");
                item.setActive(true);
                item.setUom(uomRepository.findByName("Nos"));
                itemRepository.save(item);
            }

            if (null == stockRepository.findByItemCode("0")) {
                Stock stock = new Stock();
                stock.setBarcode(Constant.NO_STOCK_BARCODE);
                stock.setItemCode("0");
                stock.setItemCost(0.0);
                stock.setSellingPrice(0.0);
                stock.setQuantity(10000.0);
                stock.setItemName("Item");
                stock.setActive(true);
                stockRepository.save(stock);
            }

            if (seqRepository.count() < 1) {
                Sequence sequence = new Sequence();
                sequence.setCounter(0);
                sequence.setName("SalesInvoice");
                sequence.setPrefix("sinv");
                seqRepository.save(sequence);

                Sequence sequence1 = new Sequence();
                sequence1.setCounter(0);
                sequence1.setName("PurchaseInvoice");
                sequence1.setPrefix("pinv");
                seqRepository.save(sequence1);

                Sequence jobSeq = new Sequence();
                jobSeq.setId(null);
                jobSeq.setName("Job No");
                jobSeq.setCounter(0);
                jobSeq.setPrefix("Job");
                seqRepository.save(jobSeq);
            }

            if (uomRepository.count() < 1) {
                uom.setName("Nos");
                uom.setSymbol("pieces");
                uom.setIsWholeNumber(true);
                uom.setActive(true);
                uomRepository.save(uom);
            }

            if (customerRepository.count() < 1) {
                Customer customer = new Customer();
                customer.setNicBr("default id");
                customer.setName("Default Customer");
                customer.setAddress("Default");
                customer.setEmail("<EMAIL>");
                customer.setTelephone1("000000000");
                customer.setBalance(0.0);
                customer.setCreditLimit(0.0);
                customer.setNote("");
                customer.setActive(true);
                customerRepository.save(customer);
            }

            // saving meta data
            MetaData gender1 = new MetaData("Gender", "Male", "Male");
            metaDataService.saveIfUnavailable(gender1);

            MetaData gender2 = new MetaData("Gender", "Female", "Female");
            metaDataService.saveIfUnavailable(gender2);

            MetaData personType1 = new MetaData("Person Type", "Customer", "Customer");
            metaDataService.saveIfUnavailable(personType1);

            MetaData personType2 = new MetaData("Person Type", "Supplier", "Supplier");
            metaDataService.saveIfUnavailable(personType2);

            MetaData personType3 = new MetaData("PersonType", "dealer", "dealer");
            metaDataService.saveIfUnavailable(personType3);

            MetaData personType4 = new MetaData("PersonType", "employee", "employee");
            metaDataService.saveIfUnavailable(personType4);

            MetaData jobStatusCreated = new MetaData("Job Status", "type1", "Job Created");
            metaDataService.saveIfUnavailable(jobStatusCreated);

            MetaData jobStatusAssigned = new MetaData("Job Status", "type2", "Job Assigned");
            metaDataService.saveIfUnavailable(jobStatusAssigned);

            MetaData jobStatusEstimated = new MetaData("Job Status", "type3", "Job Estimated");
            metaDataService.saveIfUnavailable(jobStatusEstimated);

            MetaData jobStatusInProgress = new MetaData("Job Status", "type4", "Job In Progress");
            metaDataService.saveIfUnavailable(jobStatusInProgress);

            MetaData jobStatusCancelled = new MetaData("Job Status", "type5", "Job Cancelled");
            metaDataService.saveIfUnavailable(jobStatusCancelled);

            MetaData jobStatusCompleted = new MetaData("Job Status", "type6", "Job Completed");
            metaDataService.saveIfUnavailable(jobStatusCompleted);

            MetaData jobClosed = new MetaData("Job Status", "type8", "Job Closed");
            metaDataService.saveIfUnavailable(jobClosed);

            MetaData actionManualStockAdjust = new MetaData("Action", "Adjust Stock", "Adjust Stock");
            metaDataService.saveIfUnavailable(actionManualStockAdjust);

            MetaData incomeSi = new MetaData("Income", "SalesInvoice", "SalesInvoice");
            metaDataService.saveIfUnavailable(incomeSi);

            MetaData incomeAdvance = new MetaData("Income", "Advance Payment", "Advance Payment");
            metaDataService.saveIfUnavailable(incomeAdvance);

            MetaData expenseReturn1 = new MetaData("Income", "ReturnStock", "ReturnStock");
            metaDataService.saveIfUnavailable(expenseReturn1);

            MetaData expensePi = new MetaData("Expense", "PurchaseInvoice", "PurchaseInvoice");
            metaDataService.saveIfUnavailable(expensePi);

            MetaData expenseReturn = new MetaData("Expense", "ReturnStock", "ReturnStock");
            metaDataService.saveIfUnavailable(expenseReturn);

            MetaData expenseOther = new MetaData("Expense", "Other", "Other");
            metaDataService.saveIfUnavailable(expenseOther);

            MetaData paymentMethod1 = new MetaData("PaymentMethod", "PaymentMethod", "Cash");
            metaDataService.saveIfUnavailable(paymentMethod1);

            MetaData paymentMethod2 = new MetaData("PaymentMethod", "PaymentMethod", "Cheque");
            metaDataService.saveIfUnavailable(paymentMethod2);

            MetaData paymentMethod3 = new MetaData("PaymentMethod", "PaymentMethod", "Card");
            metaDataService.saveIfUnavailable(paymentMethod3);

            MetaData paymentMethod4 = new MetaData("PaymentMethod", "PaymentMethod", "Bank");
            metaDataService.saveIfUnavailable(paymentMethod4);

            MetaData paymentStatusType1 = new MetaData("PaymentStatus", "type1", "Pending");
            metaDataService.saveIfUnavailable(paymentStatusType1);

            MetaData paymentStatusType2 = new MetaData("PaymentStatus", "type1", "Partially Paid");
            metaDataService.saveIfUnavailable(paymentStatusType2);

            MetaData paymentStatusType3 = new MetaData("PaymentStatus", "type1", "Paid");
            metaDataService.saveIfUnavailable(paymentStatusType3);

            MetaData paymentStatusType4 = new MetaData("PaymentStatus", "type1", "Cancelled");
            metaDataService.saveIfUnavailable(paymentStatusType4);

            MetaData durationReportFilter1 = new MetaData("ReportFilterDuration", "Daily", "Today");
            metaDataService.saveIfUnavailable(durationReportFilter1);

            MetaData durationReportFilter2 = new MetaData("ReportFilterDuration", "Weekly", "This Week");
            metaDataService.saveIfUnavailable(durationReportFilter2);

            MetaData durationReportFilter3 = new MetaData("ReportFilterDuration", "Monthly", "This Month");
            metaDataService.saveIfUnavailable(durationReportFilter3);

            MetaData durationReportFilter4 = new MetaData("ReportFilterDuration", "Yearly", "This Year");
            metaDataService.saveIfUnavailable(durationReportFilter4);

            MetaData expenseCategory1 = new MetaData("ExpenseCategory", "Fuel", "Fuel");
            metaDataService.saveIfUnavailable(expenseCategory1);

            MetaData expenseCategory2 = new MetaData("ExpenseCategory", "Vehicle", "Vehicle");
            metaDataService.saveIfUnavailable(expenseCategory2);

            MetaData expenseCategory3 = new MetaData("ExpenseCategory", "Bill", "Bill");
            metaDataService.saveIfUnavailable(expenseCategory3);

            MetaData expenseCategory4 = new MetaData("ExpenseCategory", "Stationary", "Stationary");
            metaDataService.saveIfUnavailable(expenseCategory4);

            MetaData expenseCategory5 = new MetaData("ExpenseCategory", "Common Items", "Common Items");
            metaDataService.saveIfUnavailable(expenseCategory5);

            MetaData expenseCategory6 = new MetaData("ExpenseCategory", "Food", "Food");
            metaDataService.saveIfUnavailable(expenseCategory6);

            MetaData expenseCategory7 = new MetaData("ExpenseCategory", "Other", "Other");
            metaDataService.saveIfUnavailable(expenseCategory7);

            MetaData bank1 = new MetaData("Bank", "Bank", "Sampath Bank");
            metaDataService.saveIfUnavailable(bank1);

            MetaData bank2 = new MetaData("Bank", "Bank", "Commercial Bank");
            metaDataService.saveIfUnavailable(bank2);

            MetaData bank3 = new MetaData("Bank", "Bank", "Bank of Ceylon");
            metaDataService.saveIfUnavailable(bank3);

            MetaData bank4 = new MetaData("Bank", "Bank", "People's Bank");
            metaDataService.saveIfUnavailable(bank4);

            MetaData bank5 = new MetaData("Bank", "Bank", "Hatton National Bank");
            metaDataService.saveIfUnavailable(bank5);

            MetaData bank6 = new MetaData("Bank", "Bank", "Seylan Bank");
            metaDataService.saveIfUnavailable(bank6);

            MetaData bank7 = new MetaData("Bank", "Bank", "Cargills Bank");
            metaDataService.saveIfUnavailable(bank7);

            MetaData bank8 = new MetaData("Bank", "Bank", "DFCC Bank");
            metaDataService.saveIfUnavailable(bank8);

            MetaData bank9 = new MetaData("Bank", "Bank", "Nations Trust Bank");
            metaDataService.saveIfUnavailable(bank9);

            MetaData bank10 = new MetaData("Bank", "Bank", "HSBC");
            metaDataService.saveIfUnavailable(bank10);

            MetaData cashOutPurpose = new MetaData("CashOutPurpose", "Banking", "Banking");
            metaDataService.saveIfUnavailable(cashOutPurpose);

            MetaData cashOutPurpose2 = new MetaData("CashOutPurpose", "Purchasing", "Purchasing");
            metaDataService.saveIfUnavailable(cashOutPurpose2);

            MetaData cashOutPurpose3 = new MetaData("CashOutPurpose", "Cash", "Cash");
            metaDataService.saveIfUnavailable(cashOutPurpose3);

            MetaData cashOutPurpose4 = new MetaData("CashOutPurpose", "Day End", "Day End");
            metaDataService.saveIfUnavailable(cashOutPurpose4);

            MetaData cashOutPurpose5 = new MetaData("CashOutPurpose", "Unspecified", "Unspecified");
            metaDataService.saveIfUnavailable(cashOutPurpose5);

            MetaData cashInPurpose = new MetaData("CashInPurpose", "Day Start", "Day Start");
            metaDataService.saveIfUnavailable(cashInPurpose);

            MetaData cashInPurpose2 = new MetaData("CashInPurpose", "Adding Changes", "Adding Changes");
            metaDataService.saveIfUnavailable(cashInPurpose2);

            MetaData cashIn = new MetaData("Cash", "Cash In", "Cash In");
            metaDataService.saveIfUnavailable(cashIn);

            MetaData cashOut = new MetaData("Cash", "Cash Out", "Cash Out");
            metaDataService.saveIfUnavailable(cashOut);

            MetaData saleType1 = new MetaData("SaleType", "Sale", "Sale");
            metaDataService.saveIfUnavailable(saleType1);

            MetaData saleType2 = new MetaData("SaleType", "Return", "Return");
            metaDataService.saveIfUnavailable(saleType2);

            if (expenseRepository.count() < 1) {
                Expense expense = new Expense();
                Expense saved = expenseRepository.save(expense);
                expenseRepository.delete(saved);
            }

            if (actionRepository.count() < 1) {
                Action action = new Action();
                Action ac = actionRepository.save(action);
                actionRepository.delete(ac);
            }

            if (purchaseInvoiceRepository.count() < 1) {
                PurchaseInvoice purchaseInvoice = new PurchaseInvoice();
                PurchaseInvoiceRecord purchaseInvoiceRecord = new PurchaseInvoiceRecord();
                List<PurchaseInvoiceRecord> records = new ArrayList<>();
                records.add(purchaseInvoiceRecord);
                purchaseInvoice.setPurchaseInvoiceRecords(records);
                PurchaseInvoice invoice = purchaseInvoiceRepository.save(purchaseInvoice);
                purchaseInvoiceRepository.delete(invoice);
                purchaseInvoiceRecordRepository.delete(purchaseInvoiceRecord);
            }

            if (salesInvoiceRepository.count() < 1) {
                SalesInvoice salesInvoice = new SalesInvoice();
                SalesInvoiceRecord salesInvoiceRecord = new SalesInvoiceRecord();
                List<SalesInvoiceRecord> records = new ArrayList<>();
                records.add(salesInvoiceRecord);
                salesInvoice.setSalesInvoiceRecords(records);
                SalesInvoice invoice = salesInvoiceRepository.save(salesInvoice);
                salesInvoiceRepository.delete(invoice);
                salesInvoiceRecordRepository.delete(salesInvoiceRecord);
            }

            if (transactionRepository.count() < 1) {
                Transaction transaction = transactionRepository.save(new Transaction());
                transactionRepository.delete(transaction);
            }

            if (commissionRepository.count() < 1) {
                Commission commission = new Commission();
                Commission commission1 = commissionRepository.save(commission);
                commissionRepository.delete(commission1);
            }

            if (showRoomRepository.count() < 1) {
                ShowRoom showRoom1 = new ShowRoom();
                showRoom1.setName("Show1");
                showRoomRepository.save(showRoom1);

                ShowRoom showRoom2 = new ShowRoom();
                showRoom2.setName("Show2");
                showRoomRepository.save(showRoom2);
            }

           /* brandRepository.findAll().forEach(brand1 -> {
                brand1.setCode(String.valueOf(System.currentTimeMillis()));
                brandRepository.save(brand1);
            });

            itemCategoryRepository.findAll().forEach(itemCategory1 -> {
                itemCategory1.setCode(String.valueOf(System.currentTimeMillis()));
                itemCategoryRepository.save(itemCategory1);
            });

            subCategoryRepository.findAll().forEach(subCategory1 -> {
                subCategory1.setCode(String.valueOf(System.currentTimeMillis()));
                subCategoryRepository.save(subCategory1);
            });

            List<Warehouse> warehouses = warehouseRepository.findAll();
            AtomicInteger i = new AtomicInteger(0);
            warehouses.forEach(wh -> wh.setCode(i.getAndIncrement()));
            warehouseRepository.saveAll(warehouses);

            itemRepository.findAll().forEach(item -> {
                Stock stock = new Stock();
                stock.setActive(true);
                stock.setBarcode(item.getBarcode());
                stock.setItemCode(item.getItemCode());
                stock.setBrandCode(item.getBrand() != null ? item.getBrand().getCode() : "N/A");
                stock.setDeadStockLevel(item.getDeadStockLevel());
                stock.setItemCost(item.getItemCost());
                stock.setItemName(item.getItemName());
                stock.setSellingPrice(item.getSellingPrice());
                stock.setWarehouseName("Main Stock");
                stock.setWarehouseCode(0);
                stock.setQuantity(item.getQuantity());
                stock.setCategoryCode(item.getItemCategory() != null ? item.getItemCategory().getCode() : "N/A");
                stock.setSubStockCode(item.getSubCategory() != null ? item.getSubCategory().getCode() : "N/A");
                stockRepository.save(stock);
                System.out.println(stock.getId());
            });

            System.out.println("Done");*/

        } catch (Exception ex) {
            ex.printStackTrace();
        }

    }

}
